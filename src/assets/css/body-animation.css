body {
  display: none;
  opacity: 0;
}

.rounded-element {
  border-radius: 0.5rem;
  box-shadow: 1px -1px 19px -3px rgba(0, 0, 0, 0.1);
}

.dynamic-shadow {
  position: relative;
}

.dynamic-shadow:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background: inherit;
  top: 0.5rem;
  filter: blur(0.4rem);
  opacity: 0.7;
  z-index: -1;
}
.post-images {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  align-content: center;
  align-items: center;
  justify-content: center;
  justify-items: center;
  gap: 1rem;
}

.post-image {
  max-width: 100%;
  max-height: 400px;
  margin: 8px;
  border-radius: 0.5rem;
  box-shadow: 1px -1px 19px -3px rgba(0, 0, 0, 0.1);
}
.image-upload-container {
  position: relative;
  overflow: hidden;
  display: inline-block;
  cursor: pointer;
}

.image-upload-input {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 2;
}

.image-upload-label {
  display: inline-block;
  padding: 10px 20px;
  background: #0073e6;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
  z-index: 1;
}

.image-upload-label:hover {
  background: #005bb8;
}

.bg-gray-300 {
  background-color: rgb(209 213 219);
}

.bg-gray-50 {
  background-color: rgb(249 250 251);
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.space-x-2 {
  margin-left: 0.5rem;
}

.overflow-x-auto {
  overflow-x: auto;
}

.thumbnail-list {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 1rem;
}

.gallery-image {
  max-height: 400px;
}

@media screen and (max-width: 1024px) {
  .gallery-image {
    max-height: 200px;
  }
}
