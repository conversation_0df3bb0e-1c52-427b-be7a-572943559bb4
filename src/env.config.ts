import * as dotenv from 'dotenv';
import { createEnv } from '@t3-oss/env-core';
import { z } from 'zod';

dotenv.config();
export const checkEnVariables = async (env: Record<string, string>) => {
  const stage = env.VITE_ENV;
  if (stage === 'dev') {
    // eslint-disable-next-line no-console
    console.log(`Skipped env check for ${stage} stage`);

    return;
  }

  return createEnv({
    clientPrefix: '',
    client: {
      AWS_REGION: z.string().min(5),
      AWS_S3_BUCKET_NAME: z.string().min(5),
      AWS_ACCESS_KEY_ID: z.string().min(10),
      AWS_SECRET_ACCESS_KEY: z.string().min(10),
    },
    runtimeEnv: env,
    emptyStringAsUndefined: true,
  });
};
