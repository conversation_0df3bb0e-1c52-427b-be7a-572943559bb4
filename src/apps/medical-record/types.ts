export type UserUpdatedData = {
  id: string;
  email: string;
  name: string;
  phone: number;
  identification: string;
  userType: string;
};
export type ProfilePictureEditProps<T> = {
  context: T;
};
export type FileUploaderProps = {
  id: string;
  isRequired?: boolean;
};
export type ProfilePasswordEditProps<T> = {
  context: T;
};
export type ProfileEditDataProps<T> = {
  context: T;
};
export type ProfileDataContainerProps<T> = {
  context: T;
};
export type UserUpdatedProfilePicture = {
  userId: string;
  profilePicture: string;
  name: string;
  size: number;
  type: string;
};
export type ViewType = 'DETAIL' | 'FORM';
export type PasswordFormProps = {
  newPassword: string;
  newPasswordRetry: string;
};
export type UserUpdatedPassword = {
  userId: string;
  password: string;
};
