import { Accessor } from 'solid-js';
import userDefaultPicture from '../../../assets/media/icons/blank.png';
import { User } from '../../../entities/User';

const UserRecordHeader = (props: { user: Accessor<User> }) => {
  const handleImageLoadError = (event: Event) => {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = userDefaultPicture;
  };

  return (
    <>
      <div class="card mb-5">
        <div class="card-body py-4">
          <div class="d-flex flex-center flex-column mt-4">
            <div class="symbol symbol-100px symbol-lg-150px symbol-circle mb-7 symbol-fixed position-relative">
              <img
                class="object-fit-cover"
                src={props.user().profilePicture}
                alt="profile-picture"
                onError={handleImageLoadError}
              />
            </div>
            <a
              href="#"
              class="fs-3 text-gray-800 text-hover-primary fw-bold mb-1"
            >
              {props.user().name}
            </a>
            <a
              href="#"
              class="fs-5 fw-semibold text-muted text-hover-primary mb-3"
            >
              {props.user().email}
            </a>
          </div>
        </div>
      </div>
    </>
  );
};
export default UserRecordHeader;
