import { UserHealthData } from 'entities/UserMedicalDataReport';

export const DiagnosisReport = ({ data }: { data: UserHealthData }) => {
  const diagnosisLibrary = [
    'abdomenUltrasound',
    'breastUltrasound',
    'conventionalPapSmear',
    'diagnosis',
    'electrocardiogram',
    'referredSpecialty',
  ];
  if (!diagnosisLibrary.includes(data.id)) return <></>;

  return (
    <div class="my-3">
      <div class="d-flex flex-column gap-1">
        <div class="fw-bold text-muted">{data.title}</div>
        <p>{data.lastValue}</p>
      </div>
    </div>
  );
};
