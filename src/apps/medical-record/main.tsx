import { render } from 'solid-js/web';
import { Page } from '../../shared/services/app-base/page-base';
import { MedicalRecordServices } from './services';
import { MedicalRecord } from './components/medical-record';

window.addEventListener('load', async () => {
  new MedicalRecordPage();
});
export class MedicalRecordPage extends Page {
  readonly services;

  constructor() {
    super();
    this.services = new MedicalRecordServices();
    this.renderComponents();
  }

  private renderComponents() {
    if ('customElements' in window) {
      const medicalRecordContainer = this.services.getHTMLElement(
        'medical-record-container'
      );
      render(
        () => (
          <>
            <MedicalRecord />
          </>
        ),
        medicalRecordContainer
      );
    }
  }
}
