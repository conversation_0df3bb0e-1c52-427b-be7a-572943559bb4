import { Services } from '../../shared/services/app-base/service-base';
import { TableData } from './types';
import { User, UserCountry, UserStatus, UserType } from 'entities/User';

export class UserAdministrationServices extends Services {
  public mapUsersToTableFormattedData(users: User[]): TableData[] {
    return users.map((user) => {
      const userStatusHuman = this.getHumanUserStatus(user.userStatus);
      const userTypeHuman = this.getHumanUserType(user.userType);
      const userCountryHuman = this.getHumanUserCountry(user.country);

      return {
        ...user,
        userCountryHuman,
        userStatusHuman,
        userTypeHuman,
      };
    });
  }

  private getHumanUserStatus(status: UserStatus) {
    const statusDictionary = {
      ['ACTIVE']: 'Activo',
      ['ARCHIVED']: 'Archivado',
      ['EXPIRED']: 'Expirado',
    };

    return statusDictionary[status];
  }

  private getHumanUserType(status: UserType) {
    const statusDictionary = {
      ['ADMIN']: 'Administrador',
      ['USER']: 'Colaborador',
      ['DESITION_MAKER']: 'Tomador de decisiones',
      ['CONTENT_CREATOR']: 'Creador de Contenido',
    };

    return statusDictionary[status] || 'USER';
  }

  private getHumanUserCountry(country: UserCountry) {
    const countryDictionary = {
      ['COSTA_RICA']: 'Costa Rica',
      ['EL_SALVADOR']: 'El Salvador',
      ['PANAMA']: 'Panama',
      ['GUATEMALA']: 'Guatemala',
      ['HONDURAS']: 'Honduras',
    };

    return countryDictionary[country] || '';
  }
}
