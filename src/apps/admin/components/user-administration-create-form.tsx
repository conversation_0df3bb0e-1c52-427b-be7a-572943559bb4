import { Setter, createSignal } from 'solid-js';
import { NewUser, TableData, ViewType } from '../types';
import { Form } from 'solid-bootstrap';
import { UserAdministrationPage } from '../main';
import { UserCountry, UserType } from 'entities/User';

const UserAdminCreateForm = (props: {
  mainContext: UserAdministrationPage;
  user: TableData | undefined;
  setView: Setter<ViewType>;
}) => {
  const [newUser, setNewUser] = createSignal<NewUser>({
    name: '',
    phone: 0,
    identification: '',
    email: '',
    country: 'COSTA_RICA',
    userType: 'USER',
  });
  const submitForm = async (e: Event) => {
    e.preventDefault();
    const isSuccess = await props.mainContext.createUser(newUser());
    if (isSuccess) props.setView('TABLE');
  };

  return (
    <>
      <Form onSubmit={submitForm}>
        <div class="card card-bordered">
          <div class="card-header">
            <h3 class="card-title">Nuevo Usuario</h3>
            <div class="card-toolbar">
              <div class="card-toolbar gap-2 gap-md-5">
                <button
                  onClick={() => {
                    props.setView('TABLE');
                  }}
                  type="button"
                  class="btn btn-sm btn-secondary"
                >
                  Regresar
                </button>
                <button
                  class="btn btn-sm btn-primary align-self-center"
                  type="submit"
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="mb-10">
              <label class="form-label">País</label>
              <select
                class="form-select"
                aria-label="Select example"
                value={newUser()?.country}
                onInput={(e) => {
                  setNewUser((form) => {
                    if (form) {
                      form.country = (e.target as unknown as HTMLInputElement)
                        .value as UserCountry;
                    }

                    return form;
                  });
                }}
                required
              >
                <option value="COSTA_RICA">Costa Rica</option>
                <option value="EL_SALVADOR">El Salvador</option>
                <option value="PANAMA">Panama</option>
                <option value="GUATEMALA">Guatemala</option>
                <option value="HONDURAS">Honduras</option>
              </select>
            </div>
            <div class="mb-10">
              <label class="form-label">Tipo de Usuario</label>
              <select
                class="form-select"
                aria-label="Select example"
                value={newUser()?.userType}
                onInput={(e) => {
                  setNewUser((form) => {
                    if (form) {
                      form.userType = (e.target as unknown as HTMLInputElement)
                        .value as UserType;
                    }

                    return form;
                  });
                }}
                required
              >
                <option value="USER">Colaborador</option>
                <option value="CONTENT_CREATOR">Creador de Contenido</option>
                <option value="DESITION_MAKER">Tomador de decisiones</option>
                <option value="ADMIN">Administrador</option>
              </select>
            </div>
          </div>
        </div>
        <div class="card card-bordered mt-5">
          <div class="card-header">
            <h3 class="card-title">Información de Básica</h3>
          </div>
          <div class="card-body">
            <div class="mb-10">
              <label class="form-label">Nombre Completo</label>
              <input
                value={newUser()?.name}
                onInput={(e) => {
                  setNewUser((form) => {
                    if (form) {
                      form.name = (e.target as HTMLInputElement).value || '';
                    }

                    return form;
                  });
                }}
                type="text"
                class="form-control"
                required
              />
            </div>
            <div class="mb-10">
              <label class="form-label">Cédula</label>
              <input
                value={newUser()?.identification}
                onInput={(e) => {
                  setNewUser((form) => {
                    if (form) {
                      form.identification =
                        (e.target as HTMLInputElement).value || '';
                    }

                    return form;
                  });
                }}
                type="text"
                class="form-control"
                required
              />
            </div>
            <div class="mb-10">
              <label class="form-label">Email</label>
              <input
                id="email"
                value={newUser()?.email}
                onInput={(e) => {
                  setNewUser((form) => {
                    if (form) {
                      form.email = (e.target as HTMLInputElement).value || '';
                    }

                    return form;
                  });
                }}
                type="email"
                class="form-control"
                pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                required
              />
            </div>
            <div class="mb-10">
              <label class="form-label">Número de Teléfono</label>
              <input
                type="text"
                pattern="^[0-9]{1,8}$"
                title="Numero de teléfono debe ser solo números y de 8 dígitos."
                class="form-control"
                value={newUser()?.phone}
                onInput={(e) => {
                  let input = (e.target as HTMLInputElement).value;
                  input = input.replace(/\D/g, '');
                  setNewUser((form) => {
                    if (form) {
                      form.phone = parseInt(input) || 0;
                    }

                    return form;
                  });
                }}
                required
              />
            </div>
          </div>
        </div>
      </Form>
    </>
  );
};
export default UserAdminCreateForm;
