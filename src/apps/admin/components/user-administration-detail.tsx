import { Form } from 'solid-bootstrap';
import { createMemo, createSignal, onMount, Setter } from 'solid-js';
import { UserAdministrationPage } from '../main';
import { TableData, ViewType } from '../types';
import ProfileHeader from '../../../shared/components/user/profile-header';
import { User, UserCountry, UserType } from 'entities/User';

const [isFormDisabled, setIsFormDisabled] = createSignal<boolean>(true);
const [formData, setFormData] = createSignal<TableData>();
const UserAdminDetail = (props: {
  mainContext: UserAdministrationPage;
  user: TableData | undefined;
  setView: Setter<ViewType>;
}) => {
  const submitForm = async (e: Event) => {
    e.preventDefault();
    await props.mainContext.editUserData(formData());
  };
  onMount(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    setIsFormDisabled(true);
    props.user && setFormData(props.user);
  });
  const user = createMemo(() => props.user as User);

  return (
    <>
      {props.user && <ProfileHeader user={user} />}
      <Form onSubmit={submitForm}>
        <div class="card mb-5 mb-xl-12">
          <div class="card-header cursor-pointer">
            <div class="card-title m-0">
              <h3 class="fw-bold m-0">Información Básica </h3>
            </div>
            <div class="card-toolbar gap-2 gap-md-5">
              <button
                onClick={() => {
                  props.setView('TABLE');
                }}
                type="button"
                class="btn btn-sm btn-secondary"
              >
                Regresar
              </button>
              {!isFormDisabled() && (
                <button
                  class="btn btn-sm btn-success align-self-center"
                  type="submit"
                >
                  Guardar
                </button>
              )}
              <button
                onClick={() => {
                  setIsFormDisabled((prev) => !prev);
                }}
                type="button"
                class="btn btn-sm btn-danger"
              >
                {isFormDisabled() ? 'Desbloquear' : 'Bloquear'}
              </button>
            </div>
          </div>

          <div class="card-body p-9">
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">
                Nombre Completo
              </label>

              <div class="col-lg-8">
                <input
                  id="user-name"
                  type="text"
                  class="form-control"
                  value={formData()?.name}
                  onInput={(e) => {
                    setFormData((user) => {
                      if (!user) return user;
                      user.name = (e.target as HTMLInputElement).value || '';

                      return user;
                    });
                  }}
                  disabled={isFormDisabled()}
                  required
                ></input>
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Cédula</label>

              <div class="col-lg-8">
                <input
                  id="identification"
                  type="text"
                  class="form-control"
                  value={formData()?.identification}
                  onInput={(e) => {
                    setFormData((user) => {
                      if (!user) return user;
                      user.identification =
                        (e.target as HTMLInputElement).value || '';

                      return user;
                    });
                  }}
                  disabled={isFormDisabled()}
                  required
                ></input>
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Teléfono</label>

              <div class="col-lg-8 d-flex align-items-center">
                <input
                  id="user-phone"
                  type="number"
                  class="form-control"
                  value={formData()?.phone}
                  disabled
                  required
                ></input>
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Email</label>

              <div class="col-lg-8 fv-row">
                <input
                  id="user-company"
                  type="text"
                  class="form-control"
                  value={props.user?.email}
                  disabled
                  required
                />
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">País</label>

              <div class="col-lg-8 fv-row">
                <select
                  class="form-select"
                  aria-label="Select example"
                  value={formData()?.country}
                  onInput={(e) => {
                    setFormData((form) => {
                      if (!form) return form;
                      form.country =
                        ((e.target as unknown as HTMLInputElement)
                          .value as UserCountry) || 'COSTA_RICA';

                      return form;
                    });
                  }}
                  required
                  disabled={isFormDisabled()}
                >
                  <option value="COSTA_RICA">Costa Rica</option>
                  <option value="EL_SALVADOR">El Salvador</option>
                  <option value="PANAMA">Panama</option>
                  <option value="GUATEMALA">Guatemala</option>
                  <option value="HONDURAS">Honduras</option>
                </select>
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Tipo</label>

              <div class="col-lg-8 fv-row">
                <select
                  class="form-select"
                  aria-label="Select example"
                  value={formData()?.userType}
                  onInput={(e) => {
                    setFormData((form) => {
                      if (!form) return form;
                      form.userType =
                        ((e.target as unknown as HTMLInputElement)
                          .value as UserType) || '';

                      return form;
                    });
                  }}
                  required
                  disabled={isFormDisabled()}
                >
                  <option value="USER">Colaborador</option>
                  <option value="CONTENT_CREATOR">Creador de Contenido</option>
                  <option value="DESITION_MAKER">Tomador de decisiones</option>
                  <option value="ADMIN">Administrador</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="card mb-5 mb-xl-12">
          <div class="card-header cursor-pointer">
            <div class="card-title m-0">
              <h3 class="fw-bold m-0">Registro </h3>
            </div>
          </div>

          <div class="card-body p-9">
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Estado</label>

              <div class="col-lg-8">
                <input
                  id="user-name"
                  type="text"
                  class="form-control"
                  value={props.user?.userStatus}
                  disabled
                ></input>
              </div>
            </div>

            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Creado</label>

              <div class="col-lg-8 fv-row">
                <input
                  id="user-company"
                  type="text"
                  class="form-control"
                  value={props.user?.createdAt}
                  disabled
                ></input>
              </div>
            </div>

            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">
                Ultima Actualización
              </label>

              <div class="col-lg-8 d-flex align-items-center">
                <input
                  id="user-phone"
                  type="text"
                  class="form-control"
                  value={props.user?.updatedAt}
                  disabled
                ></input>
              </div>
            </div>
          </div>
        </div>
      </Form>
    </>
  );
};
export default UserAdminDetail;
