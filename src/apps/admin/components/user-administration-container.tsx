import Animation from '../../../shared/components/lottie-player/animation';
import { createEffect, createSignal, Match, onMount, Switch } from 'solid-js';
import { UserContainerProps, TableData, ViewType } from '../types';
import { Tab, Tabs } from 'solid-bootstrap';
import { useTable } from '../hooks/use-table';
import { createStore } from 'solid-js/store';
import { useUserAdministration } from '../hooks/use-user-administration';
import UserAdminDetail from './user-administration-detail';
import UserAdminCreateForm from './user-administration-create-form';
import UserTableComponent from '../../../shared/components/table/user-table';

type UserList = { active: TableData[]; archived: TableData[] };
const [key, setKey] = createSignal('ACTIVE');
const [view, setView] = createSignal<ViewType>('TABLE');
const [isLoading, setIsLoading] = createSignal(true);
const [hasToLoadData, setHasToLoadData] = createSignal(false);
const [userList, setUserList] = createStore<UserList>({
  active: [],
  archived: [],
});
const [currentUser, setCurrentUser] = createSignal<TableData | undefined>();
const UserAdministrationContainer = ({
  context: mainContext,
}: UserContainerProps) => {
  const {
    detailCallback,
    resetPasswordCallback,
    deleteCallback,
    activateCallback,
  } = useUserAdministration({
    mainContext,
    setCurrentUser,
    setView,
    setHasToLoadData,
    setKey,
    setIsLoading,
  });
  const {
    tableHeaders,
    activeTableControlColumns,
    archivedTableControlColumns,
  } = useTable({
    detailCallback,
    resetPasswordCallback,
    deleteCallback,
    activateCallback,
  });
  const loadTableDataBasesOnUserStatus = async () => {
    setIsLoading(true);
    try {
      const data = await mainContext.getUserList();
      setUserList(
        'active',
        data.filter((e) => e.userStatus === 'ACTIVE')
      );
      setUserList(
        'archived',
        data.filter((e) => e.userStatus === 'ARCHIVED')
      );
    } catch (error) {
      mainContext.showErrorAlert({
        message: 'Error al cargar los usuarios',
      });
    } finally {
      setIsLoading(false);
    }
  };
  createEffect(async () => {
    if (hasToLoadData()) {
      setHasToLoadData(false);
      await loadTableDataBasesOnUserStatus();
    }
  });
  onMount(async () => {
    await loadTableDataBasesOnUserStatus();
  });
  createEffect(async () => {
    if (view() === 'TABLE') {
      await loadTableDataBasesOnUserStatus();
    }
  });

  return (
    <>
      <Switch>
        <Match when={view() === 'TABLE'}>
          <div class="card ">
            <div class="card-header">
              <h3 class="card-title">Usuarios Activos</h3>
              <div class="card-toolbar">
                <div class="card-toolbar gap-2 gap-md-5">
                  <button
                    onClick={() => {
                      setView('CREATE');
                    }}
                    type="button"
                    class="btn btn-sm btn-primary align-self-center"
                  >
                    Nuevo Usuario
                  </button>
                </div>
              </div>
            </div>
            <Switch>
              <Match when={isLoading() === true}>
                <div class="card-body card-toolbar">
                  <Animation />
                </div>
              </Match>
              <Match when={isLoading() === false}>
                <div class="card-body card-toolbar">
                  <div class="card-body card-toolbar">
                    <Tabs
                      class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6"
                      id="users"
                      activeKey={key()}
                      onSelect={(tab) => setKey(tab as string)}
                    >
                      <Tab eventKey="ACTIVE" title="Activos">
                        <UserTableComponent
                          title={'Usuarios Activos'}
                          data={userList.active}
                          header={tableHeaders}
                          control={activeTableControlColumns}
                          pageSize={15}
                          isTableStriped
                          isSearchEnabled
                          isExportEnabled
                        />
                      </Tab>
                      <Tab eventKey="ARCHIVED" title="Archivados">
                        <UserTableComponent
                          title={'Usuarios Archivados'}
                          data={userList.archived}
                          header={tableHeaders}
                          control={archivedTableControlColumns}
                          pageSize={15}
                          isTableStriped
                          isSearchEnabled
                          isExportEnabled
                        />
                      </Tab>
                    </Tabs>
                  </div>
                </div>
              </Match>
            </Switch>
          </div>
        </Match>
        <Match when={view() === 'DETAIL'}>
          <UserAdminDetail
            mainContext={mainContext}
            user={currentUser()}
            setView={setView}
          />
        </Match>
        <Match when={view() === 'CREATE'}>
          <UserAdminCreateForm
            mainContext={mainContext}
            user={currentUser()}
            setView={setView}
          />
        </Match>
      </Switch>
    </>
  );
};
export default UserAdministrationContainer;
