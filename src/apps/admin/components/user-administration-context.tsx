import {
  createSignal,
  createContext,
  useContext,
  Accessor,
  Setter,
} from 'solid-js';
import { UserAdministrationPage } from '../main';
import { TableData } from '../types';

type CurrentView = 'TABLE' | 'FORM' | 'DETAIL';
interface IUserAdministrationContextContextModel {
  currentView: Accessor<CurrentView>;
  setCurrentView: Setter<CurrentView>;
  isLoading: Accessor<boolean>;
  setIsLoading: Setter<boolean>;
  currentApplication: Accessor<TableData | undefined>;
  setCurrentApplication: Setter<TableData | undefined>;
  mainContext: Accessor<UserAdministrationPage | undefined>;
  setParentContext: Setter<UserAdministrationPage | undefined>;
  loadData: Accessor<boolean>;
  setLoadData: Setter<boolean>;
}
const UserAdministrationContext =
  createContext<IUserAdministrationContextContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function UserAdministrationProvider(props: any) {
  const [currentView, setCurrentView] = createSignal<CurrentView>('TABLE');
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [loadData, setLoadData] = createSignal<boolean>(false);
  const [currentApplication, setCurrentApplication] = createSignal<
    TableData | undefined
  >();
  const [mainContext, setParentContext] =
    createSignal<UserAdministrationPage>();
  const value: IUserAdministrationContextContextModel = {
    currentView,
    setCurrentView,
    isLoading,
    setIsLoading,
    currentApplication,
    setCurrentApplication,
    mainContext,
    setParentContext,
    loadData,
    setLoadData,
  };

  return (
    <UserAdministrationContext.Provider value={value}>
      {props.children}
    </UserAdministrationContext.Provider>
  );
}
export function useUserAdministrationContextContext():
  | IUserAdministrationContextContextModel
  | undefined {
  return useContext(UserAdministrationContext);
}
