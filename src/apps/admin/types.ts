import { User, UserType } from '../../entities/User';
import { UserAdministrationPage } from './main';

export type UserContainerProps = {
  context: UserAdministrationPage;
};
export type TableData = User & {
  country: string;
  userTypeHuman: string;
  userStatusHuman: string;
  userCountryHuman: string;
};
export type ViewType = 'TABLE' | 'FORM' | 'DETAIL' | 'EDIT' | 'CREATE';
export type EditUserData = {
  id: string;
  email: string;
  name: string;
  phone: number;
  country: string;
  identification: string;
  userType: string;
};
export type NewUser = {
  name: string;
  email: string;
  phone: number;
  identification: string;
  country: string;
  userType: UserType;
};
