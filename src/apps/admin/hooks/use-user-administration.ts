import Swal from 'sweetalert2';
import { TableData, ViewType } from '../types';
import { UserAdministrationPage } from '../main';
import { Setter } from 'solid-js';

export type UseUserAdministrationProps = {
  mainContext: UserAdministrationPage;
  setCurrentUser: Setter<TableData | undefined>;
  setView: Setter<ViewType>;
  setHasToLoadData: Setter<boolean>;
  setKey: Setter<string>;
  setIsLoading: Setter<boolean>;
};
export const useUserAdministration = (props: UseUserAdministrationProps) => {
  const detailCallback = (row: TableData) => {
    props.setCurrentUser(row);
    props.setView('DETAIL');
  };
  const resetPasswordCallback = async (row: TableData) => {
    Swal.fire({
      title: 'Reset Password',
      text: '¿Esta seguro que desea restablecer la contraseña de este usuario?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Reset',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        const password = await props.mainContext.resetSupplierPassword(row.id);
        if (password) {
          props.mainContext.showSuccessAlert({
            message: `Contraseña temporal: ${password}`,
          });
        }
      }
    });
  };
  const deleteCallback = async (row: TableData) => {
    Swal.fire({
      title: 'Archivar usuario',
      text: '¿Esta seguro que desea archivar este usuario?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Archivar',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      props.setIsLoading(true);
      try {
        if (result.isConfirmed) {
          const isSuccess = await props.mainContext.archiveUser(row.id);
          if (isSuccess) {
            props.setHasToLoadData(true);
            props.setKey('ARCHIVED');
          }
        }
      } catch (error) {
        props.mainContext.showErrorAlert({
          message: 'Error al archivar el usuario',
        });
      } finally {
        props.setIsLoading(false);
      }
    });
  };
  const activateCallback = async (row: TableData) => {
    Swal.fire({
      title: 'Activar usuario',
      text: '¿Esta seguro que desea activar este usuario?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Activar',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      props.setIsLoading(true);
      try {
        if (result.isConfirmed) {
          const isSuccess = await props.mainContext.activateUser(row.id);
          if (isSuccess) {
            props.setHasToLoadData(true);
            props.setKey('ACTIVE');
          }
        }
      } catch (error) {
        props.mainContext.showErrorAlert({
          message: 'Error al activar el usuario',
        });
      } finally {
        props.setIsLoading(false);
      }
    });
  };

  return {
    detailCallback,
    resetPasswordCallback,
    deleteCallback,
    activateCallback,
  };
};
