import { TableControlHeader, TableHeader } from 'shared/components/table/types';
import { TableData } from '../types';

type UseTableProps = {
  detailCallback: (row: TableData) => void;
  resetPasswordCallback: (row: TableData) => Promise<void>;
  deleteCallback: (row: TableData) => Promise<void>;
  activateCallback: (row: TableData) => Promise<void>;
};
export const useTable = (props: UseTableProps) => {
  const tableHeaders: TableHeader[] = [
    {
      name: 'identification',
      title: 'Cédula',
      type: 'text',
      width: 100,
      cssClass: 'text-truncate',
    },
    {
      name: 'name',
      title: 'Nombre',
      type: 'text',
      width: 200,
      cssClass: 'text-truncate',
    },
    {
      name: 'userCountryHuman',
      title: 'País',
      type: 'text',
      width: 100,
      cssClass: 'text-truncate',
    },
  ];
  const activeTableControlColumns: TableControlHeader[] = [
    {
      name: 'detail',
      title: 'Detalles',
      controlType: 'primary',
      callback: props.detailCallback,
    },
    {
      name: 'resetPassword',
      title: 'Reset',
      controlType: 'secondary',
      callback: props.resetPasswordCallback,
    },
    {
      name: 'delete',
      title: 'Archivar',
      controlType: 'danger',
      callback: props.deleteCallback,
    },
  ];
  const archivedTableControlColumns: TableControlHeader[] = [
    {
      name: 'detail',
      title: 'Detalles',
      controlType: 'primary',
      callback: props.detailCallback,
    },
    {
      name: 'activate',
      title: 'Reactivar',
      controlType: 'secondary',
      callback: props.activateCallback,
    },
  ];

  return {
    tableHeaders,
    activeTableControlColumns,
    archivedTableControlColumns,
  };
};
