import { Page } from '../../shared/services/app-base/page-base';
import { render } from 'solid-js/web';
import { UserAdministrationServices } from './services';
import { EditUserData, NewUser, TableData } from './types';
import { UserStatus } from 'entities/User';
import UserAdministrationContainer from './components/user-administration-container';
import { UserAdministrationProvider } from './components/user-administration-context';
import { UserAPI } from '../../shared/infra';

window.addEventListener('load', async () => {
  new UserAdministrationPage();
});
export class UserAdministrationPage extends Page {
  readonly services;

  readonly api;

  constructor() {
    super();
    this.services = new UserAdministrationServices();
    this.api = new UserAPI();
    this.renderComponents();
  }

  private async getUsersByStatus(
    userStatus: UserStatus
  ): Promise<TableData[] | []> {
    try {
      const supplierApplications = await this.api.getUsersByStatus(userStatus);
      const tableFormattedData =
        this.services.mapUsersToTableFormattedData(supplierApplications);

      return tableFormattedData;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener la información' });

      return [];
    }
  }

  public async getUserList(): Promise<TableData[]> {
    try {
      let data: TableData[] = [];
      const results = await Promise.allSettled([
        this.getUsersByStatus('ACTIVE'),
        this.getUsersByStatus('ARCHIVED'),
      ]);
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          data = [...data, ...result.value];
        }
      });

      return data;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener la información' });

      return [];
    }
  }

  public async archiveUser(id: string): Promise<boolean> {
    try {
      await this.api.archiveUser(id);

      return true;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al archivar usuario' });

      return false;
    }
  }

  public async activateUser(id: string): Promise<boolean> {
    try {
      await this.api.activateUser(id);

      return true;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al activar usuario' });

      return false;
    }
  }

  public async resetSupplierPassword(id: string): Promise<string> {
    try {
      const { password } = await this.api.resetUserPassword(id);

      return password;
    } catch (error) {
      this.showErrorAlert({
        message: 'Error al restablecer contraseña de usuario',
      });

      return '';
    }
  }

  public async createUser(data: NewUser | undefined) {
    try {
      this.showLoadingScreen();
      if (!data) return;
      if (this.services.isDataMissingInformation(data))
        throw 'Missing user information';
      await this.api.createUser(data);
      this.showSuccessAlert({
        message: 'Usuario Creado Con Éxito.',
      });

      return true;
    } catch (error) {
      if (error === 'Missing user information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return false;
      }
      this.showErrorAlert({ message: 'Error al crear usuario' });

      return false;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async editUserData(data: EditUserData | undefined) {
    try {
      this.showLoadingScreen();
      if (!data) return;
      if (this.services.isDataMissingInformation(data))
        throw 'Missing user information';
      await this.api.updateUserInformation(data);
      this.showSuccessAlert({
        message: 'Información del usuario actualizada.',
      });
    } catch (error) {
      if (error === 'Missing user information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return;
      }
      this.showErrorAlert({ message: 'Error al actualizar usuario' });
    } finally {
      this.hideLoadingScreen();
    }
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'new-supplier-application-container'
      );
      render(
        () => (
          <>
            <UserAdministrationProvider>
              <UserAdministrationContainer context={this} />
            </UserAdministrationProvider>
          </>
        ),
        userProfileContainer
      );
    }
  }
}
