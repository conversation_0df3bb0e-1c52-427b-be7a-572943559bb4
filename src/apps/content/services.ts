import { Services } from '../../shared/services/app-base/service-base';
import { TableData } from './types';
import { Post, PostStatus } from 'entities/Post';

export class HealthContentServices extends Services {
  public mapHealthContentsToTableFormattedData(
    contentList: Post[]
  ): TableData[] {
    return contentList.map((content) => {
      const contentStatusHuman = this.getHumanHealthContentStatus(
        content.contentStatus
      );

      return {
        id: content.id ?? '',
        userId: content.userId,
        userName: content.userName,
        imagesURL: content.imagesURL,
        externalLinks: content.externalLinks,
        likes: content.likes,
        content: content.content,
        contentStatus: content.contentStatus,
        contentStatusHuman,
        updatedAt: this.getHumanReadableDate(
          content.updatedAt ?? new Date().toISOString()
        ),
        createdAt: this.getHumanReadableDate(
          content.createdAt ?? new Date().toISOString()
        ),
      };
    });
  }

  private getHumanReadableDate(datetime: string) {
    const isoDateString = datetime;
    const date = new Date(isoDateString);
    // eslint-disable-next-line
    const options: any = { year: 'numeric', month: 'long', day: 'numeric' };
    const humanReadableDate = date.toLocaleDateString('es', options);

    return humanReadableDate;
  }

  private getHumanHealthContentStatus(status: PostStatus) {
    const statusDictionary = {
      ['ACTIVE']: 'Activo',
      ['ARCHIVED']: 'Archivado',
    };

    return statusDictionary[status];
  }
}
