import { createEffect, createSignal, onMount } from 'solid-js';
import { TableData, ViewType } from '../types';
import { TableControlHeader, TableHeader } from 'shared/components/table/types';
import Swal from 'sweetalert2';
import { HealthContentPage } from '../main';

export const useContentContainer = (controller: HealthContentPage) => {
  const [view, setView] = createSignal<ViewType>('TABLE');
  const [isLoading, setIsLoading] = createSignal(false);
  const [hasToLoadData, setHasToLoadData] = createSignal(false);
  const [tableData, setTableData] = createSignal<TableData[]>([]);
  const [currentHealthContent, setCurrentHealthContent] = createSignal<
    TableData | undefined
  >();
  const tableHeaders: TableHeader[] = [
    {
      name: 'userName',
      title: 'Nombre',
      type: 'text',
      width: 100,
    },
    {
      name: 'createdAt',
      title: 'Creado',
      type: 'text',
      width: 150,
    },
    {
      name: 'updatedAt',
      title: 'Actualizado',
      type: 'text',
      width: 150,
    },
  ];
  const tableControlColumns: TableControlHeader[] = [
    {
      name: 'detail',
      title: 'Detalles',
      controlType: 'primary',
      callback: (row: TableData) => {
        setCurrentHealthContent(row);
        setView('DETAIL');
      },
    },
    {
      name: 'delete',
      title: 'Archivar',
      controlType: 'danger',
      callback: async (row: TableData) => {
        Swal.fire({
          title: 'Archivar Publicación',
          text: '¿Esta seguro que desea archivar esta publicación?',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Archivar',
          customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary',
          },
        }).then(async (result) => {
          if (result.isConfirmed) {
            const isSuccess = await controller.archiveContent(row.id);
            if (isSuccess) {
              setHasToLoadData(true);
            }
          }
        });
      },
    },
  ];
  const loadTableDataBasesOnHealthContentStatus = async () => {
    setIsLoading(true);
    const response = await controller.getContentByStatus('ACTIVE');
    setTableData(response);
    setIsLoading(false);
  };
  createEffect(async () => {
    if (hasToLoadData()) {
      setHasToLoadData(false);
      await loadTableDataBasesOnHealthContentStatus();
    }
  });
  onMount(async () => {
    await loadTableDataBasesOnHealthContentStatus();
  });
  createEffect(async () => {
    if (view() === 'TABLE') {
      await loadTableDataBasesOnHealthContentStatus();
    }
  });

  return {
    view,
    setView,
    isLoading,
    setIsLoading,
    hasToLoadData,
    setHasToLoadData,
    tableData,
    setTableData,
    currentHealthContent,
    setCurrentHealthContent,
    tableHeaders,
    tableControlColumns,
  };
};
