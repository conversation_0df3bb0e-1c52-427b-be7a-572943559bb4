import { createSignal } from 'solid-js';
import { TableData } from '../types';

export const useContentDetail = () => {
  const [isFormDisabled, setIsFormDisabled] = createSignal<boolean>(true);
  const [formData, setFormData] = createSignal<TableData>();
  const [content, setContent] = createSignal<string>('', { equals: false });

  return {
    isFormDisabled,
    setIsFormDisabled,
    formData,
    setFormData,
    content,
    setContent,
  };
};
