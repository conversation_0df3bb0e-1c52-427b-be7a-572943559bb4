import { Setter } from 'solid-js';
import { HealthContentPage } from './main';
import { Post } from 'entities/Post';

export type HealthContentContainerProps = {
  context: HealthContentPage;
};
export type TableData = Post & {
  contentStatusHuman: string;
};
export type ViewType = 'TABLE' | 'FORM' | 'DETAIL' | 'EDIT' | 'CREATE';
export type EditHealthContentData = {
  id: string;
  userName: string;
  content: string;
};
export type NewHealthContent = {
  name: string;
  content: string;
};
export type ContentAdminDetailProps = {
  mainContext: HealthContentPage;
  user: TableData | undefined;
  setView: Setter<ViewType>;
};
export type HealthContentAdminCreateFormProps = {
  mainContext: HealthContentPage;
  user: TableData | undefined;
  setView: Setter<ViewType>;
};
