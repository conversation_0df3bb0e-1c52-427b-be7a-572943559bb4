import { Page } from '../../shared/services/app-base/page-base';
import { render } from 'solid-js/web';
import { HealthContentServices } from './services';
import { TableData } from './types';
import HealthContentContainer from './components/container';
import { HealthContentProvider } from './components/context';
import { HealthContentStatus } from 'entities/HealthContent';
import { HealthContentAPI } from '../../shared/infra';

window.addEventListener('load', async () => {
  new HealthContentPage();
});
export class HealthContentPage extends Page {
  readonly services;

  readonly api;

  constructor() {
    super();
    this.services = new HealthContentServices();
    this.api = new HealthContentAPI();
    this.renderComponents();
  }

  public async getContentByStatus(
    contentStatus: HealthContentStatus
  ): Promise<TableData[] | []> {
    try {
      const contentList = await this.api.getHealthContentsByStatus(
        contentStatus
      );
      const tableFormattedData =
        this.services.mapHealthContentsToTableFormattedData(contentList);

      return tableFormattedData;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener la información' });

      return [];
    }
  }

  public async archiveContent(id: string): Promise<boolean> {
    try {
      await this.api.archiveHealthContent(id);

      return true;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al archivar contenido' });

      return false;
    }
  }

  public async createContent(content: string, images: string[]) {
    try {
      this.showLoadingScreen();
      if (!content || !images) return;
      if (this.services.isDataMissingInformation({ content, images }))
        throw 'Missing content information';
      await this.api.createHealthContent(content, images);
      this.showSuccessAlert({
        message: 'Contenido Creado Con Éxito.',
      });

      return true;
    } catch (error) {
      if (error === 'Missing content information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return false;
      }
      this.showErrorAlert({ message: 'Error al crear contenido' });

      return false;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async editContent(data: TableData | undefined) {
    try {
      this.showLoadingScreen();
      if (!data) return;
      if (this.services.isDataMissingInformation(data))
        throw 'Missing content information';
      await this.api.updateHealthContentInformation(data);
      this.showSuccessAlert({
        message: 'Información de la publicación actualizada.',
      });
    } catch (error) {
      if (error === 'Missing content information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return;
      }
      this.showErrorAlert({ message: 'Error al actualizar conteniendo' });
    } finally {
      this.hideLoadingScreen();
    }
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer =
        this.services.getHTMLElement('content-container');
      render(
        () => (
          <>
            <HealthContentProvider>
              <HealthContentContainer context={this} />
            </HealthContentProvider>
          </>
        ),
        userProfileContainer
      );
    }
  }
}
