import { Form } from 'solid-bootstrap';
import { Show, createSignal, onMount } from 'solid-js';
import { ContentAdminDetailProps } from '../types';
import CustomEditor from '../../../shared/components/editor/editor';
import { useContentDetail } from '../hooks/use-content-detail';
import EmptyLogo from '../../../assets/media/logos/post_empty.svg';
import Gallery from '../../../shared/components/gallery/gallery';
import { useScreenWidth } from '../../../shared/hooks/use-screen-width';

const ContentAdminDetail = (props: ContentAdminDetailProps) => {
  const [images, setImages] = createSignal<Array<string>>([], {
    equals: false,
  });
  const screenWidth = useScreenWidth();
  const {
    isFormDisabled,
    setIsFormDisabled,
    formData,
    setFormData,
    content,
    setContent,
  } = useContentDetail();
  const submitForm = async (e: Event) => {
    e.preventDefault();
    setFormData((prev) => {
      if (!prev) return prev;
      prev.content = content();
      prev.content = content();
      prev.imagesURL = images();

      return prev;
    });
    await props.mainContext.editContent(formData());
  };
  function addImage(imageData: string) {
    setImages([...images(), imageData]);
  } // eslint-disable-next-line
  function handleImageInputChange(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    // eslint-disable-next-line
      reader.onload = (e: any) => {
      addImage(e.target.result as string);
    };
    reader.readAsDataURL(file);
  }
  const clearImages = () => {
    setImages([]);
  };
  onMount(() => {
    setContent(props.user?.content ?? '');
    setIsFormDisabled(true);
    setImages(props.user?.imagesURL ?? []);
    props.user && setFormData(props.user);
  });

  return (
    <>
      <Form onSubmit={submitForm}>
        <div class="card mb-5 mb-xl-12">
          <div class="card-header cursor-pointer">
            <div class="card-title m-0">
              <h3 class="fw-bold m-0">Información Básica </h3>
            </div>
            <div class="card-toolbar gap-2 gap-md-5">
              <button
                onClick={() => {
                  props.setView('TABLE');
                }}
                type="button"
                class="btn btn-sm btn-secondary"
              >
                Regresar
              </button>
              {!isFormDisabled() && (
                <button
                  class="btn btn-sm btn-success align-self-center"
                  type="submit"
                >
                  Guardar
                </button>
              )}
              <button
                onClick={() => {
                  setIsFormDisabled((prev) => !prev);
                }}
                type="button"
                class="btn btn-sm btn-danger"
              >
                {isFormDisabled() ? 'Desbloquear' : 'Bloquear'}
              </button>
            </div>
          </div>

          <div class="card-body p-9">
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Nombre:</label>

              <div class="col-lg-8">
                <input
                  id="user-name"
                  type="text"
                  class="form-control"
                  value={formData()?.userName}
                  onInput={(e) => {
                    setFormData((user) => {
                      if (!user) return user;
                      user.userName =
                        (e.target as HTMLInputElement).value || '';

                      return user;
                    });
                  }}
                  disabled
                  required
                ></input>
              </div>
            </div>

            <div class="mb-10">
              <label class="col-lg-4 fw-semibold text-muted mb-5">
                Contenido:
              </label>
              <CustomEditor
                setContent={setContent}
                isEnabled={!isFormDisabled()}
                content={props.user?.content ?? ''}
              />
            </div>
            <div class="mb-10">
              <label class="form-label">Imágenes:</label>
              <div>
                <div
                  class={`image-upload-container ${
                    screenWidth() > 900 ? ' mx-4 my-2' : ' mx-0 my-2'
                  }`}
                >
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageInputChange}
                    class="image-upload-input"
                    disabled={isFormDisabled()}
                  />
                  <label for="image-upload" class="image-upload-label">
                    Agregar Imagen
                  </label>
                </div>
                <div class="post-images my-4 w-full">
                  <Show when={images().length === 0}>
                    <img
                      src={EmptyLogo}
                      alt="Sin Imágenes"
                      style={{
                        height: screenWidth() > 900 ? '200px' : '100px',
                      }}
                    />
                  </Show>
                  <Show when={images().length > 0}>
                    <Gallery images={images} />
                  </Show>
                </div>
              </div>
              <button
                class="btn btn-secondary me-3"
                disabled={isFormDisabled()}
                onClick={clearImages}
                type="button"
              >
                Eliminar Imágenes
              </button>
            </div>
          </div>
        </div>

        <div class="card mb-5 mb-xl-12">
          <div class="card-header cursor-pointer">
            <div class="card-title m-0">
              <h3 class="fw-bold m-0">Registro </h3>
            </div>
          </div>

          <div class="card-body p-9">
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Estado</label>

              <div class="col-lg-8">
                <input
                  id="user-name"
                  type="text"
                  class="form-control"
                  value={props.user?.contentStatusHuman}
                  disabled
                ></input>
              </div>
            </div>

            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">Creado</label>

              <div class="col-lg-8 fv-row">
                <input
                  id="user-company"
                  type="text"
                  class="form-control"
                  value={props.user?.createdAt}
                  disabled
                ></input>
              </div>
            </div>

            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">
                Ultima Actualización
              </label>

              <div class="col-lg-8 d-flex align-items-center">
                <input
                  id="user-phone"
                  type="text"
                  class="form-control"
                  value={props.user?.updatedAt}
                  disabled
                ></input>
              </div>
            </div>
          </div>
        </div>
      </Form>
    </>
  );
};
export default ContentAdminDetail;
