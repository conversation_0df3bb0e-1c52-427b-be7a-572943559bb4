import {
  createSignal,
  createContext,
  useContext,
  Accessor,
  Setter,
} from 'solid-js';
import { HealthContentPage } from '../main';
import { TableData } from '../types';

type CurrentView = 'TABLE' | 'FORM' | 'DETAIL';
interface IHealthContentContextContextModel {
  currentView: Accessor<CurrentView>;
  setCurrentView: Setter<CurrentView>;
  isLoading: Accessor<boolean>;
  setIsLoading: Setter<boolean>;
  currentApplication: Accessor<TableData | undefined>;
  setCurrentApplication: Setter<TableData | undefined>;
  mainContext: Accessor<HealthContentPage | undefined>;
  setParentContext: Setter<HealthContentPage | undefined>;
  loadData: Accessor<boolean>;
  setLoadData: Setter<boolean>;
}
const HealthContentContext = createContext<IHealthContentContextContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function HealthContentProvider(props: any) {
  const [currentView, setCurrentView] = createSignal<CurrentView>('TABLE');
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [loadData, setLoadData] = createSignal<boolean>(false);
  const [currentApplication, setCurrentApplication] = createSignal<
    TableData | undefined
  >();
  const [mainContext, setParentContext] = createSignal<HealthContentPage>();
  const value: IHealthContentContextContextModel = {
    currentView,
    setCurrentView,
    isLoading,
    setIsLoading,
    currentApplication,
    setCurrentApplication,
    mainContext,
    setParentContext,
    loadData,
    setLoadData,
  };

  return (
    <HealthContentContext.Provider value={value}>
      {props.children}
    </HealthContentContext.Provider>
  );
}
export function useHealthContentContextContext():
  | IHealthContentContextContextModel
  | undefined {
  return useContext(HealthContentContext);
}
