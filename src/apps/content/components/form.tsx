import { Show, createSignal } from 'solid-js';
import { HealthContentAdminCreateFormProps } from '../types';
import CustomEditor from '../../../shared/components/editor/editor';
import '../styles/style.css';
import { CONTENT } from '../content';
import EmptyLogo from '../../../assets/media/logos/post_empty.svg';
import Gallery from '../../../shared/components/gallery/gallery';
import { useScreenWidth } from '../../../shared/hooks/use-screen-width';

const [content, setContent] = createSignal<string>(CONTENT);
const [images, setImages] = createSignal<Array<string>>([], { equals: false });
const HealthContentAdminCreateForm = (
  props: HealthContentAdminCreateFormProps
) => {
  const screenWidth = useScreenWidth();
  const submitForm = async (e: Event) => {
    e.preventDefault();
    const isSuccess = await props.mainContext.createContent(
      content(),
      images()
    );
    if (isSuccess) props.setView('TABLE');
  };
  function addImage(imageData: string) {
    setImages([...images(), imageData]);
  }
  // eslint-disable-next-line
  function handleImageInputChange(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    // eslint-disable-next-line
      reader.onload = (e: any) => {
      addImage(e.target.result as string);
    };
    reader.readAsDataURL(file);
  }

  return (
    <>
      <div class="card card-bordered">
        <div class="card-header">
          <h3 class="card-title">Nueva Publicación</h3>
          <div class="card-toolbar">
            <div class="card-toolbar gap-2 gap-md-5">
              <button
                onClick={() => {
                  props.setView('TABLE');
                }}
                type="button"
                class="btn btn-sm btn-secondary"
              >
                Regresar
              </button>
              <button
                class="btn btn-sm btn-success align-self-center"
                type="submit"
                onClick={submitForm}
              >
                Guardar
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card card-bordered mt-5">
        <div class="card-header">
          <h3 class="card-title">Información de Básica</h3>
        </div>
        <div class="card-body">
          <div class="mb-10">
            <label class="form-label mb-5">Contenido:</label>
            <CustomEditor setContent={setContent} isEnabled content={CONTENT} />
          </div>

          <div class="mb-10">
            <label class="form-label">Imágenes:</label>
            <div>
              <div
                class={`image-upload-container ${
                  screenWidth() > 900 ? ' mx-4 my-2' : ' mx-0 my-2'
                }`}
              >
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageInputChange}
                  class="image-upload-input"
                />
                <label for="image-upload" class="image-upload-label">
                  Agregar Imagen
                </label>
              </div>
              <div class="post-images my-4 w-full">
                <Show when={images().length === 0}>
                  <img
                    src={EmptyLogo}
                    alt="Sin Imágenes"
                    style={{ height: screenWidth() > 900 ? '200px' : '100px' }}
                  />
                </Show>
                <Show when={images().length > 0}>
                  <Gallery images={images} />
                </Show>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default HealthContentAdminCreateForm;
