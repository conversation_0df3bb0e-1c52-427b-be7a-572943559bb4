import Animation from '../../../shared/components/lottie-player/animation';
import { Match, Switch } from 'solid-js';
import BasicTableComponent from '../../../shared/components/table/basic-table';
import { HealthContentContainerProps } from '../types';
import ContentAdministrationDetail from './detail';
import HealthContentAdministrationCreateForm from './form';
import { useContentContainer } from '../hooks/use-content-container';

const ContentAdminContainer = ({
  context: mainContext,
}: HealthContentContainerProps) => {
  const {
    view,
    setView,
    isLoading,
    tableData,
    currentHealthContent,
    tableControlColumns,
    tableHeaders,
  } = useContentContainer(mainContext);

  return (
    <>
      <Switch>
        <Match when={view() === 'TABLE'}>
          <div class="card "></div>
          <div class="card ">
            <div class="card-header">
              <h3 class="card-title">Publicaciones</h3>
              <div class="card-toolbar">
                <div class="card-toolbar gap-2 gap-md-5">
                  <button
                    onClick={() => {
                      setView('CREATE');
                    }}
                    type="button"
                    class="btn btn-sm btn-success align-self-center"
                  >
                    Publicar
                  </button>
                </div>
              </div>
            </div>
            {isLoading() === true && (
              <div class="card-body card-toolbar">
                <Animation />
              </div>
            )}
            {isLoading() === false && (
              <div class="card-body card-toolbar">
                <BasicTableComponent
                  title={'Publicaciones Activas'}
                  data={tableData}
                  header={tableHeaders}
                  control={tableControlColumns}
                  pageSize={15}
                  isTableStriped
                  isSearchEnabled
                  isExportEnabled
                />
              </div>
            )}
          </div>
        </Match>
        <Match when={view() === 'DETAIL'}>
          <ContentAdministrationDetail
            mainContext={mainContext}
            user={currentHealthContent()}
            setView={setView}
          />
        </Match>
        <Match when={view() === 'CREATE'}>
          <HealthContentAdministrationCreateForm
            mainContext={mainContext}
            user={currentHealthContent()}
            setView={setView}
          />
        </Match>
      </Switch>
    </>
  );
};
export default ContentAdminContainer;
