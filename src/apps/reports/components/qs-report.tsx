import { createSignal, onMount } from 'solid-js';
import { QSReport } from '../main';
import { QSRReportProps } from '../types';
import Animation from '../../../shared/components/lottie-player/animation';
import { Card } from 'solid-bootstrap';

const Report = ({ context: mainContext }: QSRReportProps<QSReport>) => {
  const [isLoading, setIsLoading] = createSignal(true);
  const [url, setUrl] = createSignal('');
  const handleLoad = () => {
    setIsLoading(false);
  };
  onMount(async () => {
    setIsLoading(true);
    const url = await mainContext.getReportURL();
    setUrl(url || '');
  });

  return (
    <>
      <div class="iframe-container">
        {isLoading() && (
          <Card>
            <Card.Body>
              <div class="card-body card-toolbar">
                <Animation />
              </div>
            </Card.Body>
          </Card>
        )}
        <iframe src={url()} onLoad={handleLoad}></iframe>
      </div>
    </>
  );
};
export default Report;
