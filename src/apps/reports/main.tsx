import { render } from 'solid-js/web';
import { Page } from '../../shared/services/app-base/page-base';
import QSRReport from './components/qs-report';
import { QSReportServices } from './services';
import { MedicalDataAPI } from '../../shared/infra';
import './styles/style.css';

window.addEventListener('load', async () => {
  new QSReport();
});
export class QSReport extends Page {
  readonly api;

  readonly services;

  private view: 'PROGRESS' | 'EXAM' | 'STUDIES';

  constructor() {
    super();
    this.services = new QSReportServices();
    this.api = new MedicalDataAPI();
    this.getModuleToDisplay();
    this.renderComponents();
  }

  private getModuleToDisplay() {
    const view = this.getURLParams().get('report');
    if (view === 'PROGRESS') {
      this.view = 'PROGRESS';

      return;
    }
    if (view === 'EXAM') {
      this.view = 'EXAM';

      return;
    }
    if (view === 'STUDIES') {
      this.view = 'STUDIES';

      return;
    }
    this.showErrorAlert({ message: 'URL Invalida' });
  }

  public async getReportURL() {
    try {
      if (this.view === 'PROGRESS') {
        const result = await this.api.getEmbedQSProgressReportURL();

        return result;
      }
      if (this.view === 'EXAM') {
        const result = await this.api.getEmbedQSExamReportURL();

        return result;
      }
      if (this.view === 'STUDIES') {
        const result = await this.api.getEmbedQSStudiesReportURL();

        return result;
      }

      return '';
    } catch (error) {
      this.showErrorAlert({ message: 'Error obteniendo el reporte' });

      return null;
    }
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'user-profile-container'
      );
      render(
        () => (
          <>
            <QSRReport context={this} />
          </>
        ),
        userProfileContainer
      );
    }
  }
}
