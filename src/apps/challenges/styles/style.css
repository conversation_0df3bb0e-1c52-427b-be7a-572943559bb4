.challenge-card {
    border: 1px solid #D9D9D9;
    border-radius: 12px;
}

.challenge-card:hover {
    border: 1px solid #BC36F0;
    background-color: rgb(188, 54, 240, 0.04);
    color: #BC36F0;
}


.challenge-title {
    color: #003AC2;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.challenge-time {
    color: #000;
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
}

.challenge-price {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.challenge-last-update {
    color: #A7A7A7;
    font-size: 14px;
    font-style: italic;
    font-weight: 400;
    line-height: normal;
}

.challenge-container {
    gap: 20px;
}

.data-container {
    gap: 20px;
    border-top: 1px solid #D9D9D9;


}

.data-card {
    border-radius: 12px;
    border: 1px solid #5E6DF3;
    background: rgba(203, 219, 255, 0.33);

}

.data-card-title {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.data-card-value {
    color: #000;
    text-align: center;
    font-size: 42px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.grid-2x2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 20px;
    /* Adjust as needed */
}

.new-challenge-option {
    border-radius: 12px;
    border: 1px solid #5E6DF3;
    gap: 300px;
    cursor: pointer;

}

.new-challenge-option:hover {
    border: 1px solid #BC36F0;
    background-color: rgb(188, 54, 240, 0.04);
    color: #BC36F0;

}

.modal-header {
    border-bottom: 0px;
}

.modal-option-h1 {
    color: #000;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}


.modal-option-h3 {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}


.new-challenge-option-title {
    color: #5E6DF3;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

}

.new-challenge-option-subtitle {
    color: #716E6E;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.challenge-type-option {
    border-radius: 12px;
    border: 1px solid #5E6DF3;
    margin: 15px;
}

.challenge-type-option-text {
    text-align: center;
    vertical-align: middle;
    margin: auto;

}