import { render } from 'solid-js/web';
import { Page } from '../../shared/services/app-base/page-base';
import { ChallengesServices } from './services';
import {
  ChallengeListElement,
  NewChallengeFormDTO,
  UpdateChallengeFormDTO,
} from './types';
import { getCurrentUserData } from '../../shared/services/user/user-session-management';
import { ChallengesProvider } from './components/context';
import CompletedChallenges from './components/history/completed';
import ChallengesContainer from './components/home/<USER>';
import ChallengeGroupInvitation from './components/invitation/invitation';
import './styles/style.css';
import { ChallengeAPI, UserAPI } from '../../shared/infra';
import { ChallengeProgressEntry } from '../../shared/components/challenge-progress-modal/challenge-progress-types';

window.addEventListener('load', async () => {
  new ChallengesPage();
});
export class ChallengesPage extends Page {
  readonly api;

  readonly userApi;

  readonly services;

  private view: 'HOME' | 'COMPLETED' | 'INVITATION';

  private challengeId: string;

  constructor() {
    super();
    this.services = new ChallengesServices();
    this.api = new ChallengeAPI();
    this.userApi = new UserAPI();
    this.getModuleToDisplay();
    this.renderComponents();
  }

  public async createChallenge(challenge: NewChallengeFormDTO) {
    try {
      this.showLoadingScreen();
      const data = await this.api.postChallenge(challenge);
      this.showSuccessAlert({ message: 'Reto creado con éxito' });

      return data;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error.name && error.name === 'AxiosError') {
        this.showErrorAlert({
          message: 'Error al crear el reto',
          footer: error.response.data,
        });
      } else {
        this.showErrorAlert({ message: 'Error al crear el reto' });
      }

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async updateChallenge(updateData: UpdateChallengeFormDTO) {
    try {
      this.showLoadingScreen();
      const data = await this.api.updateChallenge(updateData);
      this.showSuccessAlert({ message: 'Reto actualizado con éxito' });

      return data;
    } catch (error: any) {
      if (error.name && error.name === 'AxiosError') {
        this.showErrorAlert({
          message: 'Error al actualizar el reto',
          footer: error.response.data,
        });
      } else {
        this.showErrorAlert({ message: 'Error al actualizar el reto' });
      }

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async getUserChallenges(
    status: string
  ): Promise<ChallengeListElement[]> {
    try {
      this.setIsLoading(true);
      const { id } = getCurrentUserData();
      const data = await this.api.getUserChallenges(id, status);

      return this.services
        .mapChallengeEntityListToChallengeListElement(data)
        .filter(
          (challenge) => !this.services.hasUserRejectedTheInvitation(challenge)
        );
    } catch (error) {
      this.showErrorAlert({
        message: 'Error al obtener los retos del usuario',
      });

      return [];
    } finally {
      this.setIsLoading(false);
    }
  }

  public async postChallengeProgressEntry(
    challengeProgressEntry: ChallengeProgressEntry
  ) {
    const result = await this.api.postChallengeProgress(challengeProgressEntry);

    return result;
  }

  public async deleteChallenge(id: string) {
    try {
      this.showLoadingScreen();
      const result = await this.api.archiveChallenge(id);
      this.showSuccessAlert({ message: 'Reto Eliminado' });

      return result;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al eliminar reto' });

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async completeChallenge(id: string) {
    try {
      this.showLoadingScreen();
      const result = await this.api.completeChallenge(id);
      this.showSuccessAlert({ message: 'Reto Cerrado' });

      return result;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al cerrar reto' });

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  private getModuleToDisplay() {
    const view = this.getURLParams().get('view');
    if (view === 'HOME') {
      this.view = 'HOME';

      return;
    }
    if (view === 'COMPLETED') {
      this.view = 'COMPLETED';

      return;
    }
    if (view === 'INVITATION') {
      const challengeId = this.getURLParams().get('challengeId');
      if (challengeId) {
        this.view = 'INVITATION';
        this.challengeId = challengeId;

        return;
      }
    }
    this.showErrorAlert({ message: 'URL Invalida' });
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'challenges-container'
      );
      render(
        () => (
          <>
            <ChallengesProvider context={this}>
              {this.view === 'HOME' && <ChallengesContainer />}
              {this.view === 'COMPLETED' && <CompletedChallenges />}
              {this.view === 'INVITATION' && (
                <ChallengeGroupInvitation
                  context={this}
                  challengeId={this.challengeId}
                />
              )}
            </ChallengesProvider>
          </>
        ),
        userProfileContainer
      );
    }
  }
}
