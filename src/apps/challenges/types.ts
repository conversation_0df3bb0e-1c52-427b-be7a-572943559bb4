import {
  Challenge,
  ChallengesParticipanTypes,
  ChallengesProgressType,
  GoalType,
  PreConfiguredChallengeTypes,
  PreConfiguredChallengeTypesName,
} from 'entities/Challenge';
import { User } from '../../shared/components/user/user-search-bar';
import { Accessor, Setter } from 'solid-js';

export type PageProps<T> = {
  context: T;
};
export type ChallengeDataProps = {
  challenge: ChallengeListElement;
};
export type NewChallengeProps = {
  participanType: ChallengesParticipanTypes | undefined;
  preConfiguredChallenge: PreConfiguredChallenge | undefined;
};
export type PreConfiguredChallenge = {
  id: PreConfiguredChallengeTypes;
  name: PreConfiguredChallengeTypesName;
  unit?: GoalUnit;
};
export type GoalUnit = {
  id: GoalUnitIdOption;
  name: string;
  extendedName: string;
};
export type GoalUnitIdOption =
  | 'KG'
  | 'LB'
  | 'KCAL'
  | 'STEPS'
  | 'DAYS'
  | 'WEEKS'
  | 'HOURS'
  | 'MINUTES'
  | 'ML';
export type ChallengeHomeProps = {
  userChallenges: Accessor<ChallengeListElement[]>;
  setUserChallenges: Setter<ChallengeListElement[]>;
  setShowNewChallengeModal: Setter<boolean>;
};
export type Views = 'TABLE' | 'FORM' | 'DETAIL' | 'EDIT';
export type NewChallengeFormDTO = Omit<
  Challenge,
  | 'id'
  | 'updatedAt'
  | 'createdAt'
  | 'participants'
  | 'timeProgress'
  | 'challengeProgress'
> & {
  participants?: User[];
};
export type EditChallengeFormDTO = Omit<
  Challenge,
  | 'id'
  | 'updatedAt'
  | 'createdAt'
  | 'participants'
  | 'timeProgress'
  | 'challengeProgress'
> & {
  participants?: User[];
};
export type UpdateChallengeFormDTO = Omit<
  Challenge,
  | 'updatedAt'
  | 'createdAt'
  | 'participants'
  | 'timeProgress'
  | 'challengeProgress'
> & {
  participants?: User[];
};
export type UserSearchBar = User;
export type ChallengeListElement = Omit<Challenge, 'participants'> & {
  time: string;
  lastUpdate: string;
  participants: ParticipantElement[];
  goalUnitHuman: string;
  challengeProgressTypeHuman: string;
};
export type ParticipantElement = Omit<User, 'profilePicture'> & {
  participantId: string;
  participantStatus: string;
  participantStatusHuman: string;
};
export type ParticipantStatusOptions =
  | 'INVITED'
  | 'DECLINED'
  | 'ACCEPTED'
  | 'KICKED_OUT';
export type ChallengeProgressModalProps = {
  challenge: Accessor<ChallengeListElement>;
  showAddProgressModal: Accessor<boolean>;
  setShowAddProgressModal: Setter<boolean>;
  callbackAfterUpdate?: () => void;
};
export type GroupChallengeProgressResponse = {
  winner: GroupChallengeProgress | undefined;
  leaderboard: GroupChallengeProgress[];
};
export type LeaderboardData = {
  winner: GroupChallengeProgress | undefined;
  leaderboard: LeaderBoardTableData[];
};
// eslint-disable-next-line @typescript-eslint/naming-convention
export interface GroupChallengeProgress {
  userId: string;
  userName: string;
  userEmail: string;
  initialProgress: number;
  overallProgress: number;
  hasAchievedGoal: boolean;
}
// eslint-disable-next-line @typescript-eslint/naming-convention
export interface LeaderBoardTableData extends GroupChallengeProgress {
  position: string;
}
export type UpdateChallengeParticipationInitialValueDTO = {
  challengeId: string;
  userId: string;
  initialValue: string;
};
export type PreConfiguredChallengeOption = {
  PROGRESS_TYPE: {
    ACCUMULATIVE: boolean;
    FINAL_RESULT: boolean;
    HABIT: boolean;
  };
  FINAL_RESULT: GoalType;
  ACCUMULATIVE: GoalType;
  HABIT: GoalType;
  UNIT: { id: string; EXTENDED_NAME: string };
  id: PreConfiguredChallengeTypes | undefined;
  progressType: ChallengesProgressType;
};
