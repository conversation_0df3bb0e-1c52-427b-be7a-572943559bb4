import { PreConfiguredChallengeTypes } from 'entities/Challenge';
import { PreConfiguredChallengeOption } from '../types';
import { PREDEFINED_CHALLENGES } from '../../../shared/libraries/challenge-library';

export const usePredefinedChallenge = (
  challengeType: PreConfiguredChallengeTypes | undefined
): PreConfiguredChallengeOption => {
  const type = challengeType || 'OTHER';
  const challengeProgressType = PREDEFINED_CHALLENGES[type].PROGRESS_TYPE;
  const getProgressType = () => {
    if (challengeProgressType.FINAL_RESULT) {
      return 'FINAL_RESULT';
    }
    if (challengeProgressType.ACCUMULATIVE) {
      return 'ACCUMULATIVE';
    }
    if (challengeProgressType.HABIT) {
      return 'HABIT';
    }

    return 'FINAL_RESULT';
  };

  return {
    id: challengeType,
    progressType: getProgressType(),
    ...PREDEFINED_CHALLENGES[type],
  };
};
