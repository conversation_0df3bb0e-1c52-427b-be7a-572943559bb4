import {
  TableControlHeader,
  TableHeader,
} from '../../../shared/components/table/types';
import { LeaderBoardTableData } from '../types';
import { useScreenWidth } from '../../../shared/hooks/use-screen-width';

export const useLeaderBoardTable = (props: {
  id: string;
  setWinnerCallback: (userId: string, id: string) => Promise<void>;
}) => {
  const screenWidth = useScreenWidth();
  const tableHeaders: TableHeader[] = [
    {
      name: 'position',
      title: 'Posición',
      type: 'text',
      width: 10,
      cssClass: 'position-value',
    },
    {
      name: 'userName',
      title: 'Usuario',
      type: 'text',
      width: screenWidth() > 900 ? 300 : 110,
    },
    {
      name: 'overallProgress',
      title: 'Progreso',
      type: 'number',
      width: 10,
    },
  ];
  const tableControls: TableControlHeader[] = [
    {
      name: 'winner',
      title: '<PERSON>anador',
      icon: 'bi bi-trophy-fill',
      controlType: 'primary',
      callback: async (row: LeaderBoardTableData) => {
        const { userId } = row;
        await props.setWinnerCallback(userId, props.id);
      },
    },
  ];

  return { tableHeaders, tableControls };
};
