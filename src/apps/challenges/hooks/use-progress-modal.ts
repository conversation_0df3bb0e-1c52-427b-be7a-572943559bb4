import { Accessor, createEffect, createSignal, onCleanup } from 'solid-js';

export const useProgressModal = (show: Accessor<boolean>) => {
  const [isChecked, setIsChecked] = createSignal(false);
  const [progressValue, setProgressValue] = createSignal<number>(0);
  const [fileList, setFileList] = createSignal<File[]>([]);
  const [challengeDates, setChallengeDates] = createSignal<Date[]>([]);
  const [selectedItem, setSelectedItem] = createSignal<string>('');
  const clearData = () => {
    setIsChecked(false);
    setFileList([]);
    setProgressValue(0);
  };
  createEffect(() => {
    if (show() === true) {
      clearData();
    }
  });
  onCleanup(() => {
    clearData();
  });

  return {
    isChecked,
    setIsChecked,
    progressValue,
    setProgressValue,
    fileList,
    setFileList,
    challengeDates,
    setChallengeDates,
    selectedItem,
    setSelectedItem,
  };
};
