import { createSignal, onCleanup } from 'solid-js';
import { ChallengeListElement } from '../types';
import { User } from '../../../shared/components/user/user-search-bar';
import { ChallengeNotificationOptions } from 'entities/Challenge';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { useScreenWidth } from '../../../shared/hooks/use-screen-width';
import { User as UserEntity } from 'entities/User';

export const useChallengeForm = (challengeData?: ChallengeListElement) => {
  const screenWidth = useScreenWidth();
  const [activeUserList, setActiveUserList] = createSignal<UserEntity[]>([]);
  const [selectedUser, setSelectedUser] = createSignal<User[]>(
    challengeData?.participants.map((p) => {
      return {
        id: p.id,
        name: p.name,
        email: p.email,
        profilePicture: '',
      };
    }) || []
  );
  const [hasNotifications, setHsNotifications] = createSignal<boolean>(
    challengeData?.isRemainderActive !== undefined
      ? challengeData?.isRemainderActive
      : true
  );
  const [isEvidenceRequired, setIsEvidenceRequired] = createSignal<boolean>(
    challengeData?.isEvidenceRequired !== undefined
      ? challengeData?.isEvidenceRequired
      : true
  );
  const [notificationOption, setNotificationOption] =
    createSignal<ChallengeNotificationOptions>(
      challengeData?.remainderType || 'DAILY'
    );
  const isUserOwnerOfThisChallenge = (
    challenge: ChallengeListElement
  ): boolean => {
    const { id } = getCurrentUserData();

    return challenge.userId === id ?? true;
  };
  const handleRadioChange = (event: Event) => {
    setNotificationOption(
      (event.target as HTMLSelectElement).value as ChallengeNotificationOptions
    );
  };
  const handleHasNotificationRadioChange = (event: Event) => {
    const hasChallengeNotification =
      (event.target as HTMLSelectElement).value &&
      (event.target as HTMLSelectElement).value == 'Si'
        ? true
        : false;
    setHsNotifications(hasChallengeNotification);
  };
  const handleIsEvidenceRequired = (event: Event) => {
    const isEvidenceRequired =
      (event.target as HTMLSelectElement).value &&
      (event.target as HTMLSelectElement).value == 'Si'
        ? true
        : false;
    setIsEvidenceRequired(isEvidenceRequired);
  };
  onCleanup(() => {
    setSelectedUser([]);
    setActiveUserList([]);
    setHsNotifications(false);
    setNotificationOption('DAILY');
  });

  return {
    screenWidth,
    selectedUser,
    setSelectedUser,
    activeUserList,
    setActiveUserList,
    isEvidenceRequired,
    hasNotifications,
    setHsNotifications,
    notificationOption,
    setNotificationOption,
    isUserOwnerOfThisChallenge,
    handleRadioChange,
    handleHasNotificationRadioChange,
    handleIsEvidenceRequired,
  };
};
