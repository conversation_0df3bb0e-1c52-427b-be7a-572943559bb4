import { ChallengeProgress } from 'entities/ChallengeProgress';
import {
  TableControlHeader,
  TableHeader,
} from '../../../shared/components/table/types';

export const useProgressTable = (props: {
  progressType: string;
  challengeParticipantType: string;
  isDeleteEnabled: boolean;
  detailCallback: (row: ChallengeProgress) => void;
  deleteCallback?: (row: ChallengeProgress) => Promise<void>;
}) => {
  const headers: TableHeader[] = [
    {
      name: 'value',
      title: 'Valor',
      type: 'text',
      width: 80,
    },
    {
      name: 'unit',
      title: 'unidad',
      type: 'text',
      width: 80,
    },
  ];
  if (props.progressType === 'HABIT') {
    headers.shift();
    headers.push({
      name: 'date',
      title: 'Fecha',
      type: 'text',
      width: 100,
    });
  }
  if (props.challengeParticipantType === 'GROUP') {
    headers.push({
      name: 'userName',
      title: 'Usuario',
      type: 'text',
      width: 80,
    });
  }
  headers.push({
    name: 'lastUpdate',
    title: 'Regis<PERSON>',
    type: 'text',
    width: 120,
  });
  const control: TableControlHeader[] = [
    {
      name: 'detail',
      title: 'Evidencia',
      icon: 'bi bi-image',
      controlType: 'primary',
      callback: (row: ChallengeProgress) => {
        props.detailCallback(row);
      },
    },
  ];
  if (props.isDeleteEnabled) {
    control.push({
      name: 'eliminar',
      title: 'Eliminar',
      icon: 'bi bi-trash-fill',
      controlType: 'danger',
      callback: async (row: ChallengeProgress) => {
        props.deleteCallback && (await props.deleteCallback(row));
      },
    });
  }

  return { headers, control };
};
