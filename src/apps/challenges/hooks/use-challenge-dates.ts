import { DateServices } from '../../../shared/services/utils/date-services';

export const useChallengeDates = ({
  startDate,
  endDate,
}: {
  startDate: string;
  endDate?: string;
}) => {
  const end = endDate ? new Date(endDate) : new Date();
  end.setDate(end.getDate() + 1);
  if (new Date(startDate) > end) {
    return [];
  }
  const dates = DateServices.getDatesBetween(startDate, end.toISOString());

  return dates.map((date) => new Date(date)).filter((date) => date <= end);
};
