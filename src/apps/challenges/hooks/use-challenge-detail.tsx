import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { ChallengeListElement } from '../types';
import { usePredefinedChallenge } from './use-predefined-challenge';
import logo from '../../../assets/media/icons/blank.png';

export const useChallengeDetail = () => {
  const renderGoal = (challenge: ChallengeListElement) => {
    const challengeOptions = usePredefinedChallenge('OTHER');
    let goalTypeTitle = '';
    switch (challenge.goalType) {
      case 'ADD':
        goalTypeTitle = challengeOptions[challenge.progressType].ADD;
        break;
      case 'REDUCE':
        goalTypeTitle = challengeOptions[challenge.progressType].REDUCE;
        break;
    }

    return (
      <h4 class="challenge-detail-card-subtitle font-weight-bold">{`${
        challenge.progressType !== 'HABIT' ? goalTypeTitle : ''
      } ${challenge.goal} ${challenge.goalUnitHuman}`}</h4>
    );
  };
  const renderEvidenceType = (challenge: ChallengeListElement) => {
    return (
      <h4 class="challenge-detail-card-subtitle font-weight-bold">{`${
        challenge.isEvidenceRequired ? 'Obligatoria' : 'Opcional'
      } `}</h4>
    );
  };
  const renderChallengeProgressType = (challenge: ChallengeListElement) => {
    const PROGRESS_TYPE = {
      FINAL_RESULT: 'Alcanzar un objetivo',
      ACCUMULATIVE: 'Acumulativo',
      HABIT: 'Hábito',
    };

    return (
      <h4 class="challenge-detail-card-subtitle font-weight-bold">{`${
        PROGRESS_TYPE[challenge.progressType]
      } `}</h4>
    );
  };
  const renderStartDate = (challenge: ChallengeListElement) => {
    const date = new Date(challenge.startDate);
    date.setDate(date.getDate() + 1);

    return (
      <>
        <h5 class="challenge-detail-card-date font-weight-light">{`Del:`}</h5>
        <h4 class="challenge-detail-card-date font-weight-bold">{`${date.toLocaleDateString(
          'es-MX'
        )}`}</h4>
      </>
    );
  };
  const renderEndDate = (challenge: ChallengeListElement) => {
    const date = new Date(challenge.endDate);
    date.setDate(date.getDate() + 1);

    return (
      <>
        <h5 class="challenge-detail-card-date font-weight-light">{`Hasta:`}</h5>
        <h4 class="challenge-detail-card-date font-weight-bold">{`${date.toLocaleDateString(
          'es-MX'
        )}`}</h4>
      </>
    );
  };
  const handleImageLoadError = (event: Event) => {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = logo;
  };
  const isUserOwnerOfThisChallenge = (
    challenge: ChallengeListElement
  ): boolean => {
    const { id } = getCurrentUserData();

    return challenge.userId === id ?? true;
  };

  return {
    renderGoal,
    renderEvidenceType,
    renderChallengeProgressType,
    renderStartDate,
    renderEndDate,
    handleImageLoadError,
    isUserOwnerOfThisChallenge,
  };
};
