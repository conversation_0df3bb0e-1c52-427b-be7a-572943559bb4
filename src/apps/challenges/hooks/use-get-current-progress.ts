import { ChallengeProgress } from 'entities/ChallengeProgress';
import { createEffect, createSignal } from 'solid-js';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { ChallengeListElement } from '../types';

export const useActualProgress = (
  challenge: ChallengeListElement,
  challengeProgress: ChallengeProgress[]
) => {
  const [actualProgress, setActualProgress] = createSignal<string>('0');
  const getActualProgress = () => {
    const data = challengeProgress.filter(
      (data) => data.userId === getCurrentUserData().id
    );
    switch (challenge.progressType) {
      case 'ACCUMULATIVE':
        setActualProgress(
          `${data.reduce((prev, current) => prev + current.value, 0)} ${
            challenge.goalUnitHuman
          }`
        );
        break;
      case 'FINAL_RESULT':
        setActualProgress(
          data.length > 0
            ? `${
                data.sort(
                  (a, b) =>
                    new Date(b.createdAt).getTime() -
                    new Date(a.createdAt).getTime()
                )[0].value
              } ${challenge.goalUnitHuman}`
            : '0'
        );
        break;
      default:
        setActualProgress('0');
        break;
    }
  };
  createEffect(() => {
    if (challenge && challengeProgress) {
      getActualProgress();
    }
  });

  return actualProgress;
};
