import { TableControlHeader, TableHeader } from 'shared/components/table/types';
import { createSignal, onMount } from 'solid-js';
import { useChallengesContext } from '../components/context';
import { ChallengeListElement } from '../types';

const useCompleted = () => {
  const context = useChallengesContext();
  const [key, setKey] = createSignal('ACTIVE');
  const [view, setView] = createSignal<string>('TABLE');
  const [isLoading, setIsLoading] = createSignal(true);
  const [activeChallenge, setActiveChallenge] =
    createSignal<ChallengeListElement>();
  const [challengeList, setChallengeList] = createSignal<
    ChallengeListElement[]
  >([]);
  const tableHeaders: TableHeader[] = [
    {
      name: 'name',
      title: 'Nombre',
      type: 'text',
      width: 180,
    },
    {
      name: 'challengeProgressTypeHuman',
      title: 'Tipo',
      type: 'text',
      width: 120,
    },
    {
      name: 'goal',
      title: 'Meta',
      type: 'text',
      width: 80,
    },
    {
      name: 'goalUnitHuman',
      title: 'Unidad',
      type: 'text',
      width: 80,
    },
    {
      name: 'lastUpdate',
      title: 'Registro',
      type: 'text',
      width: 150,
    },
  ];
  const tableControls: TableControlHeader[] = [
    {
      name: 'detail',
      title: 'Detalles',
      controlType: 'primary',
      callback: (row: ChallengeListElement) => {
        setActiveChallenge(row);
        setView('DETAIL');
      },
    },
  ];
  const getCompletedChallengeList = async () => {
    const challengeList = await context
      ?.parentContext()
      .getUserChallenges('COMPLETED');
    setChallengeList(challengeList || []);
    setIsLoading(false);
  };
  onMount(async () => {
    await getCompletedChallengeList();
  });

  return {
    tableHeaders,
    tableControls,
    key,
    view,
    isLoading,
    setKey,
    setView,
    setIsLoading,
    challengeList,
    setChallengeList,
    activeChallenge,
  };
};
export default useCompleted;
