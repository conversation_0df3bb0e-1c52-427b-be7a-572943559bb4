import { createEffect, createSignal } from 'solid-js';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { ChallengeServices } from '../services/challenge-services';
import { createStore } from 'solid-js/store';

export type HabitChallengeProgress = {
  dayName: string;
  monthName: string;
  dayNumber: number;
  isChecked: boolean;
  evidence?: string;
};
export const useUserHabitChallengeProgress = (
  userChallengeProgress: ChallengeProgress[],
  dates: string[]
) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [userHabitChallengeProgress, setUserHabitChallengeProgress] =
    createStore<HabitChallengeProgress[]>([]);
  const getUserChallengeProgress = () => {
    if (!Array.isArray(userChallengeProgress)) return;
    setIsLoading(true);
    const combinedChallengeData = combineChallengeWithChallengeProgress(
      userChallengeProgress,
      dates
    );
    setUserHabitChallengeProgress(combinedChallengeData);
    setIsLoading(false);
  };
  createEffect(() => {
    getUserChallengeProgress();
  });

  return { isLoading, userHabitChallengeProgress };
};
const combineChallengeWithChallengeProgress = (
  userChallengeProgress: ChallengeProgress[],
  dates: string[]
): HabitChallengeProgress[] => {
  return dates.map((stringDate) => {
    const challengeProgressListData = getChallengeProgressForDate(
      userChallengeProgress,
      stringDate
    );
    const date = new Date(stringDate);

    return {
      dayName: date.toLocaleDateString('es-MX', {
        weekday: 'long',
      }),
      monthName: date.toLocaleDateString('es-MX', {
        month: 'long',
      }),
      dayNumber: date.getDate(),
      isChecked: ChallengeServices.IsProgressPresentForThatDate({
        date: stringDate,
        userChallengeProgress,
      }),
      evidence: challengeProgressListData
        ? challengeProgressListData.evidence
        : undefined,
    };
  });
};
const getChallengeProgressForDate = (
  userChallengeProgress: ChallengeProgress[],
  date: string
) => {
  return userChallengeProgress
    .filter((data) => data.userId === getCurrentUserData().id)
    .find((challengeProgress) => {
      const challengeProgressDate = new Date(challengeProgress.createdAt);
      const searchDate = new Date(date);

      return (
        challengeProgressDate.getFullYear() === searchDate.getFullYear() &&
        challengeProgressDate.getMonth() === searchDate.getMonth() &&
        challengeProgressDate.getDate() === searchDate.getDate()
      );
    });
};
