import { createEffect, createSignal } from 'solid-js';
import { ChallengeProgress } from 'entities/ChallengeProgress';

export const useProgress = () => {
  const [show, setShow] = createSignal(false);
  const [evidence, setEvidence] = createSignal<string>();
  const [showAddProgressModal, setShowAddProgressModal] = createSignal(false);
  const [progressData, setProgressData] = createSignal<ChallengeProgress[]>([]);
  createEffect(() => {
    if (!show()) {
      setEvidence();
    }
  });

  return {
    showAddProgressModal,
    setShowAddProgressModal,
    progressData,
    setProgressData,
    show,
    setShow,
    evidence,
    setEvidence,
  };
};
