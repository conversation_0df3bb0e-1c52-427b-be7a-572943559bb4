import { ChallengesParticipanTypes } from 'entities/Challenge';
import { createSignal, onCleanup } from 'solid-js';
import { ChallengeListElement, PreConfiguredChallenge } from '../types';

export const useChallengeContainer = () => {
  const [showNewChallengeModal, setShowNewChallengeModal] = createSignal(false);
  const [challengesParticipantTypes, setChallengesParticipantTypes] =
    createSignal<ChallengesParticipanTypes>();
  const [userChallenges, setUserChallenges] = createSignal<
    ChallengeListElement[]
  >([]);
  const [preConfiguredChallenge, setPreConfiguredChallenge] =
    createSignal<PreConfiguredChallenge>();
  onCleanup(() => {
    setChallengesParticipantTypes();
  });

  return {
    showNewChallengeModal,
    setShowNewChallengeModal,
    challengesParticipantTypes,
    setChallengesParticipantTypes,
    userChallenges,
    setUserChallenges,
    preConfiguredChallenge,
    setPreConfiguredChallenge,
  };
};
