import { Services } from '../../shared/services/app-base/service-base';
import {
  Challenge,
  ChallengeNotificationOptions,
  ChallengesParticipanTypes,
  ChallengesProgressType,
} from 'entities/Challenge';
import {
  ChallengeListElement,
  GoalUnitIdOption,
  GroupChallengeProgressResponse,
  LeaderboardData,
  ParticipantStatusOptions,
} from './types';
import {
  useCalculateTimeDifference,
  useCalculateDaysDifference,
} from '../../shared/hooks/time-difference';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { User } from '../../shared/components/user/user-search-bar';
import { getCurrentUserData } from '../../shared/services/user/user-session-management';

type ChallengeProgressEvidenceValidation = {
  isValid: boolean;
  file: File | null;
};
export class ChallengesServices extends Services {
  public getUserChallengeInvitationId(challenge: ChallengeListElement) {
    const { id: userId } = getCurrentUserData();
    const invitation = challenge.participants.find(
      (participant) => participant.id === userId
    );

    return invitation?.participantId || '';
  }

  public isUserAwaitingToRespondInvitationToChallenge(
    challenge: ChallengeListElement
  ) {
    const { id: userId } = getCurrentUserData();
    const invitation = challenge.participants.find(
      (participant) => participant.id === userId
    );

    return (
      invitation !== undefined && invitation.participantStatus === 'INVITED'
    );
  }

  public hasUserRejectedTheInvitation(challenge: ChallengeListElement) {
    const { id: userId } = getCurrentUserData();
    const invitation = challenge.participants.find(
      (participant) => participant.id === userId
    );

    return (
      this.isChallengeAGroupChallenge(challenge) &&
      (!invitation || invitation.participantStatus === 'REJECTED')
    );
  }

  public isChallengeAGroupChallenge(challenge: ChallengeListElement) {
    return challenge.challengeParticipantType === 'GROUP';
  }

  public mapChallengeEntityListToChallengeListElement(
    challengeList: Challenge[]
  ): ChallengeListElement[] {
    return challengeList.map((challenge) =>
      this.mapChallengeEntityToChallengeListElement(challenge)
    );
  }

  public mapChallengeEntityToChallengeListElement(
    challenge: Challenge
  ): ChallengeListElement {
    const getParticipantStatusHuman = (
      participantStatus: ParticipantStatusOptions
    ) => {
      const PARTICIPANT_STATUS_LIBRARY = {
        ['INVITED']: 'Invitado',
        ['DECLINED']: 'Declinado',
        ['ACCEPTED']: 'Aceptado',
        ['KICKED_OUT']: 'Fuera',
      };

      return PARTICIPANT_STATUS_LIBRARY[participantStatus] || '-';
    };
    const getGoalUnitHuman = (unit: GoalUnitIdOption) => {
      const GOAL_UNIT_LIBRARY = {
        ['KG']: 'Kg',
        ['LB']: 'Lb',
        ['KCAL']: 'Kcal',
        ['STEPS']: 'Pasos',
        ['DAYS']: 'Días',
        ['WEEKS']: 'Semanas',
        ['HOURS']: 'Horas',
        ['MINUTES']: 'Minutos',
        ['ML']: 'Mililitros',
        ['UNITS']: 'Unidades',
      };

      return GOAL_UNIT_LIBRARY[unit] || '-';
    };
    const getHumanChallengeProgressType = (challenge: Challenge) => {
      const PROGRESS_TYPE_LIBRARY = {
        ['HABIT']: 'Hábito',
        ['FINAL_RESULT']: 'Alcanzar un objetivo',
        ['ACCUMULATIVE']: 'Acumulativo',
      };
      const { progressType: type } = challenge;

      return PROGRESS_TYPE_LIBRARY[type];
    };

    return {
      ...challenge,
      participants:
        challenge.participants?.map((participant) => {
          return {
            participantId: participant.id,
            id: participant.userId,
            name: participant.userName,
            email: participant.userEmail,
            participantStatus: participant.participantStatus,
            participantStatusHuman: getParticipantStatusHuman(
              participant.participantStatus as ParticipantStatusOptions
            ),
          };
        }) || [],
      challengeProgressTypeHuman: getHumanChallengeProgressType(challenge),
      goalUnitHuman: getGoalUnitHuman(challenge.goalUnit as GoalUnitIdOption),
      time: `${useCalculateTimeDifference(
        challenge.startDate,
        challenge.endDate
      )}`,
      lastUpdate: `Última actualización hace ${useCalculateDaysDifference(
        challenge.updatedAt
      )} días`,
    };
  }

  public combineProgressDataWithChallengeData(
    progressList: ChallengeProgress[],
    challenge: ChallengeListElement
  ) {
    return progressList.map((progress) => {
      const getRemaining = () => {
        if (challenge.goalType === 'ADD')
          return progress.value > challenge.goal
            ? 0
            : challenge.goal - progress.value;
        if (challenge.goalType === 'REDUCE')
          return progress.value < challenge.goal
            ? 0
            : progress.value - challenge.goal;

        return progress.value;
      };
      const lastUpdateValue = useCalculateDaysDifference(progress.updatedAt);

      return {
        id: progress.id,
        challengeId: progress.challengeId,
        userId: progress.userId,
        userName: progress.userName,
        value: progress.value,
        remaining: getRemaining(),
        unit: challenge.goalUnitHuman,
        evidence: progress.evidence,
        lastUpdate: `Hace ${lastUpdateValue} día${
          lastUpdateValue > 1 ? 's' : ''
        }`,
        date: new Date(progress.createdAt).toLocaleDateString(),
        challengeProgressStatus: progress.challengeProgressStatus,
        challengeProgressType: progress.challengeProgressType,
        updatedAt: progress.updatedAt,
        createdAt: progress.createdAt,
      };
    });
  }

  public mapChallengesProgressToChallengesProgressElement(
    challengeProgressList: ChallengeProgress[]
  ) {
    return challengeProgressList.map((challengeProgress) => {
      return {
        id: challengeProgress.id,
        challengeId: challengeProgress.challengeId,
        userId: challengeProgress.userId,
        userName: challengeProgress.userName,
        value: challengeProgress.value,
        evidence: challengeProgress.evidence,
        challengeProgressStatus: challengeProgress.challengeProgressStatus,
        updatedAt: challengeProgress.updatedAt,
        createdAt: challengeProgress.createdAt,
      };
    });
  }

  public mapGroupChallengeProgressResponseToLeaderboardData(
    data: GroupChallengeProgressResponse
  ): LeaderboardData {
    const leaderboard = data.leaderboard.map((participant, i) => {
      return {
        position: `#${i + 1}`,
        ...participant,
      };
    });

    return {
      winner: data.winner,
      leaderboard,
    };
  }

  public isChallengeProgressEvidenceValid(
    isEvidenceRequired: boolean,
    evidenceFile: File | undefined
  ): ChallengeProgressEvidenceValidation {
    if (
      evidenceFile instanceof File &&
      (evidenceFile.name || evidenceFile.size || evidenceFile.type)
    ) {
      return {
        isValid: true,
        file: evidenceFile,
      };
    } else if (!isEvidenceRequired || !(evidenceFile instanceof File)) {
      return {
        isValid: true,
        file: null,
      };
    }

    return {
      isValid: false,
      file: null,
    };
  }

  public mapFormSubmitEventChallengeData(
    event: Event,
    participantType: ChallengesParticipanTypes,
    progressType: ChallengesProgressType,
    participantList = [] as User[],
    isRemainderActive: boolean,
    remainderType: ChallengeNotificationOptions,
    isEvidenceRequired: boolean
  ) {
    const {
      name,
      description,
      reward,
      goalType,
      goal,
      goalUnit,
      startDate,
      endDate,
      initialValue,
    } =
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      event.target as any;
    const convertToISOString = (dateString: string) => {
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(Date.UTC(year, month - 1, day));
      const isoString = date.toISOString();

      return isoString;
    };
    const challenge = {
      name: name.value,
      description: description.value,
      reward: reward.value,
      goalType: goalType.value,
      goal: goal.value,
      goalUnit: goalUnit.value || 'UNITS',
      startDate: convertToISOString(startDate.value),
      endDate: convertToISOString(endDate.value),
      initialValue:
        participantType === 'INDIVIDUAL' && progressType === 'FINAL_RESULT'
          ? initialValue.value
          : 0,
      progressType: progressType,
      challengeParticipantType: participantType,
      participants: participantType === 'GROUP' ? participantList : [],
      isRemainderActive,
      ...(isRemainderActive && {
        remainderType,
      }),
      isEvidenceRequired,
    };

    return challenge;
  }
}
