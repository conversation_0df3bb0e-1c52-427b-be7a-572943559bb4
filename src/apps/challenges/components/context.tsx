import {
  createSignal,
  createContext,
  useContext,
  Accessor,
  Setter,
} from 'solid-js';
import { ChallengesPage } from '../main';
import { ChallengeListElement, Views } from '../types';
import { DEFAULT_CHALLENGE_DATA } from '../../../shared/libraries/challenge-library';

interface IChallengesContextModel {
  parentContext: Accessor<ChallengesPage>;
  setParentContext: Setter<ChallengesPage>;
  view: Accessor<Views>;
  setView: Setter<Views>;
  challengeDetailContent: Accessor<ChallengeListElement>;
  setChallengeDetailContent: Setter<ChallengeListElement>;
  challengeEditData: Accessor<ChallengeListElement | undefined>;
  setChallengeEditData: Setter<ChallengeListElement | undefined>;
  isFormUseForEdit: Accessor<boolean>;
  setIsFormUseForEdit: Setter<boolean>;
}
const ChallengesContext = createContext<IChallengesContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function ChallengesProvider(props: any) {
  const [parentContext, setParentContext] = createSignal<ChallengesPage>(
    props.context
  );
  const [view, setView] = createSignal<Views>('TABLE');
  const [challengeDetailContent, setChallengeDetailContent] =
    createSignal<ChallengeListElement>(DEFAULT_CHALLENGE_DATA);
  const [challengeEditData, setChallengeEditData] =
    createSignal<ChallengeListElement>();
  const [isFormUseForEdit, setIsFormUseForEdit] = createSignal<boolean>(false);
  const value: IChallengesContextModel = {
    parentContext,
    setParentContext,
    view,
    setView,
    challengeDetailContent,
    setChallengeDetailContent,
    isFormUseForEdit,
    setIsFormUseForEdit,
    challengeEditData,
    setChallengeEditData,
  };

  return (
    <ChallengesContext.Provider value={value}>
      {props.children}
    </ChallengesContext.Provider>
  );
}
export function useChallengesContext(): IChallengesContextModel | undefined {
  return useContext(ChallengesContext);
}
