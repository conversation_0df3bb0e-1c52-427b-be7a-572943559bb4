import { <PERSON>, <PERSON>, <PERSON><PERSON>, Row } from 'solid-bootstrap';
import { createEffe<PERSON>, create<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Show } from 'solid-js';
import { ChallengeListElement } from '../../types';
import ImageLoader from '../../../../shared/components/uploader/image-loader';
import { useProgress } from '../../hooks/use-progress';
import LiderBoard from '../home/<USER>';
import { getCurrentUserData } from '../../../../shared/services/user/user-session-management';
import { ChallengeHabitProgressDetail } from '../../../../shared/components/challenge/challenge-habit-progress-detail';
import ChallengeSide from '../../../../shared/components/challenge/challenge-side';
import { useScreenWidth } from '../../../../shared/hooks/use-screen-width';
import { useActualProgress } from '../../hooks/use-get-current-progress';
import '../../styles/detail-style.css';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { useChallengesContext } from '../context';
import { useProgressTable } from '../../hooks/use-progress-table';
import { ChallengeProgressTable } from '../../../../shared/components/challenge/challenge-progress-table';
import { useChallengeProgress } from '../../../../shared/infra/hooks/use-challenge-progress';
import { ErrorAlert } from '../../../../shared/components/alert/error-alert';
import { createStore } from 'solid-js/store';
import { ChallengeServices } from '../../../../apps/challenges/services/challenge-services';

type ChallengeDetailProps = {
  challenge: ChallengeListElement;
  setView: Setter<string>;
};
const ChallengeHistoryDetail = (props: ChallengeDetailProps) => {
  const context = useChallengesContext();
  const screenWidth = useScreenWidth();
  const challenge = props.challenge;
  const { id } = getCurrentUserData();
  const isOwner = challenge.userId === id;
  const { show, setShow, evidence, setEvidence } = useProgress();
  const {
    isLoading: isLoadingChallengeProgress,
    error: errorChallengeProgress,
    challengeProgress,
  } = useChallengeProgress({ id: props.challenge.id });
  const isHabit = createMemo(() => challenge.progressType === 'HABIT');
  const isGroupChallenge = createMemo(
    () => challenge.challengeParticipantType === 'GROUP'
  );
  const [challengeWithProgressData, setChallengeWithProgressData] = createStore(
    ChallengeServices.CombineProgressDataWithChallengeData(
      [...challengeProgress],
      props.challenge
    )
  );
  createEffect(() => {
    setChallengeWithProgressData(
      ChallengeServices.CombineProgressDataWithChallengeData(
        [...challengeProgress],
        props.challenge
      )
    );
  });
  const actualProgress = useActualProgress(
    props.challenge,
    challengeWithProgressData
  );
  const handleDetailButtonClicked = (row: ChallengeProgress) => {
    if (!row.evidence) {
      context
        ?.parentContext()
        ?.showInfoAlert({ message: 'No hay evidencia para este registro' });

      return;
    }
    setEvidence(row.evidence);
    setShow(true);
  };
  const { headers, control } = useProgressTable({
    progressType: challenge.progressType,
    challengeParticipantType: challenge.challengeParticipantType,
    isDeleteEnabled: false,
    detailCallback: handleDetailButtonClicked,
  });

  return (
    <>
      <Card>
        <Card.Header>
          <h1 class="card-title">Meta: {challenge.name}</h1>
          <div class="card-toolbar gap-2 gap-md-5">
            <button
              type="button"
              role="button"
              class="btn btn-sm btn-secondary btn-sm align-self-center"
              onClick={() => {
                props.setView('TABLE');
              }}
            >
              Regresar
            </button>
          </div>
        </Card.Header>
        <Card.Body>
          <Row class="py-10 px-5">
            <Col sm={12} md={6} lg={8}>
              <Show when={isHabit()}>
                <div class="mb-5">
                  <ChallengeHabitProgressDetail
                    progressData={challengeWithProgressData}
                    isLoading={isLoadingChallengeProgress}
                    challenge={props.challenge}
                  />
                </div>
              </Show>
              <div class={`${screenWidth() > 900 ? 'mb-8' : 'mb-20'}`}>
                <ErrorAlert error={[errorChallengeProgress]} />
                <ChallengeProgressTable
                  header={headers}
                  control={control}
                  data={challengeWithProgressData}
                  isHabit={isHabit()}
                  actualProgress={actualProgress}
                  isLoading={isLoadingChallengeProgress}
                />
              </div>
              <Show when={isGroupChallenge()}>
                <LiderBoard
                  challengeId={challenge.id}
                  isCompleted={true}
                  isOwner={isOwner}
                />
              </Show>
            </Col>
            <Col sm={12} md={6} lg={4}>
              <ChallengeSide challenge={props.challenge} />
            </Col>
          </Row>
        </Card.Body>
      </Card>
      <Modal
        size="lg"
        show={show()}
        onHide={() => setShow(false)}
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header closeButton>
          <h1 class="card-title">Evidencia</h1>
        </Modal.Header>
        <Modal.Body>
          <div class="p-10 my-15">
            <ImageLoader class="w-100" src={evidence() || ''} />
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};
export default ChallengeHistoryDetail;
