import Animation from '../../../../shared/components/lottie-player/animation';
import BasicTableComponent from '../../../../shared/components/table/basic-table';
import { Tab, Tabs } from 'solid-bootstrap';
import { Match, Switch } from 'solid-js';
import useCompleted from '../../hooks/use-completed';
import ChallengeHistoryDetail from './detail';
import { ChallengeListElement } from '../../types';

const CompletedChallenges = () => {
  const {
    tableHeaders,
    tableControls,
    key,
    view,
    isLoading,
    setKey,
    setView,
    challengeList,
    activeChallenge,
  } = useCompleted();

  return (
    <>
      <Switch>
        <Match when={view() === 'TABLE'}>
          <div class="card ">
            <div class="card-header">
              <h3 class="card-title">Retos Completados</h3>
            </div>

            <div class="card-body card-toolbar">
              <Tabs
                class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6"
                id="users"
                activeKey={key()}
                onSelect={(tab) => setKey(tab as string)}
              >
                <Tab eventKey="ACTIVE" title="Completados">
                  {isLoading() === true && <Animation />}
                  {isLoading() === false && (
                    <BasicTableComponent
                      title={'Retos Completados'}
                      data={challengeList}
                      header={tableHeaders}
                      control={tableControls}
                      pageSize={50}
                      isTableStriped
                      isSearchEnabled
                    />
                  )}
                </Tab>
              </Tabs>
            </div>
          </div>
        </Match>
        <Match when={view() === 'DETAIL'}>
          <ChallengeHistoryDetail
            challenge={activeChallenge() as ChallengeListElement}
            setView={setView}
          />
        </Match>
      </Switch>
    </>
  );
};
export default CompletedChallenges;
