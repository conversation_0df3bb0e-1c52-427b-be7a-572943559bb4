import {
  Match,
  Show,
  Switch,
  createEffect,
  create<PERSON>emo,
  createSignal,
} from 'solid-js';
import { Card, Col, Form, FormGroup, Row } from 'solid-bootstrap';
import { <PERSON><PERSON> } from 'solid-headless';
import { ChallengesPage } from 'apps/challenges/main';
import ChallengeSide from '../../../../shared/components/challenge/challenge-side';
import { getCurrentUserData } from '../../../../shared/services/user/user-session-management';
import { ChallengeListElement } from '../../types';
import { useChallengeInvitation } from '../../../../shared/infra/hooks/use-challenge-invitation';
import { useChallenge } from '../../../../shared/infra/hooks/use-challenge';
import { useInvitation } from '../../../../shared/infra/hooks/use-invitation';
import { ErrorAlert } from '../../../../shared/components/alert/error-alert';
import { LoadingAlert } from '../../../../shared/components/alert/loading-alert';

const CHALLENGE_HOME_URL = '../challenges/?view=HOME';
const ChallengeGroupInvitation = (props: {
  context: ChallengesPage;
  challengeId: string;
}) => {
  const {
    context: { services },
  } = props;
  const { id: userId, name } = getCurrentUserData();
  const [hasUserAccess, setHasUserAccess] = createSignal<boolean>(false);
  const [participantId, setParticipantId] = createSignal<string>('');
  const [initialValue, setInitialValue] = createSignal<string>('');
  const successCallback = () => {
    props.context.showSuccessAlert({
      message: 'Invitación de reto actualizada',
      url: CHALLENGE_HOME_URL,
    });
  };
  const {
    isLoading: isChallengeLoading,
    error: errorFetchingChallenge,
    challenge,
  } = useChallenge({ id: props.challengeId });
  const {
    isLoading: isChallengeInvitationLoading,
    error: errorChallengeInvitation,
    updateChallengeInitialValue,
  } = useChallengeInvitation();
  const {
    isLoading: isInvitationLoading,
    error: errorInvitation,
    changeInvitationStatus,
  } = useInvitation();
  const handleSubmit = async (operation: 'REJECT' | 'ACCEPT') => {
    try {
      if (operation === 'ACCEPT' && !isInitialValueValid()) {
        throw new Error('Por favor, ingrese un valor inicial válido');
      }
      if (challenge.progressType === 'FINAL_RESULT') {
        await updateInitialValue();
      }
      await changeInvitationStatus(participantId(), operation);
      successCallback();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      props.context.showErrorAlert({
        message: error.message || 'Error al actualizar la invitación al reto',
      });
    }
  };
  const isInitialValueValid = () => {
    return (
      challenge.progressType !== 'FINAL_RESULT' ||
      (initialValue() && !isNaN(+initialValue()))
    );
  };
  const updateInitialValue = async () => {
    const dto = {
      challengeId: props.challengeId,
      userId,
      initialValue: initialValue(),
    };
    await updateChallengeInitialValue(dto);
  };
  const checkIfUserIsPendingToRespondeToInvitation = (
    challenge: ChallengeListElement
  ) => {
    return services.isUserAwaitingToRespondInvitationToChallenge(challenge);
  };
  const checkIfChallengeTypeIsValid = (challenge: ChallengeListElement) => {
    return services.isChallengeAGroupChallenge(challenge);
  };
  const challengeElementMapped = createMemo(() =>
    services.mapChallengeEntityToChallengeListElement(challenge)
  );
  const isLoading = createMemo<boolean>(
    () =>
      isChallengeLoading() ||
      isChallengeInvitationLoading() ||
      isInvitationLoading()
  );
  createEffect(async () => {
    if (!challenge.id) return;
    const isChallengeValid = checkIfChallengeTypeIsValid(
      challengeElementMapped()
    );
    const isUserValid = checkIfUserIsPendingToRespondeToInvitation(
      challengeElementMapped()
    );
    if (!isChallengeValid || !isUserValid) {
      props.context.showErrorAlert({
        message: 'Esta invitación  no se encuentra disponible',
        url: CHALLENGE_HOME_URL,
      });

      return;
    }
    const invitationId = services.getUserChallengeInvitationId(
      challengeElementMapped()
    );
    setParticipantId(invitationId);
    setHasUserAccess(true);
  });

  return (
    <>
      <Card>
        <ErrorAlert
          error={[
            errorFetchingChallenge,
            errorChallengeInvitation,
            errorInvitation,
          ]}
        />
        <LoadingAlert
          showLoadingAnimation
          loadingFlags={[
            isChallengeLoading,
            isChallengeInvitationLoading,
            isInvitationLoading,
          ]}
        />
      </Card>
      <Switch>
        <Match when={!isLoading() && challenge.id && hasUserAccess()}>
          <Card>
            <Card.Header>
              <div class="card-title m-0">
                <div>
                  <h1 class="title mt-6">Invitación a Reto Grupal</h1>
                  <p class="sub-title mt-2">
                    Por favor, confirma tu participación en el desafío
                  </p>
                </div>
              </div>
              <div class="card-toolbar gap-2 gap-md-5">
                <Button
                  class="btn btn-sm btn-danger btn-sm align-self-center"
                  onClick={() => handleSubmit('REJECT')}
                >
                  Rechazar
                </Button>
                <Button
                  class="btn btn-sm btn-success btn-sm align-self-center"
                  onClick={() => handleSubmit('ACCEPT')}
                >
                  Aceptar
                </Button>
              </div>
            </Card.Header>
            <Card.Body>
              <Row class="py-10 px-5">
                <Col sm={12} md={6} lg={8}>
                  <div>
                    <h2> ¡Hola {name}!</h2>
                    <h5 class="mt-8">
                      ¡Te invitamos a un emocionante desafío grupal llamado "
                      {challenge.name}"!
                    </h5>
                    <p class="mt-8">
                      Este desafío es una oportunidad para unir fuerzas con tus
                      colegas y alcanzar juntos un objetivo saludable. Para
                      participar, solo necesitas aceptar este desafío. Una vez
                      dentro, podrás registrar tus resultados y ver cómo
                      contribuyes al progreso del grupo.
                    </p>
                    <p class="mt-8">
                      ¡Es una excelente oportunidad para mantenerte activo y
                      motivado junto con tus compañeros. Esperamos contar
                      contigo en este desafío y trabajar juntos para lograr
                      nuestro objetivo !
                    </p>
                  </div>
                  <Show when={challenge.progressType === 'FINAL_RESULT'}>
                    <Row>
                      <p class="mt-8">
                        Este desafío requiere que ingreses un valor inicial para
                        comenzar a participar.
                      </p>
                      <Form.Label for="initValue">
                        ¿Con cuánto inicias tu meta?
                      </Form.Label>
                      <Col sm={6} md={6} lg={4}>
                        <FormGroup>
                          <Form.Control
                            type="number"
                            id="initialValue"
                            name="initialValue"
                            class="form-control"
                            onInput={(e) =>
                              setInitialValue(e.currentTarget.value || '')
                            }
                            value={initialValue()}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col class="d-flex" sm={6} md={6} lg={4}>
                        <div class="d-flex">
                          <p class="my-auto">
                            {challengeElementMapped().goalUnitHuman}
                          </p>
                        </div>
                      </Col>
                    </Row>
                  </Show>
                </Col>
                <Col sm={12} md={6} lg={4}>
                  <ChallengeSide challenge={challengeElementMapped()} />
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Match>
      </Switch>
    </>
  );
};
export default ChallengeGroupInvitation;
