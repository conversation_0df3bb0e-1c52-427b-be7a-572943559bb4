import { <PERSON><PERSON>, <PERSON>, Col, Row } from 'solid-bootstrap';
import { useChallengesContext } from '../context';
import { useProgress } from '../../hooks/use-progress';
import { useChallengeDetail } from '../../hooks/use-challenge-detail';
import LiderBoard from './leader-board';
import ChallengeSide from '../../../../shared/components/challenge/challenge-side';
import { ChallengeHabitProgressDetail } from '../../../../shared/components/challenge/challenge-habit-progress-detail';
import { Show, createEffect, createMemo } from 'solid-js';
import { useActualProgress } from '../../hooks/use-get-current-progress';
import { useProgressTable } from '../../hooks/use-progress-table';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { getCurrentUserData } from '../../../../shared/services/user/user-session-management';
import { useChallengeProgress } from '../../../../shared/infra/hooks/use-challenge-progress';
import { ErrorAlert } from '../../../../shared/components/alert/error-alert';
import { ChallengeProgressTable } from '../../../../shared/components/challenge/challenge-progress-table';
import { useScreenWidth } from '../../../../shared/hooks/use-screen-width';
import { ChallengeProgressModalProvider } from '../../../../shared/components/challenge-progress-modal/challenge-progress-modal-context';
import ChallengeProgressModal from '../../../../shared/components/challenge-progress-modal/challenge-progress-modal';
import ChallengeEvidenceModal from '../../../../shared/components/challenge/challenge-evidence-modal';
import { ChallengeAlertServices } from '../../services/alert-services';
import { ChallengeServices } from '../../services/challenge-services';
import '../../styles/detail-style.css';
import { createStore } from 'solid-js/store';

const ChallengeDetail = () => {
  const context = useChallengesContext();
  const screenWidth = useScreenWidth();
  if (!context || !context.challengeDetailContent()) return;
  const {
    show,
    setShow,
    evidence,
    setEvidence,
    showAddProgressModal,
    setShowAddProgressModal,
  } = useProgress();
  const { isUserOwnerOfThisChallenge } = useChallengeDetail();
  const {
    isLoading: isLoadingChallengeProgress,
    error: errorChallengeProgress,
    refetchChallengeProgress,
    challengeProgress,
    deleteChallengeProgress,
  } = useChallengeProgress({ id: context.challengeDetailContent().id });
  const isUserOwner = createMemo(() =>
    isUserOwnerOfThisChallenge(context.challengeDetailContent())
  );
  const onEvidenceUpdated = () => {
    setShowAddProgressModal(false);
    refetchChallengeProgress();
  };
  const isHabit = createMemo(
    () => context.challengeDetailContent().progressType === 'HABIT'
  );
  const isGroupChallenge = createMemo(
    () => context.challengeDetailContent().challengeParticipantType === 'GROUP'
  );
  const [challengeWithProgressData, setChallengeWithProgressData] = createStore(
    ChallengeServices.CombineProgressDataWithChallengeData(
      [...challengeProgress],
      context.challengeDetailContent()
    )
  );
  createEffect(() => {
    setChallengeWithProgressData(
      ChallengeServices.CombineProgressDataWithChallengeData(
        [...challengeProgress],
        context.challengeDetailContent()
      )
    );
  });
  const actualProgress = useActualProgress(
    context.challengeDetailContent(),
    challengeWithProgressData
  );
  const handleDetailButtonClicked = (row: ChallengeProgress) => {
    if (!row.evidence) {
      context
        ?.parentContext()
        ?.showInfoAlert({ message: 'No hay evidencia para este registro' });

      return;
    }
    setEvidence(row.evidence);
    setShow(true);
  };
  const handleDeleteButtonClicked = async (row: ChallengeProgress) => {
    const { id } = getCurrentUserData();
    if (row.userId !== id) return;
    ChallengeAlertServices.handleDeleteProgressButtonClicked({
      id: row.id,
      callback: deleteChallengeProgress,
    });
  };
  const { headers, control } = useProgressTable({
    progressType: context.challengeDetailContent().progressType,
    challengeParticipantType:
      context.challengeDetailContent().challengeParticipantType,
    isDeleteEnabled: true,
    detailCallback: handleDetailButtonClicked,
    deleteCallback: handleDeleteButtonClicked,
  });
  const ChallengeDetailHeader = () => (
    <>
      <h1 class="card-title">Meta: {context.challengeDetailContent().name}</h1>
      <div class="card-toolbar gap-2 gap-md-5">
        <button
          type="button"
          role="button"
          class="btn btn-sm btn-secondary btn-sm align-self-center"
          onClick={() => {
            context.setView('TABLE');
          }}
        >
          Regresar
        </button>
        <Show when={isUserOwner()}>
          <>
            <button
              type="button"
              role="button"
              class="btn btn-sm btn-danger btn-sm align-self-center"
              onClick={async () => {
                await ChallengeAlertServices.handleDeleteChallengeButtonClicked(
                  {
                    callback: async () => {
                      const isSuccess = await context
                        .parentContext()
                        ?.deleteChallenge(context.challengeDetailContent().id);
                      isSuccess && context.setView('TABLE');
                    },
                  }
                );
              }}
            >
              Eliminar
            </button>
            <button
              type="button"
              role="button"
              class="btn btn-sm btn-success btn-sm align-self-center"
              onClick={async () => {
                await ChallengeAlertServices.handleCompleteChallengeButtonClicked(
                  {
                    callback: async () => {
                      const isSuccess = await context
                        .parentContext()
                        ?.completeChallenge(
                          context.challengeDetailContent().id
                        );
                      isSuccess && context.setView('TABLE');
                    },
                  }
                );
              }}
            >
              Completar
            </button>
            <button
              class="btn btn-sm btn-primary btn-sm align-self-center"
              type="submit"
              disabled={
                !isUserOwnerOfThisChallenge(context.challengeDetailContent())
              }
              onClick={() => {
                context.setChallengeEditData(
                  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                  context.challengeDetailContent()!
                );
                context.setView('EDIT');
              }}
            >
              Editar
            </button>
          </>
        </Show>
      </div>
    </>
  );

  return (
    <>
      <Card>
        <Card.Header>
          <ChallengeDetailHeader />
        </Card.Header>
        <Card.Body>
          <Row class="py-10 px-5">
            <Col sm={12} md={6} lg={8}>
              <Show when={isHabit()}>
                <div class="mb-5">
                  <ChallengeHabitProgressDetail
                    progressData={challengeWithProgressData}
                    isLoading={isLoadingChallengeProgress}
                    challenge={context.challengeDetailContent()}
                  />
                </div>
              </Show>

              <div class={`${screenWidth() > 900 ? 'mb-8' : 'mb-20'}`}>
                <ErrorAlert error={[errorChallengeProgress]} />
                <ChallengeProgressTable
                  header={headers}
                  control={control}
                  isHabit={isHabit()}
                  data={challengeWithProgressData}
                  actualProgress={actualProgress}
                  isLoading={isLoadingChallengeProgress}
                />
              </div>
              <Show when={isGroupChallenge()}>
                <LiderBoard challengeId={context.challengeDetailContent().id} />
              </Show>
              <Button
                variant="outline-primary"
                class="btn btn-primary  my-5"
                onClick={() => setShowAddProgressModal(true)}
              >
                <i class="bi bi-plus-lg"></i> Agregar progreso
              </Button>
            </Col>
            <Col sm={12} md={6} lg={4}>
              <ChallengeSide challenge={context.challengeDetailContent()} />
            </Col>
          </Row>
        </Card.Body>
      </Card>
      <Show when={context.challengeDetailContent()}>
        <ChallengeProgressModalProvider>
          <ChallengeProgressModal
            {...{
              challenge: context.challengeDetailContent,
              showAddProgressModal,
              setShowAddProgressModal,
              callbackAfterUpdate: onEvidenceUpdated,
            }}
          />
        </ChallengeProgressModalProvider>
      </Show>
      <ChallengeEvidenceModal
        show={show}
        setShow={setShow}
        evidence={evidence()}
      />
    </>
  );
};
export default ChallengeDetail;
