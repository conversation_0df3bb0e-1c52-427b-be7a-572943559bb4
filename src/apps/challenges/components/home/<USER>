import { PreConfiguredChallenge } from '../../types';
import { Card } from 'solid-bootstrap';
import {
  Match,
  Switch,
  createEffect,
  createSignal,
  onCleanup,
  onMount,
} from 'solid-js';
import NewChallengeForm from './form';
import ChallengeHome from './home';
import Animation from '../../../../shared/components/lottie-player/animation';
import { useChallengesContext } from '../context';
import NewChallengeModal from './new-challenge-modal';
import ChallengeDetail from './detail';
import { useChallengeContainer } from '../../hooks/use-challenge-container';
import ChallengeEditForm from './edit';

const CHALLENGE_STATUS = 'ACTIVE';
const ChallengesContainer = () => {
  const context = useChallengesContext();
  if (!context) return;
  const {
    showNewChallengeModal,
    setShowNewChallengeModal,
    challengesParticipantTypes,
    setChallengesParticipantTypes,
    userChallenges,
    setUserChallenges,
    preConfiguredChallenge,
    setPreConfiguredChallenge,
  } = useChallengeContainer();
  const [hasToLoadData, setHasToLoadData] = createSignal(false);
  onCleanup(() => {
    context.setView('FORM');
  });
  onMount(async () => {
    const challenges = await context
      .parentContext()
      .getUserChallenges(CHALLENGE_STATUS);
    challenges && setUserChallenges(challenges);
  });
  createEffect(async () => {
    if (hasToLoadData() && context.view() === 'TABLE') {
      const challenges = await context
        .parentContext()
        .getUserChallenges(CHALLENGE_STATUS);
      challenges && setUserChallenges(challenges);
    }
    if (context.view() !== 'TABLE') setHasToLoadData(true);
  }, context.view());
  const handleChallengeTypeOptionClicked = (type: PreConfiguredChallenge) => {
    setPreConfiguredChallenge(type);
    setShowNewChallengeModal(false);
    context.setView('FORM');
  };

  return (
    <>
      {context.parentContext().isLoading() && (
        <>
          <Card>
            <Card.Body>
              <Animation />
            </Card.Body>
          </Card>
        </>
      )}
      {!context.parentContext().isLoading() && (
        <Switch>
          <Match when={context.view() === 'TABLE'}>
            <ChallengeHome
              setUserChallenges={setUserChallenges}
              userChallenges={userChallenges}
              setShowNewChallengeModal={setShowNewChallengeModal}
            />
            <NewChallengeModal
              {...{
                showNewChallengeModal,
                setShowNewChallengeModal,
                setChallengesParticipantTypes,
                handleChallengeTypeOptionClicked,
              }}
            />
          </Match>
          <Match when={context.view() === 'FORM'}>
            <NewChallengeForm
              participanType={challengesParticipantTypes()}
              preConfiguredChallenge={preConfiguredChallenge()}
            />
          </Match>
          <Match when={context.view() === 'DETAIL'}>
            <ChallengeDetail />
          </Match>
          <Match when={context.view() === 'EDIT'}>
            <ChallengeEditForm />
          </Match>
        </Switch>
      )}
    </>
  );
};
export default ChallengesContainer;
