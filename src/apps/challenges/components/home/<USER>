import { Button, Modal } from 'solid-bootstrap';
import { Accessor, For, Match, Setter, Switch, createSignal } from 'solid-js';
import {
  CHALLENGE_LIBRARY,
  CHALLENGE_TYPE_LIBRARY,
} from '../../../../shared/libraries/challenge-library';
import { PreConfiguredChallenge } from '../../types';
import { ChallengesParticipanTypes } from 'entities/Challenge';

const NewChallengeModal = (props: {
  showNewChallengeModal: Accessor<boolean>;
  setShowNewChallengeModal: Setter<boolean>;
  setChallengesParticipantTypes: Setter<ChallengesParticipanTypes | undefined>;
  handleChallengeTypeOptionClicked: (type: PreConfiguredChallenge) => void;
}) => {
  const [modalOption, setModalOption] =
    createSignal<ChallengesParticipanTypes>();
  const handleChallengeTypeOptionClicked = (type: PreConfiguredChallenge) => {
    props.handleChallengeTypeOptionClicked(type);
  };
  const renderChallengeButon = (type: PreConfiguredChallenge) => {
    return (
      <Button
        class="btn btn-outline btn-outline btn-outline-primary btn-active-light-primary my-5 mt-10 flex-grow-1 m-3"
        style="max-width: calc(50% - 40px);"
        onClick={() => {
          handleChallengeTypeOptionClicked(type);
        }}
      >
        {type.name}
      </Button>
    );
  };

  return (
    <>
      <Modal
        show={props.showNewChallengeModal()}
        onHide={() => {
          props.setShowNewChallengeModal(false);
          props.setChallengesParticipantTypes();
          setModalOption();
        }}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header closeButton></Modal.Header>
        <Modal.Body>
          <div>
            <h1 class="modal-option-h1">¡Nuevo reto!</h1>
            <h3 class="modal-option-h3">
              Selecciona el tipo de reto al que te gustaría inscribirte.
            </h3>
            <div class="gap-8 my-20">
              <Switch>
                <Match when={!modalOption()}>
                  <div>
                    <For each={CHALLENGE_TYPE_LIBRARY}>
                      {(option) => (
                        <div
                          class="new-challenge-option p-6 mx-10 my-4"
                          onClick={() => {
                            setModalOption(option.type);
                            props.setChallengesParticipantTypes(option.type);
                          }}
                        >
                          <h2 class="new-challenge-option-title">
                            {option.name}
                          </h2>
                          <p class="new-challenge-option-subtitle">
                            {option.description}
                          </p>
                        </div>
                      )}
                    </For>
                  </div>
                </Match>
                <Match when={modalOption() === 'INDIVIDUAL'}>
                  <div class="m-0">
                    <div class="d-flex flex-wrap mx-10">
                      <For each={CHALLENGE_LIBRARY}>
                        {(option) => renderChallengeButon(option)}
                      </For>
                    </div>
                  </div>
                </Match>
                <Match when={modalOption() === 'GROUP'}>
                  <div class="m-0">
                    <div class="d-flex flex-wrap mx-10">
                      <For each={CHALLENGE_LIBRARY}>
                        {(option) => renderChallengeButon(option)}
                      </For>
                    </div>
                  </div>
                </Match>
              </Switch>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};
export default NewChallengeModal;
