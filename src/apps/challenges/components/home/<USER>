import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'solid-bootstrap';
import { ChallengeDataProps } from '../../types';
import { useChallengesContext } from '../context';
import { useChallengeDates } from '../../hooks/use-challenge-dates';
import { Show, createMemo, createSignal } from 'solid-js';
import { ChallengeProgressModalProvider } from '../../../../shared/components/challenge-progress-modal/challenge-progress-modal-context';
import ChallengeProgressModal from '../../../../shared/components/challenge-progress-modal/challenge-progress-modal';

const Challenge = (props: ChallengeDataProps) => {
  const context = useChallengesContext();
  const challenge = createMemo(() => props.challenge);
  const [showAddProgressModal, setShowAddProgressModal] = createSignal(false);
  if (!context) return;
  const getChallengeParticipantIcon = () => {
    const CHALLENGE_ICON_LIBRARY = {
      ['GROUP']: 'bi bi-people-fill ms-2',
      ['INDIVIDUAL']: 'bi bi-person-fill',
    };
    const { challengeParticipantType: type } = challenge();

    return CHALLENGE_ICON_LIBRARY[type];
  };
  const challengeDates = createMemo(() =>
    useChallengeDates({
      startDate: challenge().startDate,
      endDate: challenge().endDate,
    })
  );
  const getChallengeStatusIcon = () => {
    const type = challengeDates().length === 0 ? 'PENDING' : 'STARTED';
    const CHALLENGE_STATUS_ICON_LIBRARY = {
      ['PENDING']: 'bi bi-calendar-x',
      ['STARTED']: 'bi bi-calendar-check',
    };

    return CHALLENGE_STATUS_ICON_LIBRARY[type];
  };

  return (
    <>
      <Card class="challenge-card px-5 py-6 my-3">
        <Row>
          <Col sm={6} md={6} lg={6} class="gap-2 gap-md-5">
            <h3 class="text-truncate challenge-title mt-3 mb-6">
              {'⭐  ' + challenge().name}
            </h3>
            <h3 class="text-truncate challenge-time my-4">
              {'⏱ ' + challenge().time}
            </h3>
            <h3 class="text-truncate challenge-time my-4">
              {'📍 ' + challenge().challengeProgressTypeHuman}
            </h3>
            <h3 class="text-truncate challenge-price my-4 mb-6">
              {'🏆 ' + challenge().reward}
            </h3>
            <h3 class="text-truncate challenge-last-update mt-3 mb-3">
              {challenge().lastUpdate}
            </h3>
          </Col>
          <Col sm={6} md={6} lg={6}>
            <div class="gap-2 gap-md-5">
              <ProgressBar
                class="my-5"
                variant="primary"
                now={challenge().timeProgress}
              />
              <Show
                when={context
                  .parentContext()
                  .services.isUserAwaitingToRespondInvitationToChallenge(
                    challenge()
                  )}
              >
                <Button
                  variant="outline-primary me-5 w-100"
                  class="btn btn-outline btn-outline btn-outline-info btn-active-light-primary my-5"
                  onClick={() => {
                    context
                      .parentContext()
                      .pageRedirect(
                        `../challenges/?view=INVITATION&challengeId=${
                          challenge().id
                        }`
                      );
                  }}
                >
                  <i class="bi bi-exclamation-triangle-fill mb-1"></i> Responder
                  a Invitación
                </Button>
              </Show>
              <Show
                when={
                  !context
                    .parentContext()
                    .services.isUserAwaitingToRespondInvitationToChallenge(
                      challenge()
                    )
                }
              >
                <>
                  <Button
                    variant="outline-primary me-5"
                    class="btn btn-outline btn-outline btn-outline-primary btn-active-light-primary my-5"
                    onClick={() => {
                      context.setChallengeDetailContent(challenge());
                      context.setView('DETAIL');
                    }}
                  >
                    <i class="bi bi-info-circle-fill mb-1"></i> Detalle
                  </Button>
                  <Button
                    variant="outline-primary"
                    class="btn btn-outline btn-outline btn-outline-primary btn-active-light-primary my-5"
                    onClick={() => setShowAddProgressModal(true)}
                  >
                    <i class="bi bi-plus-lg"></i> Agregar progreso
                  </Button>
                </>
              </Show>
            </div>
            <div class="mt-5 text-end">
              {challengeDates().length === 0 ? 'Pendiente  ' : 'Iniciado  '}
              <i
                // eslint-disable-next-line @typescript-eslint/naming-convention
                style={{ 'font-size': '16px', color: '#BC36F0' }}
                class={getChallengeStatusIcon()}
              ></i>
            </div>
            <div class="mt-4 text-end">
              {challenge().challengeParticipantType === 'GROUP'
                ? 'Grupal  '
                : 'Individual  '}
              <i
                // eslint-disable-next-line @typescript-eslint/naming-convention
                style={{ 'font-size': '16px', color: '#BC36F0' }}
                class={getChallengeParticipantIcon()}
              ></i>
            </div>
          </Col>
        </Row>
      </Card>
      <ChallengeProgressModalProvider>
        <ChallengeProgressModal
          {...{
            challenge,
            showAddProgressModal,
            setShowAddProgressModal,
            callbackAfterUpdate: () => setShowAddProgressModal(false),
          }}
        />
      </ChallengeProgressModalProvider>
    </>
  );
};
export default Challenge;
