import { Card, Col, Dropdown, Row } from 'solid-bootstrap';
import { For, createMemo } from 'solid-js';
import Challenge from './challenge';
import { ChallengeHomeProps } from '../../types';
import { useScreenWidth } from '../../../../shared/hooks/use-screen-width';
import { useChallengesContext } from '../context';
import { DEFAULT_CHALLENGE_DATA } from '../../../../shared/libraries/challenge-library';

const ChallengeHome = (props: ChallengeHomeProps) => {
  const context = useChallengesContext();
  if (!context) return;
  const screenWidth = useScreenWidth();
  const groupChallengeCount = createMemo(() => {
    return props
      .userChallenges()
      .filter((challenge) => challenge.challengeParticipantType === 'GROUP')
      .length;
  });
  const individualChallengeCount = createMemo(() => {
    return props
      .userChallenges()
      .filter(
        (challenge) => challenge.challengeParticipantType === 'INDIVIDUAL'
      ).length;
  });
  const sortedChallenges = (sortFilter: string) => {
    if (sortFilter === 'ASC') {
      props.setUserChallenges((prevChallenges) =>
        [...prevChallenges].sort(
          (a, b) =>
            new Date(a.endDate).getTime() - new Date(b.endDate).getTime()
        )
      );
    }
    if (sortFilter === 'DESC') {
      props.setUserChallenges((prevChallenges) =>
        [...prevChallenges].sort(
          (a, b) =>
            new Date(b.endDate).getTime() - new Date(a.endDate).getTime()
        )
      );
    }
  };

  return (
    <>
      <Card class="p-5">
        <Card.Header>
          <div class="card-title">
            <div>{/* <h3>Retos y metas</h3> */}</div>
          </div>
          <div class="card-toolbar gap-2 gap-md-5">
            <button
              class="btn btn-sm btn-primary btn-sm align-self-center"
              type="submit"
              onClick={() => {
                context.setChallengeDetailContent(DEFAULT_CHALLENGE_DATA);
                props.setShowNewChallengeModal(true);
              }}
            >
              <i class="bi bi-plus-lg"></i> Añadir
            </button>
          </div>
        </Card.Header>
        <div class={`${screenWidth() > 1300 ? 'mt-8 px-5' : 'mt-8 px-2'}`}>
          <Row>
            <Col sm={12} md={12} lg={8}>
              <Card
                class={`${screenWidth() > 1300 ? 'pe-10 px-5' : 'px-0'} gap-0`}
              >
                <h3 class={`${screenWidth() > 1300 ? 'mt-10' : 'mt-2'}`}>
                  Retos y Metas
                </h3>
                <div class={`mb-${screenWidth() > 900 ? '10' : '0'} pt-5`}>
                  {props.userChallenges().length === 0 && (
                    <div class="ms-2 mt-10">
                      <p
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        style={{ 'font-size': '16px' }}
                        class="font-weight-light text-muted"
                      >
                        No tienes activas
                      </p>
                    </div>
                  )}
                  <div
                    class={`challenge-container ${
                      screenWidth() > 900 ? ' px-3  pe-12' : ''
                    } mb-5`}
                  >
                    <Dropdown
                      class="text-end"
                      onSelect={(e) => {
                        sortedChallenges(e || 'ASC');
                      }}
                    >
                      <Dropdown.Toggle
                        class="btn btn-bg-body text-inverse-body me-2 mb-2"
                        variant="body"
                        id="dropdown-basic"
                      >
                        Filtrar
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <Dropdown.Item eventKey="ASC">
                          Ascendente (Fecha Final)
                        </Dropdown.Item>
                        <Dropdown.Item eventKey="DESC">
                          Descendente (Fecha Final)
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                    <For each={props.userChallenges()}>
                      {(challenge) => <Challenge challenge={challenge} />}
                    </For>
                  </div>
                </div>
              </Card>
            </Col>
            <Col
              sm={12}
              md={12}
              lg={4}
              class={`${screenWidth() > 900 && 'pe-20'}`}
            >
              <div class="text-center mt-10 mb-6 gap-10">
                <h3>Estas participando </h3>
                <div class="mt-15 ">
                  <div class="data-card p-5 my-5">
                    <h3 class="data-card-title">Metas personales</h3>
                    <h1 class="data-card-value">
                      {individualChallengeCount()}
                    </h1>
                  </div>
                  <div class="data-card p-5 my-5">
                    <h3 class="data-card-title">Retos grupales</h3>
                    <h1 class="data-card-value">{groupChallengeCount()}</h1>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </Card>
    </>
  );
};
export default ChallengeHome;
