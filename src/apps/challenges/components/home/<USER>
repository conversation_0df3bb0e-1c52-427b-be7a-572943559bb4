/* eslint-disable @typescript-eslint/naming-convention */
import { Show, createMemo } from 'solid-js';
import { useLeaderBoard } from '../../../../shared/infra/hooks/use-lider-board';
import { useLeaderBoardTable } from '../../hooks/use-leader-board-table';
import { Card, Row } from 'solid-bootstrap';
import { LoadingAlert } from '../../../../shared/components/alert/loading-alert';
import EmptyTableIcon from '../../../../shared/components/table/empty-table-icon';
import StoreTableComponent from '../../../../shared/components/table/store-table';
import { ErrorAlert } from '../../../../shared/components/alert/error-alert';

const LiderBoard = ({
  challengeId,
  isCompleted,
  isOwner,
}: {
  challengeId: string;
  isCompleted?: boolean;
  isOwner?: boolean;
}) => {
  const { isLoading, error, leaderBoard, setChallengeWinner } = useLeaderBoard({
    id: challengeId,
  });
  const { tableHeaders, tableControls } = useLeaderBoardTable({
    id: challengeId,
    setWinnerCallback: setChallengeWinner,
  });
  const winner = createMemo(() => leaderBoard.winner);
  const isLeaderBoardEmpty = createMemo(
    () => leaderBoard.leaderboard.length === 0
  );

  return (
    <>
      <ErrorAlert error={[error]} />
      <Card>
        <Card.Header style={{ 'border-bottom': '0px' }}>
          <div>
            <Card.Title class="mt-3">LeaderBoard 🏆</Card.Title>
            <Show when={winner() && isCompleted}>
              <Card.Subtitle class="my-10">
                {`🎉 Ganador: ${winner()?.userName}`}
              </Card.Subtitle>
            </Show>
          </div>
        </Card.Header>
      </Card>
      <Card.Body class="px-0 pt-0">
        <LoadingAlert class="my-5 py-5" loadingFlags={[isLoading]} />
        <Show when={!isLoading() && isLeaderBoardEmpty()}>
          <Row>
            <EmptyTableIcon
              class="m-auto mt-5"
              style="height:200px"
            ></EmptyTableIcon>
            <label class="m-auto mt-8 text-center">No hay participantes</label>
          </Row>
        </Show>
        <Show when={!isLoading()}>
          <StoreTableComponent
            header={tableHeaders}
            control={isCompleted && isOwner ? tableControls : []}
            data={leaderBoard.leaderboard.map((item, index) => ({
              position: index + 1,
              ...item,
            }))}
          />
        </Show>
      </Card.Body>
    </>
  );
};
export default LiderBoard;
