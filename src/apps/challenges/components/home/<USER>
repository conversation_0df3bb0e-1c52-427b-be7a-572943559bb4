/* eslint-disable @typescript-eslint/no-explicit-any */
import { Col, Form, Row, Card } from 'solid-bootstrap';
import { createMemo, createSignal, onCleanup } from 'solid-js';
import { NewChallengeFormDTO, NewChallengeProps } from '../../types';
import { getCurrentUserData } from '../../../../shared/services/user/user-session-management';
import { useChallengesContext } from '../context';
import { useChallengeForm } from '../../hooks/use-challenge-form';
import { usePredefinedChallenge } from '../../hooks/use-predefined-challenge';
import { ChallengesProgressType } from 'entities/Challenge';
import { ChallengeGoal } from '../../../../shared/components/challenge/challenge-goal';
import { ChallengeType } from '../../../../shared/components/challenge/challenge-type';
import { ChallengeRemainder } from '../../../../shared/components/challenge/challenge-remainder';
import { ChallengeInitialValue } from '../../../../shared/components/challenge/challenge-initial-value';
import { ChallengeBasicInfo } from '../../../../shared/components/challenge/challenge-basic-info';
import { ChallengeDate } from '../../../../shared/components/challenge/challenge-date';
import { ChallengeEvidence } from '../../../../shared/components/challenge/challenge-evidence';
import { DEFAULT_CHALLENGE_DATA } from '../../../../shared/libraries/challenge-library';
import { ParticipanList } from '../../../../shared/components/challenge/participant-list';
import '../../styles/form-style.css';
import { useUsersByStatus } from '../../../../shared/infra/hooks/use-user-by-status';

const ChallengeForm = (props: NewChallengeProps) => {
  const context = useChallengesContext();
  if (!context) return;
  const { id, name, profilePicture, email } = getCurrentUserData();
  const challengeOptions = usePredefinedChallenge(
    props.preConfiguredChallenge?.id
  );
  const challengeData = context.challengeDetailContent();
  const [challengeProgressType, setChallengeProgressType] =
    createSignal<ChallengesProgressType>(challengeOptions.progressType);
  const {
    screenWidth,
    selectedUser,
    setSelectedUser,
    activeUserList,
    setActiveUserList,
    hasNotifications,
    isEvidenceRequired,
    notificationOption,
    handleRadioChange,
    handleHasNotificationRadioChange,
    handleIsEvidenceRequired,
  } = useChallengeForm(challengeData);
  const challengeParticipantType = createMemo(() => {
    return props.participanType || 'INDIVIDUAL';
  });
  const handleSubmit = async (event: Event) => {
    event.preventDefault();
    const participants = [
      ...(selectedUser() || []),
      { id, name, profilePicture, email },
    ];
    const eventChallengeData = context
      .parentContext()
      ?.services.mapFormSubmitEventChallengeData(
        event,
        challengeParticipantType(),
        challengeProgressType(),
        participants,
        hasNotifications(),
        notificationOption(),
        isEvidenceRequired()
      );
    const challenge: NewChallengeFormDTO = {
      userId: id,
      ...eventChallengeData,
    };
    await saveNewChallenge(challenge);
  };
  const saveNewChallenge = async (challenge: NewChallengeFormDTO) => {
    const trimmedChallenge = context
      ?.parentContext()
      ?.services.trimStringProperties(challenge);
    if (!trimmedChallenge) return;
    const result = await context
      ?.parentContext()
      ?.createChallenge(trimmedChallenge);
    if (result) context.setView('TABLE');
  };
  const handleChallengeProgressTypeRadioChange = (event: Event) => {
    const challengeProgressType = (event.target as HTMLSelectElement).value;
    if (
      challengeProgressType === 'ACCUMULATIVE' ||
      challengeProgressType === 'FINAL_RESULT' ||
      challengeProgressType === 'HABIT'
    ) {
      setChallengeProgressType(challengeProgressType);
    }
  };
  const { isLoading: isUsersByStatusLoading, data: usersByStatus } =
    useUsersByStatus('ACTIVE');
  createMemo(() => setActiveUserList(usersByStatus));
  onCleanup(() => {
    context.setIsFormUseForEdit(false);
    context.setChallengeDetailContent(DEFAULT_CHALLENGE_DATA);
  });

  return (
    <>
      <Form onSubmit={handleSubmit}>
        <Card class="p-8">
          <Card.Header class="mb-5 pb-2">
            <div class="card-title m-0">
              <div>
                <h1 class="title mt-6">Personaliza tu meta:</h1>
                {props.preConfiguredChallenge && (
                  <p class="mt-2">{props.preConfiguredChallenge.name}</p>
                )}
                <p class="sub-title mt-2">Define los parámetros de tu meta</p>
              </div>
            </div>
            <div class="card-toolbar gap-2 gap-md-5">
              <button
                type="button"
                role="button"
                class="btn btn-sm btn-secondary btn-sm align-self-center"
                onClick={() => context.setView('TABLE')}
              >
                Regresar
              </button>
              <button
                class="btn btn-sm btn-primary btn-sm align-self-center"
                type="submit"
              >
                Guardar
              </button>
            </div>
          </Card.Header>
          <Card>
            <Row class={`${screenWidth() > 600 && 'px-15'}`}>
              <Col
                sm={12}
                md={7}
                lg={7}
                class={`${screenWidth() > 600 ? 'mb-10 pe-20 ' : 'mb-2'} mt-0`}
              >
                <ChallengeType
                  {...{
                    isEdit: false,
                    challengeOptions,
                    challengeProgressType,
                    handleChallengeProgressTypeRadioChange,
                  }}
                />
                <ChallengeBasicInfo {...{ challengeData }} />
                <ChallengeInitialValue
                  {...{
                    challengeData,
                    challengeParticipantType,
                    challengeProgressType,
                    challengeOptions,
                  }}
                />
                <ChallengeGoal
                  {...{
                    challengeOptions,
                    challengeProgressType,
                    challengeData,
                  }}
                />
                <ChallengeDate {...{ challengeData }} />
              </Col>
              <Col
                sm={12}
                md={5}
                lg={5}
                class={`${screenWidth() > 600 ? 'mt-3 px-20' : ' mt-5'} mb-10`}
              >
                <ChallengeEvidence
                  {...{
                    isEvidenceRequired,
                    handleIsEvidenceRequired,
                  }}
                />
                <ChallengeRemainder
                  {...{
                    hasNotifications,
                    notificationOption,
                    handleHasNotificationRadioChange,
                    handleRadioChange,
                  }}
                />
                {challengeParticipantType() === 'GROUP' && (
                  <>
                    {isUsersByStatusLoading() ? (
                      <div class="d-flex justify-content-center p-5">
                        <div class="spinner-border text-primary" role="status">
                          <span class="visually-hidden">Loading...</span>
                        </div>
                      </div>
                    ) : (
                      <ParticipanList
                        {...{ activeUserList, selectedUser, setSelectedUser }}
                      />
                    )}
                  </>
                )}
              </Col>
            </Row>
          </Card>
        </Card>
      </Form>
    </>
  );
};
export default ChallengeForm;
