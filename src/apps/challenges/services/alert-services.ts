import Swal from 'sweetalert2';

export class ChallengeAlertServices {
  public static handleCompleteChallengeButtonClicked({
    callback,
  }: {
    callback: () => Promise<void>;
  }) {
    Swal.fire({
      title: 'Completar Reto',
      text: '¿Esta seguro que desea completar esta reto antes de la fecha establecida?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Completar',
      customClass: {
        confirmButton: 'btn btn-warning',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        if (result.isConfirmed) {
          await callback();
        }
      }
    });
  }

  public static handleDeleteChallengeButtonClicked({
    callback,
  }: {
    callback: () => Promise<void>;
  }) {
    Swal.fire({
      title: 'Eliminar Reto',
      text: '¿Esta seguro que desea eliminar esta reto?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Eliminar',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        await callback();
      }
    });
  }

  public static handleDeleteProgressButtonClicked({
    id,
    callback,
  }: {
    id: string;
    callback: (id: string) => Promise<void>;
  }) {
    Swal.fire({
      title: 'Archivar Progreso',
      text: '¿Esta seguro que desea archivar esta progreso?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Archivar',
      customClass: {
        confirmButton: 'btn btn-danger',
        cancelButton: 'btn btn-secondary',
      },
    }).then(async (result) => {
      if (result.isConfirmed) {
        callback(id);
      }
    });
  }
}
