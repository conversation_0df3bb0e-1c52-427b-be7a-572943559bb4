import { ChallengeProgress } from 'entities/ChallengeProgress';
import { ChallengeListElement } from '../types';
import { useCalculateDaysDifference } from '../../../shared/hooks/time-difference';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';

export class ChallengeServices {
  public static CombineProgressDataWithChallengeData(
    progressList: ChallengeProgress[],
    challenge: ChallengeListElement
  ) {
    return progressList.map((progress) => {
      const getRemaining = () => {
        if (challenge.goalType === 'ADD')
          return progress.value > challenge.goal
            ? 0
            : challenge.goal - progress.value;
        if (challenge.goalType === 'REDUCE')
          return progress.value < challenge.goal
            ? 0
            : progress.value - challenge.goal;

        return progress.value;
      };
      const lastUpdateValue = useCalculateDaysDifference(progress.updatedAt);

      return {
        id: progress.id,
        challengeId: progress.challengeId,
        userId: progress.userId,
        userName: progress.userName,
        value: progress.value,
        remaining: getRemaining(),
        unit: challenge.goalUnitHuman,
        evidence: progress.evidence,
        lastUpdate: `Hace ${lastUpdateValue} día${
          lastUpdateValue > 1 ? 's' : ''
        }`,
        date: new Date(progress.createdAt).toLocaleDateString(),
        challengeProgressStatus: progress.challengeProgressStatus,
        challengeProgressType: progress.challengeProgressType,
        updatedAt: progress.updatedAt,
        createdAt: progress.createdAt,
      };
    });
  }

  public static IsProgressPresentForThatDate(props: {
    date: string;
    userChallengeProgress: ChallengeProgress[];
    userId?: string;
  }) {
    const searchDate = new Date(props.date);
    const userId = props.userId || getCurrentUserData().id;

    return props.userChallengeProgress
      .filter((data) => data.userId === userId)
      .some((challengeProgress) => {
        const challengeProgressDate = new Date(challengeProgress.createdAt);

        return (
          challengeProgressDate.getFullYear() === searchDate.getFullYear() &&
          challengeProgressDate.getMonth() === searchDate.getMonth() &&
          challengeProgressDate.getDate() === searchDate.getDate()
        );
      });
  }
}
