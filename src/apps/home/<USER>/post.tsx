import { Card } from 'solid-bootstrap';
import { Post } from '../../../entities/Post';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { Match, Show, Switch, createSignal } from 'solid-js';
import RocheEditor from '../../../shared/components/roche-editor/editor';
import { PageContext } from '../types';
import { Home } from '../main';
import Gallery from '../../../shared/components/gallery/gallery';

export interface IPostElementProps {
  post: Post;
  homeContext: PageContext<Home>;
}
const PostElement = ({ post, homeContext }: IPostElementProps) => {
  const userData = getCurrentUserData();
  const [isEdit, setIsEdit] = createSignal(false);
  const [likes, setLikes] = createSignal(post.likes.length);
  const [editContent, setEditContent] = createSignal(post.content);
  const [images] = createSignal<string[]>(post.imagesURL, {
    equals: false,
  });
  const [shareUrl] = createSignal(
    `${window.location.origin}/apps/public-content/?id=${post.id}`
  );
  const [isLiked, setIsLiked] = createSignal(
    post.likes?.find((user) => user.userId === userData.id) ? true : false
  );
  const handleLikeClicked = () => {
    giveLike();
    homeContext.context.postLike(post);
    setIsLiked(!isLiked());
    setLikes(!isLiked() ? likes() - 1 : likes() + 1);
  };
  const giveLike = () => {
    const { id, name } = userData;
    const isLiked = post.likes.some((like) => like.userId === id);
    if (isLiked) {
      post.likes = post.likes.filter((like) => like.userId !== id);
    } else {
      post.likes.push({ userId: id, userName: name });
    }
  };
  const updatePost = async () => {
    await homeContext.context.updatePost(post, editContent()).then(() => {
      setIsEdit(false);
    });
  };
  const handleShareButton = () => {
    // TODO: hidden from the user
    // navigator.clipboard.writeText(shareUrl());
    homeContext.context.showSuccessAlert({
      title: `URL copiado!`,
      message: `${shareUrl()}`,
    });
  };

  return (
    <>
      <Card class="card card-flush shadow-sm my-3 px-5">
        <Card.Header class="align-items-center py-5 gap-2 gap-md-5">
          <div class="card-title">
            <div>
              <h1 class="title mt-6">BeBetter</h1>
              <p class="sub-title mt-2">
                {/*eslint-disable-next-line */}
              {homeContext.context.services.getHumanReadableDate(post.createdAt!)}
              </p>
            </div>
          </div>
          <div class="card-toolbar ms-auto">
            <div class="card-toolbar gap-2 gap-md-5">
              {/* TODO: hide this from user */}
              {false && (
                <div>
                  <button
                    class="btn btn-active-light mx-2"
                    onClick={handleShareButton}
                  >
                    <i class="bi bi-share"></i>
                  </button>
                </div>
              )}

              {userData.userType === 'ADMIN' && (
                <div>
                  <button
                    class="btn btn-active-light mx-2"
                    onClick={() => setIsEdit(!isEdit())}
                  >
                    <i class="bi bi-pencil-square"></i>
                  </button>
                </div>
              )}
            </div>
          </div>
        </Card.Header>
        <Card.Body class="pt-0">
          <Show when={!isEdit()}>
            <div innerHTML={editContent()}></div>
          </Show>
          <Show when={isEdit()}>
            <RocheEditor content={editContent()} updateHtml={setEditContent} />
            <button type="submit" class="btn btn-primary w-auto mb-5 mt-5">
              <span class="indicator-label" onClick={() => updatePost()}>
                Actualizar
              </span>
            </button>
          </Show>
          {images() && images().length > 0 && <Gallery images={images} />}
        </Card.Body>
        <Card.Footer>
          <div class="flex justify-start align-center">
            <div class="card-toolbar gap-2 gap-md-5">
              <Switch>
                <Match when={!isLiked()}>
                  <button
                    class="btn btn-active-light-primary mx-2 flex justify-center items-center"
                    onClick={handleLikeClicked}
                  >
                    <span class="text-gray-500 mr-4 p-0">{likes()}</span>
                    &numsp; <i class="bi bi-hand-thumbs-up"></i>
                  </button>
                </Match>
                <Match when={isLiked()}>
                  <button
                    class="btn btn-active-light mx-2 flex justify-center align-center"
                    onClick={handleLikeClicked}
                  >
                    <span class="text-gray-500 mr-4 p-0">{likes()}</span>
                    &numsp; <i class="bi bi-hand-thumbs-up-fill"></i>
                  </button>
                </Match>
              </Switch>
            </div>
          </div>
        </Card.Footer>
      </Card>
    </>
  );
};
export default PostElement;
