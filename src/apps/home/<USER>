import { Services } from '../../shared/services/app-base/service-base';

export class HomeServices extends Services {
  public getHumanReadableDate(datetime: string): string {
    const isoDateString = datetime;
    const date = new Date(isoDateString);
    // eslint-disable-next-line
        const options: any = { year: 'numeric', month: 'long', day: 'numeric' };
    const humanReadableDate = date.toLocaleDateString('es', options);

    return humanReadableDate;
  }
}
