import { Post } from 'entities/Post';
import { createSignal, onMount } from 'solid-js';
import { Home } from '../main';

export const useContent = (props: { context: Home }) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(true);
  const [postList, setPostList] = createSignal<Post[]>([]);
  const getContent = async () => {
    try {
      setIsLoading(true);
      const data = await props.context.getPost();
      setPostList(data);
    } catch (error) {
      props.context.showErrorAlert({ message: 'Error al obtener los posts' });
    } finally {
      setIsLoading(false);
    }
  };
  onMount(async () => {
    await getContent();
  });

  return { isLoading, postList, getContent };
};
