import { For, Match, Switch } from 'solid-js';
import { Home } from '../main';
import { PageContext } from '../types';
import PostElement from './post';
import EmptyLogo from '../../../assets/media/logos/post_empty.svg';
import { Card } from 'solid-bootstrap';
import { useContent } from '../hooks/use-content';
import Animation from '../../../shared/components/lottie-player/animation';
import '../styles/styles.css';

const PostsContainer = (props: PageContext<Home>) => {
  const { isLoading, postList } = useContent({
    context: props.context,
  });

  return (
    <>
      <Switch>
        <Match when={isLoading() === true}>
          <Card>
            <Card.Body>
              <div class="card-body card-toolbar">
                <Animation />
              </div>
            </Card.Body>
          </Card>
        </Match>
        <Match when={isLoading() === false && postList().length > 0}>
          <div class="gap-2 gap-md-5">
            <For each={postList()}>
              {(post) => <PostElement post={post} homeContext={props} />}
            </For>
          </div>
        </Match>
        <Match when={isLoading() === false && postList().length === 0}>
          <Card>
            <Card.Body>
              <div
                class="py-20"
                style={{
                  width: '100%',
                  display: 'flex', // eslint-disable-next-line
            'flex-direction': 'column',// eslint-disable-next-line
            'justify-content': 'center',// eslint-disable-next-line
            'justify-items': 'center',// eslint-disable-next-line
            'align-content': 'center',// eslint-disable-next-line
            'align-items': 'center',
                }}
              >
                <img
                  src={EmptyLogo}
                  alt="Sin Imágenes"
                  style={{ height: '200px' }}
                />
                {/* eslint-disable-next-line*/}
          <h2 class='mt-20'>No existe contenido para mostrar</h2>
              </div>
            </Card.Body>
          </Card>
        </Match>
      </Switch>
    </>
  );
};
export default PostsContainer;
