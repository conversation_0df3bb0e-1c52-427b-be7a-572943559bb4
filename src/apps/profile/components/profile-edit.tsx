import { createMemo } from 'solid-js';
import { ProfilePage } from '../main';
import { ProfileEditDataProps } from '../types';
import { useUser } from './profile-context';

const ProfileEditData = ({
  context: mainContext,
}: ProfileEditDataProps<ProfilePage>) => {
  const context = useUser();
  if (!context) return;
  const user = createMemo(() => context.userFormData());
  const onSaveHandler = (e: Event) => {
    e.preventDefault();
    mainContext.editUserData(user());
  };

  return (
    <>
      <div class="card mb-5 mb-xl-12">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Datos Generales</h3>
          </div>

          <button
            onClick={onSaveHandler}
            class="btn btn-sm btn-primary align-self-center"
          >
            Guardar
          </button>
        </div>

        <div class="card-body p-9">
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">Cédula</label>

            <div class="col-lg-8 fv-row">
              <input
                type="text"
                class="form-control"
                value={user().identification}
                disabled
              />
            </div>
          </div>
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">
              Nombre Completo
            </label>

            <div class="col-lg-8">
              <input
                id="user-name"
                type="text"
                class="form-control"
                value={user().name}
                onInput={(e) => {
                  context?.setUserFormData((user) => {
                    user.name = (e.target as HTMLInputElement).value || '';

                    return user;
                  });
                }}
              ></input>
            </div>
          </div>
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">
              Fecha de Nacimiento
            </label>

            <div class="col-lg-8">
              <input
                id="user-name"
                type="date"
                class="form-control"
                value={user().birthDate ? user().birthDate.split('T')[0] : ''}
                onInput={(e) => {
                  context?.setUserFormData((user) => {
                    const dateValue = (e.target as HTMLInputElement).value;
                    if (dateValue) {
                      // Create a date object from the value
                      const date = new Date(dateValue);
                      // Add 6 hours
                      date.setHours(date.getHours() + 6);
                      // Store the updated value in ISO string format
                      user.birthDate = date.toISOString();
                    } else {
                      user.birthDate = '';
                    }

                    return user;
                  });
                }}
              ></input>
            </div>
          </div>
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">País</label>
            <div class="col-lg-8 fv-row">
              <select
                class="form-select"
                aria-label="Select example"
                required
                disabled
              >
                <option value="COSTA_RICA">Costa Rica</option>
                <option value="EL_SALVADOR">El Salvador</option>
                <option value="PANAMA">Panama</option>
                <option value="GUATEMALA">Guatemala</option>
                <option value="HONDURAS">Honduras</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="card mb-5 mb-xl-12">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Medidas Corporales</h3>
          </div>
        </div>

        <div class="card-body p-9">
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">Altura (m)</label>

            <div class="col-lg-8 fv-row">
              <input
                type="number"
                step="any"
                class="form-control"
                value={user().height}
                min={1}
                max={350}
                required
                onInput={(e) => {
                  context?.setUserFormData((user) => {
                    user.height =
                      parseFloat((e.target as HTMLInputElement).value) || 0;

                    return user;
                  });
                }}
              />
            </div>
          </div>
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">Peso (kg)</label>
            <div class="col-lg-8 fv-row">
              <input
                type="number"
                step="any"
                class="form-control"
                min={1}
                max={200}
                required
                value={user().weight}
                onInput={(e) => {
                  context?.setUserFormData((user) => {
                    user.weight =
                      parseFloat((e.target as HTMLInputElement).value) || 0;

                    return user;
                  });
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <div class="card mb-5 mb-xl-12">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Información de Contacto</h3>
          </div>
        </div>

        <div class="card-body p-9">
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">Teléfono</label>

            <div class="col-lg-8 d-flex align-items-center">
              <input
                id="user-phone"
                type="text"
                pattern="^[0-9]{8}$"
                title="Numero de teléfono debe ser solo números y de 8 dígitos."
                class="form-control"
                value={user().phone}
                onInput={(e) => {
                  let input = (e.target as HTMLInputElement).value;
                  input = input.replace(/\D/g, '');
                  context?.setUserFormData((user) => {
                    user.phone = parseInt(input) || 0;

                    return user;
                  });
                }}
              ></input>
            </div>
          </div>
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted">Email</label>

            <div class="col-lg-8 fv-row">
              <input
                id="user-company"
                type="text"
                class="form-control"
                value={user().email}
                disabled
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ProfileEditData;
