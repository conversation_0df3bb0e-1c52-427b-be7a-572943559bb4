import { Match, Switch } from 'solid-js';
import ProfileDetail from './profile-detail';
import ProfileEditData from './profile-edit';
import ProfileHeader from '../../../shared/components/user/profile-header';
import { useUser } from './profile-context';
import ProfilePictureEdit from './profile-profile-picture-edit';
import ProfilePasswordEdit from './profile-password-edit';
import { ProfileDataContainerProps } from '../types';
import { ProfilePage } from '../main';

const ProfileDataContainer = ({
  context: mainContext,
}: ProfileDataContainerProps<ProfilePage>) => {
  const context = useUser();
  if (!context) return null;

  return (
    <>
      <div class="mb-5 mb-xxl-8">
        <ProfileHeader user={context.userFormData} />
      </div>
      <Switch>
        <Match when={context.view() === 'DETAIL'}>
          <ProfileDetail />
        </Match>
        <Match when={context.view() === 'FORM'}>
          <ProfilePictureEdit context={mainContext} />
          <ProfilePasswordEdit context={mainContext} />
          <ProfileEditData context={mainContext} />
        </Match>
      </Switch>
    </>
  );
};
export default ProfileDataContainer;
