import CustomFileUploader from '../../../shared/components/custom-file-uploader/custom-file-uploader';
import { ProfilePage } from '../main';
import { ProfilePictureEditProps } from '../types';
import { useUser } from './profile-context';
import { createSignal } from 'solid-js';

const ProfilePictureEdit = ({
  context: mainContext,
}: ProfilePictureEditProps<ProfilePage>) => {
  const context = useUser();
  const [fileList, setFileList] = createSignal<File[]>([]);
  const onSaveHandler = async (e: Event) => {
    e.preventDefault();
    const file = fileList()[0];
    if (!file) return;
    const userData = await mainContext.editProfilePicture(file);
    if (userData?.profilePicture) {
      updateUserContext(userData.profilePicture);
      setFileList([]);
    }
  };
  const updateUserContext = (profilePicture: string) => {
    context?.setUserFormData((user) => {
      user.profilePicture = profilePicture;

      return user;
    });
  };

  return (
    <>
      <div class="card mb-5 mb-xl-12">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Imagen </h3>
          </div>

          <button
            onClick={onSaveHandler}
            class="btn btn-sm btn-primary align-self-center"
          >
            Guardar
          </button>
        </div>

        <div class="card-body p-9">
          <div class="row mb-7">
            <label class="col-lg-4 fw-semibold text-muted mb-3">Imagen</label>

            <div class="col-lg-8">
              <CustomFileUploader
                id={'profile-picture'}
                fileList={[fileList, setFileList]}
                numberOfFiles={10}
                acceptedTypes="image/*"
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ProfilePictureEdit;
