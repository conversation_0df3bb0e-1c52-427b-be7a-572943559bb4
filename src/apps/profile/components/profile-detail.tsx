import { UserCountry } from 'entities/User';
import { useUser } from './profile-context';
import { DataLabel } from '../../../shared/components/data-label';
import { createMemo } from 'solid-js';
import { DateServices } from '../../../shared/services/utils/date-services';

const ProfileDetail = () => {
  const context = useUser();
  if (!context) return;
  const user = createMemo(() => context.userFormData());
  const getHumanUserCountry = (country: UserCountry | undefined) => {
    if (country === undefined) return '';
    const countryDictionary = {
      ['COSTA_RICA']: 'Costa Rica',
      ['EL_SALVADOR']: 'El Salvador',
      ['PANAMA']: 'Panama',
      ['GUATEMALA']: 'Guatemala',
      ['HONDURAS']: 'Honduras',
    };

    return countryDictionary[country] || '';
  };

  return (
    <>
      <div class="card mb-5 mb-xl-6">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Datos Generales</h3>
          </div>

          <button
            onClick={(e) => {
              e.preventDefault();
              context?.setView('FORM');
            }}
            class="btn btn-sm btn-primary align-self-center"
          >
            Editar Perfil
          </button>
        </div>

        <div class="card-body p-9">
          <DataLabel label={'Cédula'} value={user().identification} />
          <DataLabel label={'Nombre'} value={user().name} />
          <DataLabel
            label={'Edad'}
            value={DateServices.calculateAgeBasedOnDate(
              user().birthDate
            ).toString()}
          />
          <DataLabel
            label={'Fecha de Nacimiento'}
            value={DateServices.mapISODateToDateWithoutTime(user().birthDate)}
          />
          <DataLabel
            label={'País'}
            value={getHumanUserCountry(user().country)}
          />
        </div>
      </div>
      <div class="card mb-5 mb-xl-6">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Medidas Corporales</h3>
          </div>
        </div>

        <div class="card-body p-9">
          <DataLabel
            label={'Altura'}
            value={`${user().height?.toString() || '-'} m`}
          />
          <DataLabel
            label={'Peso'}
            value={`${user().weight?.toString() || '-'} kg`}
          />
          <DataLabel
            label={'IMC'}
            value={parseFloat(user().bmi.toFixed(2)).toString()}
          />
        </div>
      </div>
      <div class="card mb-5 mb-xl-6">
        <div class="card-header cursor-pointer">
          <div class="card-title m-0">
            <h3 class="fw-bold m-0">Información de Contacto</h3>
          </div>
        </div>

        <div class="card-body p-9">
          <DataLabel label={'Teléfono'} value={user().phone.toString()} />
          <DataLabel label={'Email'} value={user().email} />
        </div>
      </div>
    </>
  );
};
export default ProfileDetail;
