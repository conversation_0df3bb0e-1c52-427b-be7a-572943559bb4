import { User } from 'entities/User';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import {
  createSignal,
  createContext,
  useContext,
  Accessor,
  Setter,
} from 'solid-js';
import { ViewType } from '../types';

export interface IUserContextModel {
  view: Accessor<ViewType>;
  setView: Setter<ViewType>;
  userFormData: Accessor<User>;
  setUserFormData: Setter<User>;
  userNewPassword: Accessor<string>;
  setUserNewPassword: Setter<string>;
}
const UserContext = createContext<IUserContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function UserProvider(props: any) {
  const userData = getCurrentUserData();
  const [view, setView] = createSignal<ViewType>('DETAIL');
  const [userFormData, setUserFormData] = createSignal<User>(userData, {
    equals: false,
  });
  const [userNewPassword, setUserNewPassword] = createSignal<string>('');
  const value: IUserContextModel = {
    view,
    setView,
    userFormData,
    setUserFormData,
    userNewPassword,
    setUserNewPassword,
  };

  return (
    <UserContext.Provider value={value}>{props.children}</UserContext.Provider>
  );
}
export function useUser(): IUserContextModel | undefined {
  return useContext(UserContext);
}
