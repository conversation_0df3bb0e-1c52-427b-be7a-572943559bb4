import { createSignal } from 'solid-js';
import { ProfilePage } from '../main';
import { PasswordFormProps, ProfilePasswordEditProps } from '../types';

const [passwordForm, setPasswordForm] = createSignal<PasswordFormProps>({
  newPassword: '',
  newPasswordRetry: '',
});
const ProfilePasswordEdit = ({
  context: mainContext,
}: ProfilePasswordEditProps<ProfilePage>) => {
  const onSaveHandler = (e: Event) => {
    e.preventDefault();
    mainContext.editUserPassword(passwordForm());
  };

  return (
    <>
      <form onSubmit={onSaveHandler}>
        <div class="card mb-5 mb-xl-12">
          <div class="card-header cursor-pointer">
            <div class="card-title m-0">
              <h3 class="fw-bold m-0">Contraseña </h3>
            </div>

            <button
              type="submit"
              class="btn btn-sm btn-primary align-self-center"
            >
              Guardar
            </button>
          </div>

          <div class="card-body p-9">
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">
                Nueva Contraseña
              </label>

              <div class="col-lg-8">
                <input
                  id="user-new-password"
                  type="text"
                  class="form-control mb-3"
                  pattern="(?=.*[a-z])(?=.*[A-Z]).{8,}"
                  required
                  autocomplete="none"
                  value={passwordForm()?.newPassword}
                  onInput={(e) => {
                    setPasswordForm((form) => {
                      form.newPassword =
                        (e.target as HTMLInputElement).value || '';

                      return form;
                    });
                  }}
                />
                <small class="mt-3">
                  Formato de la contraseña: 8 caracteres, debe contener al
                  menos: 1 numero, 1 letra mayúscula y 1 letra minúscula.
                </small>
              </div>
            </div>
            <div class="row mb-7">
              <label class="col-lg-4 fw-semibold text-muted">
                Reescriba Contraseña
              </label>

              <div class="col-lg-8 fv-row">
                <input
                  id="user-new-password-retry"
                  type="text"
                  class="form-control"
                  required
                  autocomplete="none"
                  pattern="(?=.*[a-z])(?=.*[A-Z]).{8,}"
                  value={passwordForm()?.newPasswordRetry}
                  onInput={(e) => {
                    setPasswordForm((form) => {
                      form.newPasswordRetry =
                        (e.target as HTMLInputElement).value || '';

                      return form;
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </form>
    </>
  );
};
export default ProfilePasswordEdit;
