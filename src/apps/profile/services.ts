import { Services } from '../../shared/services/app-base/service-base';
import { PasswordFormProps } from './types';

export class ProfileServices extends Services {
  public isPasswordMatching(data: PasswordFormProps) {
    const { newPassword, newPasswordRetry } = data;

    return newPassword === newPasswordRetry;
  }

  public getCurrentUserData() {
    const localData = JSON.parse(localStorage.getItem('user-local-data') || '');

    return localData;
  }
}
