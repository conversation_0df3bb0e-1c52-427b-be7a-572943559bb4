import { render } from 'solid-js/web';
import { Page } from '../../shared/services/app-base/page-base';
import ProfileDataContainer from './components/profile-container';
import { ProfileServices } from './services';
import { UserProvider } from './components/profile-context';
import { User } from 'entities/User';
import { getCurrentUserData } from '../../shared/services/user/user-session-management';
import { UserProps } from '../auth/sign-in/types';
import { PasswordFormProps } from './types';
import { UserAPI } from '../../shared/infra';
import { ImageServices } from '../../shared/services/utils/image-services';

window.addEventListener('load', async () => {
  new ProfilePage();
});
export class ProfilePage extends Page {
  readonly api;

  readonly services;

  constructor() {
    super();
    this.api = new UserAPI();
    this.services = new ProfileServices();
    this.renderComponents();
  }

  public async editUserData(data: User | undefined) {
    try {
      this.showLoadingScreen();
      if (!data) return;
      if (this.services.isDataMissingInformation(data))
        throw 'Missing user information';
      const result = await this.api.updateUserInformation(data);
      this.services.updateUserLocalStorageUserData(result);
      this.showSuccessAlert({ message: 'Información básica actualizada.' });
    } catch (error) {
      if (error === 'Missing user information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return;
      }
      this.showErrorAlert({ message: 'Error al actualizar usuario' });
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async editProfilePicture(file: File): Promise<UserProps | null> {
    try {
      this.showLoadingScreen();
      const compressFile = await ImageServices.CompressImage(file, {
        maxSizeMB: 5,
        maxWidthOrHeight: 900,
      });
      const base64File = await this.services.convertFileToBase64(compressFile);
      const user = getCurrentUserData();
      if (!this.services.isFileSizeValid(file.size)) throw 'File Size Invalid';
      const updateProfilePictureProps = {
        userId: user.id,
        profilePicture: base64File,
        name: file.name,
        size: file.size,
        type: file.type,
      };
      const result = await this.api.updateProfilePicture(
        updateProfilePictureProps
      );
      this.services.updateUserLocalStorageUserData(result);
      this.showSuccessAlert({ message: 'Imagen actualizada.' });

      return result;
    } catch (error) {
      if (error === 'File Size Invalid') {
        this.showErrorAlert({ message: 'La imagen no puede exceder 1 mb' });

        return null;
      }
      this.showErrorAlert({ message: 'Error al actualizar imagen' });

      return null;
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async editUserPassword(data: PasswordFormProps) {
    try {
      this.showLoadingScreen();
      if (!this.services.isPasswordMatching(data)) throw 'Wrong Password';
      const user = getCurrentUserData();
      const params = {
        userId: user.id,
        password: data.newPassword,
      };
      await this.api.updateUserPassword(params);
      this.showSuccessAlert({ message: 'Contraseña actualizada.' });
    } catch (error) {
      if (error === 'Missing user information') {
        this.showErrorAlert({ message: 'Complete todos los campos' });

        return;
      }
      this.showErrorAlert({ message: 'Error al actualizar usuario' });
    } finally {
      this.hideLoadingScreen();
    }
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'user-profile-container'
      );
      render(
        () => (
          <>
            <UserProvider>
              <ProfileDataContainer context={this} />
            </UserProvider>
          </>
        ),
        userProfileContainer
      );
    }
  }
}
