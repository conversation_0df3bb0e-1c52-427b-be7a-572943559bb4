<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <script>
        const global = window;
    </script>
    <script type="module" src="../../shared/layout/main-layout/index.tsx"></script>
</head>

<body id="kt_body" style="display:none"
    class="header-fixed header-tablet-and-mobile-fixed toolbar-enabled aside-fixed aside-default-enabled">
    <div class="d-flex flex-column flex-root">
        <div class="page d-flex flex-row flex-column-fluid">
            <custom-sidebar></custom-sidebar>

            <div class="wrapper d-flex flex-column flex-row-fluid" id="kt_wrapper">
                <custom-header></custom-header>

                <div class="content fs-6 d-flex flex-column flex-column-fluid" id="kt_content">
                    <div class="toolbar" id="kt_toolbar">
                        <div class="container-fluid d-flex flex-stack flex-wrap flex-sm-nowrap">
                            <div class="d-flex flex-column align-items-start justify-content-center flex-wrap me-2">
                                <h1 class="text-dark fw-bold my-1 fs-2">
                                    Perfil
                                    <small class="text-muted fs-6 fw-normal ms-1"></small>
                                </h1>

                                <ul class="breadcrumb fw-semibold fs-base my-1">
                                    <li class="breadcrumb-item text-muted">
                                        <a href="/apps/home/" class="text-muted text-hover-primary">Inicio</a>
                                    </li>
                                    <li class="breadcrumb-item text-dark">Detalles</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="post fs-6 d-flex flex-column-fluid" id="kt_post">
                        <div class="container-xxl">
                            <user-profile-container />
                        </div>
                    </div>
                </div>

                <custom-footer></custom-footer>
            </div>
        </div>
    </div>

    <script type="module" src="./main.tsx"></script>
    <script type="application/ecmascript" src="../../assets/js/scripts.bundle.js"></script>
    <custom-loader></custom-loader>
</body>

</html>