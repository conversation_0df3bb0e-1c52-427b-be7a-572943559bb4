import { Post } from '../../entities/Post';
import { Page } from '../../shared/services/app-base/page-base';
import { HomeServices } from './services';
import { render } from 'solid-js/web';
import PostsContainer from './components/posts-container';
import { HealthContentAPI } from '../../shared/infra';

window.addEventListener('load', async () => {
  new Home();
});
export class Home extends Page {
  readonly services;

  readonly api;

  constructor() {
    super();
    this.api = new HealthContentAPI();
    this.services = new HomeServices();
    this.renderComponents();
  }

  public async getPostById(): Promise<Post[]> {
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    if (!id) return [];
    try {
      this.showLoadingScreen();
      const data = await this.api.getHealthPostById(id);

      return [data];
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener las publicaciones' });

      return [];
    } finally {
      this.hideLoadingScreen();
    }
  }

  private async renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer =
        this.services.getHTMLElement('posts-container');
      render(() => <PostsContainer context={this} />, userProfileContainer);
    }
  }
}
