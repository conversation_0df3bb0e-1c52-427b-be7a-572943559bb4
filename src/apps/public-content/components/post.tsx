import { Card } from 'solid-bootstrap';
import { Post } from '../../../entities/Post';
import { createSignal } from 'solid-js';
import { PageContext } from '../types';
import { Home } from '../main';
import Gallery from '../../../shared/components/gallery/gallery';

export interface IPostElementProps {
  post: Post;
  homeContext: PageContext<Home>;
}
const PostElement = ({ post, homeContext }: IPostElementProps) => {
  const [images] = createSignal(post.imagesURL);

  return (
    <>
      <Card class="card card-flush shadow-sm my-3">
        <Card.Header>
          <div class="mt-4">
            <h1 class="title mt-6">BeBetter</h1>
            <p class="sub-title mt-2">
              {/*eslint-disable-next-line */}
              {homeContext.context.services.getHumanReadableDate(post.createdAt!)}
            </p>
          </div>
        </Card.Header>
        <Card.Body>
          <div innerHTML={post.content}></div>
          {images() && images().length > 0 && <Gallery images={images} />}
        </Card.Body>
      </Card>
    </>
  );
};
export default PostElement;
