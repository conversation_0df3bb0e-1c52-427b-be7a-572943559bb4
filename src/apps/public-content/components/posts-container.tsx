import { For, Show, createSignal, onMount } from 'solid-js';
import { Home } from '../main';
import { PageContext } from '../types';
import { Post } from '../../../entities/Post';
import PostElement from './post';
import EmptyLogo from '../../../assets/media/logos/post_empty.svg';
import '../styles/styles.css';

// eslint-disable-next-line @typescript-eslint/naming-convention
const responsiveCardStyle = { 'max-width': '80vw' };
const PostsContainer = (props: PageContext<Home>) => {
  const [postList, setPostList] = createSignal<Post[]>([]);
  onMount(async () => {
    const data = await props.context.getPostById();
    setPostList(data);
  });

  return (
    <>
      <Show when={postList().length > 0}>
        <div class="mt-20" style={responsiveCardStyle}>
          <For each={postList()}>
            {(post) => <PostElement post={post} homeContext={props} />}
          </For>
        </div>
      </Show>
      <Show when={postList().length === 0}>
        <div
          style={{
            width: '100%',
            height: '100vh',
            display: 'flex', // eslint-disable-next-line
            'flex-direction': 'column',// eslint-disable-next-line
            'justify-content': 'center',// eslint-disable-next-line
            'justify-items': 'center',// eslint-disable-next-line
            'align-content': 'center',// eslint-disable-next-line
            'align-items': 'center',
          }}
        >
          <img src={EmptyLogo} alt="Sin Imágenes" style={{ height: '200px' }} />
          {/* eslint-disable-next-line*/}
          <h2 style={{ 'margin-top': '2rem' }}>Este contenido no existe</h2>
        </div>
      </Show>
    </>
  );
};
export default PostsContainer;
