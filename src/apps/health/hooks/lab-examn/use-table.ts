import { TableData } from 'apps/health/components/lab-examn/types';
import { TableControlHeader, TableHeader } from 'shared/components/table/types';
import { createSignal } from 'solid-js';
import Swal from 'sweetalert2';

export type UseTableProps = {
  onDelete: (id: string) => Promise<void>;
};
export const useTable = (props: UseTableProps) => {
  const [tableData, setTableData] = createSignal<TableData[]>([]);
  const header: TableHeader[] = [
    {
      name: 'labExamnName',
      title: 'Nombre',
      type: 'text',
      width: 150,
    },
    {
      name: 'labExamValue',
      title: 'Resultado',
      type: 'text',
      width: 150,
    },
    {
      name: 'createdAtHuman',
      title: 'Fecha',
      type: 'text',
      width: 150,
    },
  ];
  const control: TableControlHeader[] = [
    {
      name: 'delete',
      title: 'Eliminar',
      controlType: 'danger',
      callback: async (row: TableData) => {
        Swal.fire({
          title: 'Eliminar',
          text: '¿Esta seguro que desea eliminar este registro?',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Eliminar',
          customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary',
          },
        }).then(async (result: { isConfirmed: boolean }) => {
          if (result.isConfirmed) {
            props.onDelete(row.id);
          }
        });
      },
    },
  ];

  return { tableData, setTableData, header, control };
};
