/* eslint-disable @typescript-eslint/no-explicit-any */
import { IHabitProps } from 'apps/health/components/habits/types';

export const useForm = (event: Event) => {
  const oliveOil = (event.target as any).oliveOil.value;
  const greenLeafyVegetables = (event.target as any).greenLeafyVegetables.value;
  const otherVegetables = (event.target as any).otherVegetables.value;
  const berries = (event.target as any).berries.value;
  const otherFruit = (event.target as any).otherFruit.value;
  const meat = (event.target as any).meat.value;
  const fish = (event.target as any).fish.value;
  const chicken = (event.target as any).chicken.value;
  const cheese = (event.target as any).cheese.value;
  const butter = (event.target as any).butter.value;
  const beans = (event.target as any).beans.value;
  const wholeGrains = (event.target as any).wholeGrains.value;
  const sweetsAndPastries = (event.target as any).sweetsAndPastries.value;
  const nuts = (event.target as any).nuts.value;
  const fastFood = (event.target as any).fastFood.value;
  const alcohol = (event.target as any).alcohol.value;
  const physicalActivity = (event.target as any).physicalActivity.value;
  const nicotineExposure = (event.target as any).nicotineExposure.value;
  const sleep = (event.target as any).sleep.value;
  const bodyWeight = (event.target as any).bodyWeight.value;
  const height = (event.target as any).height.value;
  const cholesterol = (event.target as any).cholesterol.value;
  const glucose = (event.target as any).glucose.value;
  const systolic = (event.target as any).systolic.value;
  const diastolic = (event.target as any).diastolic.value;
  const data: IHabitProps = {
    oliveOil,
    greenLeafyVegetables,
    otherVegetables,
    berries,
    otherFruit,
    meat,
    fish,
    chicken,
    cheese,
    butter,
    beans,
    wholeGrains,
    sweetsAndPastries,
    nuts,
    fastFood,
    alcohol,
    physicalActivity,
    nicotineExposure,
    sleep,
    bodyWeight,
    height,
    cholesterol,
    glucose,
    systolic,
    diastolic,
  };

  return data;
};
