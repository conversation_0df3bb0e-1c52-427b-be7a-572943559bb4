import { ApexOptions } from 'apexcharts';
import { createSignal } from 'solid-js';

export const useChart = () => {
  const CHART_BASE_OPTIONS: ApexOptions = {
    chart: {
      height: 600,
    },
    dataLabels: {
      enabled: true,
      style: {
        colors: ['#7D0096'],
      },
    },
    stroke: {
      show: true,
      curve: 'smooth',
      colors: ['#BC36F0'],
    },
    markers: {
      size: 1,
    },
    noData: {
      text: 'No se encontró información',
    },
  };
  const [chartOptions, setChartOptions] = createSignal<ApexOptions>(
    {
      xaxis: {
        categories: [],
      },
      ...CHART_BASE_OPTIONS,
    },
    {
      equals: false,
    }
  );
  const [chartOptionsGauge, setChartOptionsGauge] = createSignal<ApexOptions>(
    {
      chart: {
        type: 'radialBar',
        height: 900,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          hollow: {
            margin: 0,
            size: '60%',
          },
          track: {
            background: '#e7e7e7',
            strokeWidth: '97%',
            margin: 5, // margin is in pixels
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              offsetY: -2,
              fontSize: '18px',
            },
          },
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'horizontal',
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 1,
          opacityTo: 1,
          colorStops: [
            {
              offset: 0,
              color: '#FF1F26',
              opacity: 1,
            },
            {
              offset: 20,
              color: '#FFBD69',
              opacity: 1,
            },
            {
              offset: 60,
              color: '#FFBD69',
              opacity: 1,
            },
            {
              offset: 100,
              color: '#50CD89',
              opacity: 1,
            },
          ],
        },
      },
    },
    {
      equals: false,
    }
  );

  return {
    chartOptions,
    setChartOptions,
    chartOptionsGauge,
    setChartOptionsGauge,
  };
};
