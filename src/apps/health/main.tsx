import { render } from 'solid-js/web';
import { Page } from '../../shared/services/app-base/page-base';
import UserHealthContainer from './components/container';
import { UserHealthServices } from './services';
import { UserHealthProvider } from './components/context';
import './styles.css';

window.addEventListener('load', async () => {
  new UserHealth();
});
export class UserHealth extends Page {
  readonly services;

  private view: 'HEALTH' | 'LAB_EXAMS';

  constructor() {
    super();
    this.services = new UserHealthServices();
    this.getModuleToDisplay();
    this.renderComponents();
  }

  private getModuleToDisplay() {
    const view = this.getURLParams().get('view');
    if (view === 'HEALTH') {
      this.view = 'HEALTH';

      return;
    }
    // LAB_EXAMNS is a typo, it should be LAB_EXAMS
    if (view === 'LAB_EXAMS' || view === 'LAB_EXAMNS') {
      this.view = 'LAB_EXAMS';

      return;
    }
    this.showErrorAlert({ message: 'URL Invalida' });
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'user-health-container'
      );
      render(
        () => (
          <>
            <UserHealthProvider>
              <UserHealthContainer view={this.view} />
            </UserHealthProvider>
          </>
        ),
        userProfileContainer
      );
    }
  }
}
