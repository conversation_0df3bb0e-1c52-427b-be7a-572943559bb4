import {
  createSignal,
  createContext,
  useContext,
  Accessor,
  Setter,
} from 'solid-js';
import { ViewType } from '../types';
import { UserHealth } from '../main';
import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';

interface IUserHealthContextModel {
  currentView: Accessor<ViewType>;
  setCurrentView: Setter<ViewType>;
  mainContext: Accessor<UserHealth | undefined>;
  setParentContext: Setter<UserHealth | undefined>;
  userReport: Accessor<UserMedicalDataReport | undefined>;
  setUserReport: Setter<UserMedicalDataReport | undefined>;
}
const UserHealthContext = createContext<IUserHealthContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function UserHealthProvider(props: any) {
  const [userReport, setUserReport] = createSignal<UserMedicalDataReport>();
  const [currentView, setCurrentView] = createSignal<ViewType>('DASHBOARD');
  const [mainContext, setParentContext] = createSignal<UserHealth>();
  const value: IUserHealthContextModel = {
    currentView,
    setCurrentView,
    mainContext,
    setParentContext,
    userReport,
    setUserReport,
  };

  return (
    <UserHealthContext.Provider value={value}>
      {props.children}
    </UserHealthContext.Provider>
  );
}
export function useUserHealthContext(): IUserHealthContextModel | undefined {
  return useContext(UserHealthContext);
}
