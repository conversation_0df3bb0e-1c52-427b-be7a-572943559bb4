import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';
import { Services } from '../../../../shared/services/app-base/service-base';
import { TableData } from './types';
import { UserLabExamResult } from 'entities/UserLabExamResult';
import { useTimeFormat } from '../../../../shared/hooks/use-time-format';
import { MedicalDataService } from '../../../../shared/services/medical-data/medical-data-services';

export class UserLabExamResultServices extends Services {
  public filterUserMedicalDataReport(report: UserMedicalDataReport) {
    return {
      ...report,
      data: report.data.filter(
        (healthData) =>
          healthData.lastValue !== '-' &&
          healthData.lastValue !== 'sin alteraciones'
      ),
    };
  }

  public mapUserLabExamResultToTableData(
    data: UserLabExamResult[]
  ): TableData[] {
    const mapExamIdToTitle = ({ labExamnId }: { labExamnId: string }) => {
      return MedicalDataService.getLabExamDetailById(labExamnId)?.title || '-';
    };

    return data
      .map((result) => {
        return {
          id: result.id,
          userId: result.userId,
          labExamId: result.labExamnId,
          labExamnName: mapExamIdToTitle(result),
          labExamValue: result.labExamnValue,
          userLabExamResultStatus: result.userLabExamnResultStatus,
          updatedAt: result.updatedAt,
          createdAt: result.createdAt,
          createdAtHuman: useTimeFormat(result.createdAt),
        };
      })
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
  }
}
