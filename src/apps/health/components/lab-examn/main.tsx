import { UserLabExamResultAPI } from '../../../../shared/infra';
import { Page } from '../../../../shared/services/app-base/page-base';
import { UserLabExamResultServices } from './services';
import { lazy } from 'solid-js';

const LabExams = lazy(() => import('./components/view'));
export class UserLabExamResultPage extends Page {
  readonly userLabExamResultAPI;

  readonly services;

  constructor() {
    super();
    this.userLabExamResultAPI = new UserLabExamResultAPI();
    this.services = new UserLabExamResultServices();
  }

  public getComponent() {
    return <LabExams context={this} />;
  }
}
