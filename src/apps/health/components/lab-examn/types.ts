import { UserLabExamResult } from 'entities/UserLabExamResult';
import { LabExamRangeTableData } from 'shared/services/medical-data/type';
import { Accessor, Setter } from 'solid-js';

export type RangeTableProps = {
  data: LabExamRangeTableData | undefined;
  isClosed?: boolean;
};
export type LabExamFormProps = {
  activeKey?: Accessor<string>;
  show: Accessor<boolean>;
  setShow: Setter<boolean>;
  create: (data: any) => void;
};
export type NewLabExam = Pick<
  UserLabExamResult,
  'labExamnId' | 'labExamnValue'
> & {
  date: string;
};
export type TableData = {
  id: string;
  userId: string;
  labExamId: string;
  labExamValue: number;
  userLabExamResultStatus: string;
  updatedAt: string;
  createdAt: string;
  createdAtHuman: string;
};
