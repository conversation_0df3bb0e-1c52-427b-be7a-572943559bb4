/* eslint-disable @typescript-eslint/naming-convention */
import {
  For,
  Match,
  Show,
  Switch,
  createEffect,
  createMemo,
  createSignal,
} from 'solid-js';
import { <PERSON>ton, Card, Col, Form, Nav, Row, Tab } from 'solid-bootstrap';
import { PageProps } from '../../../types';
import { useScreenWidth } from '../../../../../shared/hooks/use-screen-width';
import LabExamForm from './form';
import { useUserHealthContext } from '../../context';
import { UserLabExamResultPage } from '../main';
import BasicTableComponent from '../../../../../shared/components/table/basic-table';
import { backgroundDivStyle } from '../../../constants';
import { getCurrentUserData } from '../../../../../shared/services/user/user-session-management';
import EmptyTableIcon from '../../../../../shared/components/table/empty-table-icon';
import { useTable } from '../../../hooks/lab-examn/use-table';
import { MedicalDataService } from '../../../../../shared/services/medical-data/medical-data-services';
import { useUserLabExamResult } from '../../../../../shared/infra/hooks/use-user-lab-exam-result';
import { useMedicalDataReport } from '../../../../../shared/infra/hooks/use-medical-report';
import { LoadingAlert } from '../../../../../shared/components/alert/loading-alert';
import { ErrorAlert } from '../../../../../shared/components/alert/error-alert';
import { LabReportChart } from '../../../../../shared/components/medical-data/lab-report-chart';

const LabExams = ({
  context: mainContext,
}: PageProps<UserLabExamResultPage>) => {
  const context = useUserHealthContext();
  const { services } = mainContext;
  const screenWidth = useScreenWidth();
  const [show, setShow] = createSignal(false);
  const [activeKey, setActiveKey] = createSignal<string>('redBloodCells');
  const {
    isLoading: isLabExamResultLoading,
    error: errorFetchingLabExamResult,
    data: LabExamResult,
    create,
    archive: onDelete,
  } = useUserLabExamResult();
  const {
    isLoading: isMedicalDataReportLoading,
    error: errorFetchingMedicalDataReport,
    data: medicalDataReport,
    fetchMedicalReport,
  } = useMedicalDataReport();
  createEffect(() => {
    fetchMedicalReport();
    setTableData(services.mapUserLabExamResultToTableData(LabExamResult));
  });
  createEffect(() => {
    context?.setUserReport(medicalDataReport);
  });
  const { tableData, setTableData, header, control } = useTable({
    onDelete: onDelete,
  });
  const getLabExam = (id: string) => {
    return MedicalDataService.getLabExamDetailById(id);
  };
  const isLoading = () =>
    isLabExamResultLoading() || isMedicalDataReportLoading();
  const isDashboardEmpty = createMemo(() => {
    return medicalDataReport.data.length == 0;
  });
  const userMedicalDataReport = createMemo(() => [...medicalDataReport.data]);

  return (
    <>
      <Show
        when={
          isLoading() ||
          errorFetchingLabExamResult() ||
          errorFetchingMedicalDataReport()
        }
      >
        <Card.Body>
          <ErrorAlert
            error={[errorFetchingLabExamResult, errorFetchingMedicalDataReport]}
          />
          <LoadingAlert
            loadingFlags={[isLabExamResultLoading, isMedicalDataReportLoading]}
            showLoadingAnimation
          />
        </Card.Body>
      </Show>
      <Show when={!isLoading()}>
        <Card>
          <Switch>
            <Match when={context?.currentView() === 'DASHBOARD'}>
              <Card.Header style={backgroundDivStyle}>
                <div class={`${screenWidth() > 900 ? 'mt-10' : 'mt-1'} `}>
                  <h1 class="mt-5">Mediciones</h1>
                  <h5 class="mt-5 mb-5 text-capitalize">
                    {getCurrentUserData().name}
                  </h5>
                  <p
                    class={`health-page-subheader ${
                      screenWidth() > 900 ? 'mb-20' : 'mb-3'
                    } `}
                  >
                    Selecciona el criterio que te interesa consultar
                  </p>
                </div>
                <div class="card-toolbar ms-auto">
                  <div class="card-toolbar gap-2 gap-md-5">
                    <button
                      type="button"
                      onClick={() => {
                        context?.setCurrentView('TABLE');
                      }}
                      class="btn btn-sm btn-secondary align-self-center"
                    >
                      Editar
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShow(!show());
                      }}
                      class="btn btn-sm btn-primary align-self-center"
                    >
                      Nuevo Registro
                    </button>
                  </div>
                </div>
              </Card.Header>
              <Card.Body>
                <Show when={isDashboardEmpty()}>
                  <Row>
                    <EmptyTableIcon
                      class="m-auto mt-5"
                      style="height:200px"
                    ></EmptyTableIcon>
                    <label class="m-auto mt-8 text-center">
                      No se encontraron datos
                    </label>
                  </Row>
                </Show>
                <Show when={userMedicalDataReport().length > 0}>
                  <Tab.Container
                    id="left-tabs-example"
                    defaultActiveKey="redBloodCells"
                    activeKey={activeKey()}
                    onSelect={(key) => {
                      setActiveKey(key as string);
                    }}
                  >
                    <Row>
                      {screenWidth() > 900 && (
                        <Col sm={12} md={5} lg={3}>
                          <div
                            style={{
                              'max-height': '60vh',
                              'overflow-y': 'auto',
                            }}
                          >
                            <Nav class="nav nav-tabs nav-pills border-0 flex-row flex-md-column me-5 mb-3 mb-md-0 fs-6 py-4">
                              <For each={userMedicalDataReport()}>
                                {(data) => (
                                  <Nav.Item>
                                    <Nav.Link eventKey={data.id}>
                                      {getLabExam(data.id)?.title || data.title}
                                    </Nav.Link>
                                  </Nav.Item>
                                )}
                              </For>
                            </Nav>
                          </div>
                        </Col>
                      )}
                      {screenWidth() <= 900 && (
                        <Col sm={12} md={5} lg={3} class="my-10">
                          <Form.Select
                            value={activeKey()}
                            onInput={(event: Event) => {
                              const key = (
                                event.target as unknown as HTMLInputElement
                              ).value;
                              key && setActiveKey(key);
                            }}
                          >
                            <For each={userMedicalDataReport()}>
                              {(data) => (
                                <option value={data.id}>
                                  {getLabExam(data.id)?.title || data.title}
                                </option>
                              )}
                            </For>
                          </Form.Select>
                        </Col>
                      )}
                      <Col sm={12} md={7} lg={9}>
                        <Tab.Content>
                          <For each={userMedicalDataReport()}>
                            {(healthData) => (
                              <Tab.Pane
                                eventKey={healthData.id}
                                class={`${
                                  screenWidth() > 900 && 'ms-20'
                                } gap-10 text-left`}
                              >
                                <LabReportChart data={healthData} />
                              </Tab.Pane>
                            )}
                          </For>
                        </Tab.Content>
                      </Col>
                    </Row>
                  </Tab.Container>
                </Show>
              </Card.Body>
            </Match>
            <Match when={context?.currentView() === 'TABLE'}>
              <Card>
                <Card.Header>
                  <h3 class="card-title">Registros Personalizados</h3>
                  <div class="card-toolbar">
                    <div class="card-toolbar gap-2 gap-md-5">
                      <Button
                        type="button"
                        class="btn btn-sm btn-secondary align-self-center"
                        onClick={() => {
                          context?.setCurrentView('DASHBOARD');
                        }}
                      >
                        Regresar
                      </Button>
                    </div>
                  </div>
                </Card.Header>
                <Card.Body class="card-body card-toolbar">
                  <BasicTableComponent
                    title={'Registros Personalizados'}
                    data={tableData}
                    header={header}
                    control={control}
                    pageSize={15}
                    isTableStriped
                    isSearchEnabled
                  />
                </Card.Body>
              </Card>
            </Match>
          </Switch>
        </Card>
      </Show>
      <LabExamForm
        {...{
          activeKey,
          show,
          setShow,
          create,
        }}
      />
    </>
  );
};
export default LabExams;
