/* eslint-disable @typescript-eslint/naming-convention */
import { <PERSON><PERSON>, <PERSON>, Col, Form, Modal, Row } from 'solid-bootstrap';
import { For, createEffect, createSignal } from 'solid-js';
import { LabExamFormProps, NewLabExam } from '../types';
import { MedicalDataService } from '../../../../../shared/services/medical-data/medical-data-services';

const DEFAULT_LAB_EXAM_ID = 'redBloodCells';
const LabExamForm = (props: LabExamFormProps) => {
  const [date, setDate] = createSignal<string>(new Date().toISOString());
  const handleClose = () => props.setShow(false);
  const mapFromDateStringToInputDate = (date: string) => {
    return new Date(date).toISOString().split('T')[0];
  };
  const mapFromInputDateToISODate = (date: string) => {
    const time = new Date().toISOString().split('T')[1];

    return `${date}T${time}`;
  };
  const getDataFromForm = (event: Event): NewLabExam => {
    event.preventDefault();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const labExamValue = (event.target as any).labExamValue.value as number;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const labExamId = (event.target as any).labExamId.value as string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const date = (event.target as any).date.value;

    return {
      labExamnId: labExamId,
      labExamnValue: labExamValue,
      date: mapFromInputDateToISODate(date),
    };
  };
  const onSubmit = async (event: Event) => {
    const formData = getDataFromForm(event);
    props.create(formData);
    handleClose();
  };
  createEffect(() => {
    if (props.show()) {
      setDate(new Date().toISOString());
    }
  });

  return (
    <>
      <Modal
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        show={props.show()}
        onHide={handleClose}
      >
        <Form onSubmit={onSubmit}>
          <Modal.Header closeButton>
            <Modal.Title>Nuevo registro médico:</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div
              style={{
                'max-height': '60vh',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
              }}
            >
              <Card>
                <Form.Group class="mb-6 gap-md-3">
                  <Row>
                    <Col sm={12} md={12} lg={9}>
                      <Form.Label>Examen:</Form.Label>
                      <Form.Select
                        id="labExamId"
                        name="labExamId"
                        value={
                          props.activeKey
                            ? props.activeKey()
                            : DEFAULT_LAB_EXAM_ID
                        }
                      >
                        <For each={MedicalDataService.getLabExamDictionary()}>
                          {(option) => (
                            <option value={option.id}>{option.title}</option>
                          )}
                        </For>
                      </Form.Select>
                    </Col>
                    <Col sm={12} md={12} lg={3}>
                      <Form.Label>Valor:</Form.Label>
                      <Form.Control
                        type="number"
                        step="any"
                        value={0}
                        id="labExamValue"
                        name="labExamValue"
                      />
                    </Col>
                    <Col sm={12} md={12} lg={12}>
                      <Form.Label class="mt-10">Fecha:</Form.Label>
                      <Form.Control
                        type="date"
                        step="any"
                        value={mapFromDateStringToInputDate(date())}
                        onChange={(e) => {
                          const date = mapFromInputDateToISODate(
                            e.currentTarget.value
                          );
                          setDate(date);
                        }}
                        id="date"
                        name="date"
                        required
                      />
                    </Col>
                  </Row>
                </Form.Group>
              </Card>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              variant="secondary"
              onClick={handleClose}
              class="btn btn-sm"
            >
              Cancelar
            </Button>
            <Button variant="primary" type="submit" class="btn btn-sm">
              Guardar
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </>
  );
};
export default LabExamForm;
