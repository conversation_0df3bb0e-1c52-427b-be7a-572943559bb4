import { getCurrentUserData } from '../../../../shared/services/user/user-session-management';
import { Page } from '../../../../shared/services/app-base/page-base';
import { LifeEssentialServices } from './services';
import { NewHabit } from './types';
import { lazy } from 'solid-js';
import { LifeEssentialAPI } from '../../../../shared/infra';

const Habit = lazy(() => import('./components/view'));
export class LifeEssentialPage extends Page {
  readonly services;

  readonly api;

  constructor() {
    super();
    this.services = new LifeEssentialServices();
    this.api = new LifeEssentialAPI();
  }

  public async getUserDashboardData() {
    try {
      this.setIsLoading(true);
      const userId = getCurrentUserData().id;
      const result = await this.api.getLifeEssentialDashboardData(userId);

      return this.services.mapLifeEssentialDashboardDataToDashboardData(result);
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener reporte médico' });

      return [];
    } finally {
      this.setIsLoading(false);
    }
  }

  public async createLifeEssentialRacord(data: Partial<NewHabit>) {
    try {
      this.setIsLoading(true);
      data.userId = getCurrentUserData().id;
      await this.api.createLifeEssential(data as NewHabit);

      return true;
    } catch (error) {
      this.showErrorAlert({ message: 'Error al obtener reporte médico' });

      return false;
    } finally {
      this.setIsLoading(false);
    }
  }

  public getComponent() {
    return <Habit context={this} />;
  }
}
