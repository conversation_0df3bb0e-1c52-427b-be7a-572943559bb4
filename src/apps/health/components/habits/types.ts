import { NicotineExposure } from 'entities/Habit';
import { Accessor } from 'solid-js';

export type QuestionForm = {
  id: keyof NewHabit;
  type: string;
  question: string;
  tooltip: string;
  options?: {
    name: string;
    value: string;
  }[];
}[];
export type HabitFormProps = {
  onFormSubmit: (event: Event) => Promise<void>;
  glucose: Accessor<number>;
  show: Accessor<boolean>;
  handleClose: () => void;
};
// eslint-disable-next-line @typescript-eslint/naming-convention
export interface NewHabit extends IHabitProps {
  userId: string;
}
export interface IHabitProps {
  //Diet
  oliveOil: number;
  greenLeafyVegetables: number;
  otherVegetables: number;
  berries: number;
  otherFruit: number;
  meat: number;
  fish: number;
  chicken: number;
  cheese: number;
  butter: number;
  beans: number;
  wholeGrains: number;
  sweetsAndPastries: number;
  nuts: number;
  fastFood: number;
  alcohol: number;
  //PA (in minutes)
  physicalActivity: number;
  //Nicotine
  nicotineExposure: NicotineExposure | number;
  //Sleep Health (hours per night)
  sleep: number;
  //BMI
  bodyWeight: number; //kilograms
  height: number; // meters
  //Blood Lipids
  cholesterol: number;
  //Blood glucose
  glucose: number; //HbA1c
  //Blood Preasure
  systolic: number;
  diastolic: number;
  //metadata
}
export type LifeEssentialDashboardData = {
  date: string;
  overallScore: number;
  dietScore: number;
  physicalActivityScore: number;
  nicotineExposureScore: number;
  sleepScore: number;
  bodyWeightScore: number;
  cholesterolScore: number;
  glucoseScore: number;
  bloodPreasureScore: number;
};
export type DashboardData = {
  date: string;
  dateHuman: string;
  overallScore: number;
  dietScore: number;
  physicalActivityScore: number;
  nicotineExposureScore: number;
  sleepScore: number;
  bodyWeightScore: number;
  cholesterolScore: number;
  glucoseScore: number;
  bloodPreasureScore: number;
  dietVariant: string;
  physicalActivityVariant: string;
  nicotineExposureVariant: string;
  sleepVariant: string;
  bodyWeightVariant: string;
  cholesterolVariant: string;
  glucoseVariant: string;
  bloodPreasurVariant: string;
};
