/* eslint-disable no-case-declarations */
import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';
import { ApexOptions } from 'apexcharts';
import { Accessor } from 'solid-js';
import { Services } from '../../../../shared/services/app-base/service-base';
import { DashboardData, LifeEssentialDashboardData } from './types';
import { useTimeFormat } from '../../../../shared/hooks/use-time-format';

const CHART_BASE_OPTIONS: ApexOptions = {
  dataLabels: { enabled: true, style: { colors: ['#003AC2'] } },
  stroke: { show: true, curve: 'smooth', colors: ['#CBDBFF'] },
  markers: { size: 1 },
  noData: { text: 'No se encontró información' },
};
export class LifeEssentialServices extends Services {
  public renderChartOptions(
    medicalReport: UserMedicalDataReport | undefined,
    examnId: string,
    screenWidth: Accessor<number>
  ): ApexOptions {
    let categories: string[] = [];
    if (medicalReport) {
      const options = medicalReport?.data.find((e) => e.id === examnId);
      if (medicalReport && options) {
        const categoryOptions = options?.values?.map((e) => e.date);
        categories = categoryOptions;
      }
    }

    return {
      xaxis: { categories: categories.reverse() },
      chart: { height: screenWidth() > 900 ? 500 : 400 },
      ...CHART_BASE_OPTIONS,
    };
  }

  public renderChartSeries(
    medicalReport: UserMedicalDataReport | undefined,
    examnId: string
  ) {
    let values: number[] = [];
    const options = medicalReport?.data.find((e) => e.id === examnId);
    if (medicalReport && options) {
      const seriesOptions = options?.values
        ?.map((e) => e.examnResult)
        .map((e) => (this.isValidNumber(parseFloat(e)) ? parseFloat(e) : 0));
      values = seriesOptions;
    }

    return [{ name: 'Total', data: values.reverse() }];
  }

  public isValidNumber(value: unknown): boolean {
    return typeof value === 'number' && !Number.isNaN(value);
  }

  public mapLifeEssentialDashboardDataToDashboardData(
    lifeEssentialDashboardData: LifeEssentialDashboardData[]
  ): DashboardData[] {
    const getProgressbarVariant = (score: number | undefined): string => {
      if (!score) return 'info';
      const statusMap = [
        { range: [0, 40], status: 'danger' },
        { range: [41, 80], status: 'warning' },
        { range: [81, Infinity], status: 'success' },
      ];
      const matchingStatus = statusMap.find(
        ({ range }) => score >= range[0] && score <= range[1]
      );

      return matchingStatus ? matchingStatus.status : 'success';
    };

    return lifeEssentialDashboardData
      .map((data) => {
        return {
          date: data.date,
          dateHuman: useTimeFormat(data.date),
          overallScore: data.overallScore,
          dietScore: data.dietScore,
          dietVariant: getProgressbarVariant(data.dietScore),
          physicalActivityScore: data.physicalActivityScore,
          physicalActivityVariant: getProgressbarVariant(
            data.physicalActivityScore
          ),
          nicotineExposureScore: data.nicotineExposureScore,
          nicotineExposureVariant: getProgressbarVariant(
            data.nicotineExposureScore
          ),
          sleepScore: data.sleepScore,
          sleepVariant: getProgressbarVariant(data.sleepScore),
          bodyWeightScore: data.bodyWeightScore,
          bodyWeightVariant: getProgressbarVariant(data.bodyWeightScore),
          cholesterolScore: data.cholesterolScore,
          cholesterolVariant: getProgressbarVariant(data.cholesterolScore),
          glucoseScore: data.glucoseScore,
          glucoseVariant: getProgressbarVariant(data.glucoseScore),
          bloodPreasureScore: data.bloodPreasureScore,
          bloodPreasurVariant: getProgressbarVariant(data.bloodPreasureScore),
        };
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }
}
