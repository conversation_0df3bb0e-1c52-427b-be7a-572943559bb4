/* eslint-disable @typescript-eslint/naming-convention */
import {
  But<PERSON>,
  Card,
  Col,
  Form,
  Modal,
  OverlayTrigger,
  Row,
  Tooltip,
} from 'solid-bootstrap';
import { For } from 'solid-js';
import { HabitFormProps } from '../types';
import { QUESTIONNAIRE } from '../constants';

const HabitForm = (props: HabitFormProps) => {
  return (
    <Modal
      size="lg"
      aria-labelledby="contained-modal-title-vcenter"
      centered
      show={props.show()}
      onHide={props.handleClose}
    >
      <Form onSubmit={props.onFormSubmit}>
        <Modal.Header closeButton>
          <Modal.Title>Cuestionario de Hábitos Saludables</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <>
            <div
              style={{
                'max-height': '60vh',
                'overflow-y': 'auto',
                'overflow-x': 'hidden',
              }}
            >
              <For each={QUESTIONNAIRE}>
                {(question) => (
                  <Card>
                    <Form.Group class="mb-6">
                      <Row>
                        <Col sm={11} md={11} lg={8}>
                          <Row>
                            <div>
                              <Form.Label>{question.question} </Form.Label>
                            </div>
                            <div></div>
                          </Row>
                        </Col>
                        <Col sm={1} md={1} lg={1}>
                          <Row>
                            <div class="my-auto">
                              {question.tooltip && (
                                <OverlayTrigger
                                  placement="top"
                                  delay={{ show: 100, hide: 100 }}
                                  overlay={
                                    <Tooltip id="button-tooltip">
                                      {question.tooltip}
                                    </Tooltip>
                                  }
                                >
                                  <i
                                    style={{ cursor: 'pointer' }}
                                    class="bi bi-question-circle fs-1x my-auto"
                                  ></i>
                                </OverlayTrigger>
                              )}
                            </div>
                          </Row>
                        </Col>
                        <Col sm={12} md={12} lg={3}>
                          {question.type === 'select' && (
                            <Form.Select
                              name={question.id}
                              autocomplete="off"
                              required
                            >
                              <For each={question.options}>
                                {(option) => (
                                  <option value={option.value}>
                                    {option.name}
                                  </option>
                                )}
                              </For>
                            </Form.Select>
                          )}
                          {question.type === 'number' &&
                            question.id !== 'nicotineExposure' &&
                            question.id === 'glucose' && (
                              <Form.Control
                                type={question.type}
                                name={question.id}
                                value={props.glucose()}
                                autocomplete="off"
                                min="0"
                                required
                              />
                            )}
                          {question.type === 'number' &&
                            question.id !== 'nicotineExposure' &&
                            question.id !== 'glucose' && (
                              <Form.Control
                                type={question.type}
                                name={question.id}
                                autocomplete="off"
                                min="0"
                                required
                              />
                            )}
                        </Col>
                      </Row>
                    </Form.Group>
                  </Card>
                )}
              </For>
            </div>
          </>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={props.handleClose}
            class="btn btn-sm"
          >
            Cancelar
          </Button>
          <Button variant="primary" class="btn btn-sm" type="submit">
            Guardar
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};
export default HabitForm;
