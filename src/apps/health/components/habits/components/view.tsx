/* eslint-disable @typescript-eslint/no-explicit-any */
import { For, createEffect, createSignal, onMount } from 'solid-js';
import { Accordion, Card, Col, Form, Row } from 'solid-bootstrap';
import { SolidApexCharts } from 'solid-apexcharts';
import { DashboardData } from '../types';
import { useScreenWidth } from '../../../../../shared/hooks/use-screen-width';
import { PageProps } from '../../../types';
import { LifeEssentialPage } from '../main';
import Animation from '../../../../../shared/components/lottie-player/animation';
import { useUserHealthContext } from '../../context';
import HabitForm from './form';
import { backgroundDivStyle } from '../../../constants';
import { useForm } from '../../../hooks/habits/use-form';
import { useChart } from '../../../hooks/habits/use-chart';
import EmptyTableIcon from '../../../../../shared/components/table/empty-table-icon';
import { MeasureTooltip } from './measure-tooltip';
import { DateServices } from '../../../../../shared/services/utils/date-services';

const [chartSeries, setChartSeries] = createSignal<ApexAxisChartSeries>([], {
  equals: false,
});
const [show, setShow] = createSignal(false);
const handleOpen = () => setShow(true);
const handleClose = () => setShow(false);
const [dashboardData, setDashboardData] = createSignal<DashboardData[]>([], {
  equals: false,
});
const [currentDashboardData, setCurrentDashboardData] =
  createSignal<DashboardData>();
const Habit = ({ context: mainContext }: PageProps<LifeEssentialPage>) => {
  const {
    chartOptions,
    setChartOptions,
    chartOptionsGauge,
    setChartOptionsGauge,
  } = useChart();
  const [glucose, setGlucose] = createSignal<number>(0);
  const screenWidth = useScreenWidth();
  const context = useUserHealthContext();
  createEffect(() => {
    setChartOptionsGauge((prev) => {
      if (prev.chart?.height) {
        prev.chart.height = screenWidth() > 900 ? 500 : 300;
      }

      return prev;
    });
    setChartOptions((prev) => {
      if (prev.chart?.height) {
        prev.chart.height = screenWidth() > 900 ? 500 : 300;
      }

      return prev;
    });
  });
  const loadDashboard = async () => {
    const dashboardData = await mainContext.getUserDashboardData();
    setDashboardData(dashboardData);
    setCurrentDashboardData(dashboardData[0]);
    setChartSeries([
      {
        name: 'Total',
        data: dashboardData.reverse().map((data) => data.overallScore),
      },
    ]);
    setChartOptions((prev) => {
      if (prev.xaxis)
        prev.xaxis.categories = dashboardData.map((data) =>
          DateServices.fromUTCDateToLocalDate(data.date, 'dd-MM-yyyy')
        );

      return prev;
    });
  };
  const setGlucoseInitialValue = () => {
    const glucosePreExistingValue =
      parseFloat(
        context?.userReport()?.data.find((e) => e.id === 'glycemia')
          ?.lastValue || '0'
      ) || 0;
    setGlucose(glucosePreExistingValue);
  };
  const handleDashboardDateSelectChanged = (event: Event) => {
    const date = (event.target as unknown as HTMLInputElement).value;
    const newData = dashboardData()?.find((data) => data.date === date);
    if (newData) setCurrentDashboardData(newData);
  };
  const onFormSubmit = async (event: Event) => {
    event.preventDefault();
    const data = useForm(event);
    if (!data) return;
    setShow(false);
    await mainContext.createLifeEssentialRacord(data);
    await loadDashboard();
  };
  createEffect(() => {
    if (context?.userReport()) {
      setGlucoseInitialValue();
    }
  });
  onMount(async () => {
    await loadDashboard();
  });

  return (
    <>
      {mainContext.isLoading() && (
        <div class="card-body card-toolbar">
          <Animation />
        </div>
      )}
      {!mainContext.isLoading() && (
        <>
          <div class="card-header" style={backgroundDivStyle}>
            <div class={`${screenWidth() > 900 ? 'mt-10' : 'mt-1'} `}>
              <h1 class="mt-5">Hábitos Saludables</h1>
              <p
                class={`health-page-subheader ${
                  screenWidth() > 900 ? 'mb-10' : 'mb-3'
                } `}
              >
                Esta sección recopila información sobre tu estilo de vida y te
                brinda una perspectiva sobre qué hábitos podes mejorar para
                vivir más saludable. Las métricas de hábitos están basadas en el
                Life´s Essential 8 de la American Heart Association que recopiló
                los 8 factores que más impactan en la salud cardiovascular de
                cada individuo así como estos factores también son factores de
                riesgo para otras enfermedades crónicas. Este cuestionario se
                puede repetir varias veces durante el año para ver cómo va
                cambiando tu resultado conforme vayas realizando cambios en tus
                hábitos diarios.
              </p>
              <p
                class={`health-page-subheader ${
                  screenWidth() > 900 ? 'mb-20' : 'mb-3'
                } `}
              >
                Referencia: &nbsp;
                <a href="https://doi.org/10.1161/CIR.0000000000001078">
                  Life’s Essential 8
                </a>
                &nbsp; y &nbsp;
                <a href="https://doi.org/10.1111/jhn.12451">
                  Evaluation of a dietary screener
                </a>
              </p>
            </div>
            <div class="card-toolbar">
              <div class="card-toolbar gap-2 gap-md-5">
                <button
                  type="button"
                  onClick={handleOpen}
                  class="btn btn-sm btn-primary align-self-center"
                >
                  Nuevo Registro
                </button>
              </div>
            </div>
          </div>
          <Card>
            {!currentDashboardData() && (
              <>
                <Row class="mt-20 mb-20 pb-20">
                  <EmptyTableIcon
                    class="m-auto mt-5"
                    style="height:200px"
                  ></EmptyTableIcon>
                  <label class="m-auto mt-8 text-center">
                    No se encontraron datos
                  </label>
                </Row>
              </>
            )}
            {currentDashboardData() && (
              <>
                <Card.Header class="py-10">
                  <Form.Label class="card-title form-label mb-6">
                    Registros:
                  </Form.Label>
                  <Form.Select
                    size="lg"
                    value={currentDashboardData()?.date}
                    onChange={handleDashboardDateSelectChanged}
                  >
                    <For each={dashboardData()}>
                      {(data) => (
                        <option value={data.date}>{data.dateHuman}</option>
                      )}
                    </For>
                  </Form.Select>
                </Card.Header>
                <Card.Body>
                  <div
                    class={`text-center mb-5 ${
                      screenWidth() > 900 ? 'px-10' : 'px-0 '
                    }`}
                  >
                    <div class="w-100">
                      <h4 class="text-center mt-20 mb-8">
                        Evaluación General de Hábitos Saludables
                      </h4>
                    </div>
                    <SolidApexCharts
                      options={chartOptionsGauge()}
                      series={[currentDashboardData()?.overallScore || 0]}
                      type="radialBar"
                      height={350}
                    />
                  </div>
                  <Row>
                    <Col
                      sx={12}
                      md={12}
                      lg={6}
                      class={`${screenWidth() > 900 ? 'px-20' : 'px-0'}`}
                    >
                      <MeasureTooltip
                        name="Dieta"
                        variant={currentDashboardData()?.dietVariant}
                        now={currentDashboardData()?.dietScore || 0}
                      />
                      <MeasureTooltip
                        name="Actividad Física"
                        variant={
                          currentDashboardData()?.physicalActivityVariant
                        }
                        now={currentDashboardData()?.physicalActivityScore || 0}
                      />
                      <MeasureTooltip
                        name="Nicotina"
                        variant={
                          currentDashboardData()?.nicotineExposureVariant
                        }
                        now={currentDashboardData()?.nicotineExposureScore || 0}
                      />
                      <MeasureTooltip
                        name="Sueño"
                        variant={currentDashboardData()?.sleepVariant}
                        now={currentDashboardData()?.sleepScore || 0}
                      />
                    </Col>
                    <Col
                      sx={12}
                      md={12}
                      lg={6}
                      class={`${screenWidth() > 900 ? 'px-20' : 'px-0 mt-2'}`}
                    >
                      <MeasureTooltip
                        name="Masa Corporal"
                        variant={currentDashboardData()?.bodyWeightVariant}
                        now={currentDashboardData()?.bodyWeightScore || 0}
                      />
                      <MeasureTooltip
                        name="Colesterol"
                        variant={currentDashboardData()?.cholesterolVariant}
                        now={currentDashboardData()?.cholesterolScore || 0}
                      />
                      <MeasureTooltip
                        name="Glucosa"
                        variant={currentDashboardData()?.glucoseVariant}
                        now={currentDashboardData()?.glucoseScore || 0}
                      />
                      <MeasureTooltip
                        name="Presión Arterial"
                        variant={currentDashboardData()?.bloodPreasurVariant}
                        now={currentDashboardData()?.bloodPreasureScore || 0}
                      />
                    </Col>
                  </Row>
                </Card.Body>
                <Accordion defaultActiveKey="0" class="mt-20">
                  <Accordion.Item eventKey="0">
                    <Accordion.Header>Progreso</Accordion.Header>
                    <Accordion.Body>
                      <div
                        class={`"mt-20 ${
                          screenWidth() > 900 ? 'px-20 mx-20' : 'px-0 mx-0'
                        }`}
                      >
                        <SolidApexCharts
                          key={JSON.stringify(chartOptions)}
                          options={chartOptions()}
                          series={chartSeries()}
                          type="line"
                        />
                      </div>
                    </Accordion.Body>
                  </Accordion.Item>
                </Accordion>
              </>
            )}
          </Card>
        </>
      )}
      <HabitForm
        {...{ onFormSubmit: onFormSubmit, glucose, show, handleClose }}
      />
    </>
  );
};
export default Habit;
