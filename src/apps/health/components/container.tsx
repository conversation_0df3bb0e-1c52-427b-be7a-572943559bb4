import { createEffect, createMemo, createSignal, onMount } from 'solid-js';
import { Card, Tab, Tabs } from 'solid-bootstrap';
import { useUserHealthContext } from './context';
import { LifeEssentialPage } from './habits/main';
import { UserLabExamResultPage } from './lab-examn/main';

export type VerticalMenuOptions = 'HEALTH' | 'LAB_EXAMS';
const UserHealthContainer = (props: { view: VerticalMenuOptions }) => {
  const [activeKey, setActiveKey] = createSignal<VerticalMenuOptions>(
    props.view || 'HEALTH'
  ); // Default active key
  const context = useUserHealthContext();
  const habits = createMemo(() => {
    return new LifeEssentialPage();
  });
  const labExams = createMemo(() => {
    return new UserLabExamResultPage();
  });
  createEffect(() => {
    if (activeKey()) {
      context?.setCurrentView('DASHBOARD');
    }
  });
  onMount(() => {
    context?.setCurrentView('DASHBOARD');
  });

  return (
    <>
      <Card class="card mb-5 mb-xl-10 p-5">
        <Tabs
          defaultActiveKey={'HEALTH'}
          activeKey={activeKey()}
          onSelect={(key) => {
            setActiveKey(key as VerticalMenuOptions);
          }}
          class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6"
        >
          <Tab eventKey="HEALTH" title="Hábitos Saludables" class="nav-link">
            <Card class="card-body p-0">{habits().getComponent()}</Card>
          </Tab>
          <Tab
            eventKey="LAB_EXAMS"
            title="Examenes de Laboratorio"
            class="nav-link"
          >
            <Card class="card-body p-0">{labExams().getComponent()}</Card>
          </Tab>
        </Tabs>
      </Card>
    </>
  );
};
export default UserHealthContainer;
