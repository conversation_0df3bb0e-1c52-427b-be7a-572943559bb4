import { ICognitoError } from 'shared/services/user/types';
import { Services } from '../../../shared/services/app-base/service-base';
import { UserProps } from './types';

const USER_GENERIC_AVATAR = '../../../assets/media/icons/blank.png';
export class SignInServices extends Services {
  public manageUserData(userInformation: UserProps) {
    try {
      if (Object.keys(userInformation).length === 0)
        throw 'Missing user information';
      userInformation.profilePicture =
        userInformation.profilePicture || USER_GENERIC_AVATAR;
      localStorage.setItem('user-local-data', JSON.stringify(userInformation));

      return true;
    } catch (error) {
      return false;
    }
  }

  public getContextualizedErrorMessage(error: ICognitoError): string {
    const errorMessage = 'Error al restablecer contraseña .';
    try {
      if (error?.code === 'NotAuthorizedException') {
        return 'Nombre de usuario o contraseña incorrecta';
      }
      if (error?.code === 'CodeMismatchException') {
        return 'Código Invalido';
      }

      return errorMessage;
    } catch (error) {
      return errorMessage;
    }
  }
}
