import { User } from 'entities/User';
import { APIService } from 'shared/infra/service-library';
import { Setter } from 'solid-js';
import { SignIn } from './main';

export type UserProps = User;
export type LoginForm = {
  username: string;
  password: string;
};
export type ViewOptions = 'SIGN_IN' | 'MFA' | 'NEW_PASSWORD';
export type SingInFormProps = {
  form: string;
  setView: Setter<ViewOptions>;
  context: SignIn;
};
export type SingInSetNewPasswordProps = {
  setView: Setter<ViewOptions>;
  context: SignIn;
};
export type AuthenticateUserResponse = {
  response: 'SUCCESS' | 'MFA' | 'NEW_PASSWORD';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
};
export type IncomingRequest = {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: unknown;
  params?: unknown;
};
export type CustomIncomingRequest = {
  api: APIService;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: unknown;
  params?: unknown;
};
export type AvailablePools = 'ADMIN' | 'USER';
