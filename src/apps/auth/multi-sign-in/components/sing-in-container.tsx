import { Tab, Tabs } from 'solid-bootstrap';
import { createSignal, Match, onMount, Switch } from 'solid-js';
import BeBetterLogo from '../../../../assets/media/logos/bebetter.png';
import { SignIn } from '../main';
import { AvailablePools, ViewOptions } from '../types';
import SingInForm from './sing-in-form';
import SingInMFA from './sing-in-mfa';
import SingInSetNewPassword from './sing-in-set-new-password';
import './style.css';

const [view, setView] = createSignal<ViewOptions>('SIGN_IN');
const [key, setKey] = createSignal('USER');
const SingInContainer = (props: { context: SignIn }) => {
  onMount(() => {
    props.context.api.init();
  });

  return (
    <>
      <div
        class="form w-100"
        id="kt_sign_in_form"
        data-kt-redirect-url="../home/"
      >
        <div class="d-flex flex-row-fluid flex-column text-center p-5 p-lg-10 pt-lg-20">
          <a href="#" class="py-1 py-lg-10">
            <img alt="Logo" src={BeBetterLogo} class="h-80px h-lg-90px" />
          </a>
        </div>
        <Switch>
          <Match when={view() === 'SIGN_IN'}>
            <Tabs
              id="controlled-tab-example"
              activeKey={key()}
              onSelect={(k) => {
                setKey(k || 'USER');
                props.context.api.userPool = (k as AvailablePools) || 'USER';
                props.context.api.init();
              }}
              class="nav-line-tabs nav-line-tabs-2x mb-3"
            >
              <Tab class="m-auto" eventKey="USER" title="Usuarios">
                <SingInForm
                  form={key()}
                  setView={setView}
                  context={props.context}
                />
              </Tab>
              <Tab class="m-auto" eventKey="ADMIN" title="Administrador">
                <SingInForm
                  form={key()}
                  setView={setView}
                  context={props.context}
                />
              </Tab>
            </Tabs>
          </Match>
          <Match when={view() === 'NEW_PASSWORD'}>
            <SingInSetNewPassword setView={setView} context={props.context} />
          </Match>
          <Match when={view() === 'MFA'}>
            <SingInMFA context={props.context} />
          </Match>
        </Switch>
      </div>
    </>
  );
};
export default SingInContainer;
