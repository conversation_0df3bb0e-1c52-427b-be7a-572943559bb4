import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { SingInFormProps } from '../types';

const [isLoading, setIsLoading] = createSignal<boolean>(false);
const [form, setForm] = createSignal({
  username: '',
  password: '',
});
const SingInForm = (props: SingInFormProps) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    const result = await props.context.login(form());
    if (result?.response === 'NEW_PASSWORD') props.setView('NEW_PASSWORD');
    if (result?.response === 'MFA') props.setView('MFA');
    setIsLoading(false);
  };

  return (
    <>
      <Form onSubmit={onFormSubmit} id="login-form">
        <div class="fv-row mb-10">
          <label class="form-label fs-6 fw-bold text-dark">Username</label>
          <input
            class="form-control form-control-lg form-control-solid"
            type="text"
            name="username"
            id="username"
            autocomplete="off"
            required
            value={form().username}
            onInput={(e) => {
              setForm((form) => {
                form.username = (e.target as HTMLInputElement).value || '';

                return form;
              });
            }}
            disabled={isLoading()}
          />
        </div>
        <div class="fv-row mb-10">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">
              Contraseña
            </label>
            <a
              href={`../reset-password/?user=` + props.form}
              role="button"
              class="link-primary fs-6 fw-bold"
            >
              ¿Olvidó su Contraseña?
            </a>
          </div>
          <input
            class="form-control form-control-lg form-control-solid"
            type="password"
            name="password"
            id="password"
            autocomplete="off"
            required
            value={form().password}
            onInput={(e) => {
              setForm((form) => {
                form.password = (e.target as HTMLInputElement).value || '';

                return form;
              });
            }}
            disabled={isLoading()}
          />
        </div>
        <div class="text-center">
          <button
            type="submit"
            class="btn btn-lg btn-primary w-100 mb-5"
            data-kt-indicator={isLoading() && 'on'}
            disabled={isLoading()}
          >
            <span class="indicator-label">Continuar</span>
            <span class="indicator-progress">
              Espere...
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </button>
        </div>
      </Form>
    </>
  );
};
export default SingInForm;
