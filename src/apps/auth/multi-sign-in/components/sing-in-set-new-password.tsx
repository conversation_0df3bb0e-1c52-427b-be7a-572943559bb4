import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { SingInSetNewPasswordProps } from '../types';

const [passwod, setNewPassword] = createSignal('');
const SingInSetNewPassword = (props: SingInSetNewPasswordProps) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    const result = await props.context.setNewPassword({ password: passwod() });
    if (result?.response === 'NEW_PASSWORD') props.setView('NEW_PASSWORD');
    if (result?.response === 'MFA') props.setView('MFA');
  };

  return (
    <>
      <Form onSubmit={onFormSubmit} class="mt-3" id="new-passwod-form">
        <div class="fv-row mb-10">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">
              Nueva Contraseña
            </label>
          </div>
          <input
            id="new-password"
            class="form-control form-control-lg form-control-solid"
            type="password"
            name="password"
            autocomplete="off"
            required
            onInput={(e) => {
              setNewPassword((e.target as HTMLInputElement).value || '');
            }}
          />
        </div>
        <div class="mt-3">
          <button
            type="submit"
            id="new-password-btn"
            class="btn btn-lg btn-primary w-100 mb-5"
          >
            <span class="indicator-label">Ingresar</span>
            <span class="indicator-progress">
              Espere...
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </button>
        </div>
      </Form>
    </>
  );
};
export default SingInSetNewPassword;
