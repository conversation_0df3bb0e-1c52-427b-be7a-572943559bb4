import { Form } from 'solid-bootstrap';
import { createSignal, onMount } from 'solid-js';
import { SignIn } from '../main';

const [isResendEnabled, setIsResendEnabled] = createSignal<boolean>(false);
const [isLoading, setIsLoading] = createSignal<boolean>(false);
const [smsCode, setSmsCode] = createSignal<string>('');
const SingInMFA = (props: { context: SignIn }) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    await props.context.codeVerification(smsCode());
    setIsLoading(false);
  };
  const resendMFCode = async () => {
    await props.context.api.resendVrificateCode();
  };
  onMount(() => {
    setTimeout(() => {
      setIsResendEnabled(false);
    }, 5000);
  });

  return (
    <>
      <Form onSubmit={onFormSubmit} class="mt-3" id="new-passwod-form">
        <div class="fv-row mb-6">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">Código</label>
          </div>
          <input
            id="sms-code"
            class="form-control form-control-lg form-control-solid"
            type="text"
            name="sms-code"
            autocomplete="off"
            required
            value={smsCode()}
            onInput={(e) => {
              setSmsCode((e.target as HTMLInputElement).value || '');
            }}
            disabled={isLoading()}
          />
        </div>
        {isResendEnabled() && (
          <div class="text-center mb-6">
            <a
              class="btn btn-link btn-active-color-primary me-5 mb-2"
              role="button"
              onClick={resendMFCode}
            >
              Re-enviar código
            </a>
          </div>
        )}
        <div class="mt-3">
          <button
            type="submit"
            class="btn btn-lg btn-primary w-100 mb-5"
            data-kt-indicator={isLoading() && 'on'}
            disabled={isLoading()}
          >
            <span class="indicator-label">Ingresar</span>
            <span class="indicator-progress">
              Espere...
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </button>
        </div>
      </Form>
    </>
  );
};
export default SingInMFA;
