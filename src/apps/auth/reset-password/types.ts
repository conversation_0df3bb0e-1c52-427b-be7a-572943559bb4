import { Setter } from 'solid-js';
import { RestePasswordPage } from './main';

export type AuthenticateUserResponse = {
  response: 'SUCCESS' | 'MFA' | 'NEW_PASSWORD';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
};
export type ViewOptions = 'PRE_FORM' | 'FORM';
export type ResetPasswordPreFormProps = {
  setView: Setter<ViewOptions>;
  context: RestePasswordPage;
};
export type ResetPasswordFormProps = {
  context: RestePasswordPage;
};
