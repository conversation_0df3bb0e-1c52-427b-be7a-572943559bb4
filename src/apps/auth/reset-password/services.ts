import { ICognitoError } from 'shared/services/user/types';
import { Services } from '../../../shared/services/app-base/service-base';
import { UserProps } from '../sign-in/types';

const USER_GENERIC_AVATAR = '../../../assets/media/icons/blank.png';
export class ResetPasswordServices extends Services {
  public getContextualizedErrorMessage(error: ICognitoError): string {
    const errorMessage = 'Error al restablecer contraseña .';
    try {
      if (error.code == 'LimitExceededException') {
        return 'Limite de intentos excedido. Intente mas tarde.';
      }
      if (
        error.code == 'InvalidParameterException' ||
        error.code == 'InvalidPasswordException'
      ) {
        return 'Error en el formato de la contraseña.';
      }

      return errorMessage;
    } catch (error) {
      return errorMessage;
    }
  }

  public manageUserData(userInformation: UserProps) {
    try {
      if (Object.keys(userInformation).length === 0)
        throw 'Missing user information';
      userInformation.profilePicture =
        userInformation.profilePicture || USER_GENERIC_AVATAR;
      localStorage.setItem('user-local-data', JSON.stringify(userInformation));

      return true;
    } catch (error) {
      return false;
    }
  }

  public getURLParams(): string {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);

    return urlParams.get('user') || 'USER';
  }
}
