import { ICognitoError } from 'shared/services/user/types';
import { render } from 'solid-js/web';
import Swal from 'sweetalert2';
import { Page } from '../../../shared/services/app-base/page-base';
import { ResetPasswordAPI } from './api';
import ResetPasswordContainer from './components/reset-password-container';
import { ResetPasswordServices } from './services';
import { BodyAnimationService } from '../../../shared/services/utils/body-animation-service';

window.addEventListener('load', async () => {
  new RestePasswordPage();
});
export class RestePasswordPage extends Page {
  private api;

  private services;

  private username: string;

  constructor() {
    super();
    this.services = new ResetPasswordServices();
    this.renderComponents();
    this.api = new ResetPasswordAPI();
    BodyAnimationService.changeOpacity();
  }

  public async verifyUser(username: string): Promise<boolean> {
    try {
      this.username = username;
      if (!username) throw 'Complete Form';
      const result = await this.api.sendVerificationCode(username);

      return result.response === 'SUCCESS' ? true : false;
    } catch (error) {
      this.logger(error);
      this.showErrorAlert({
        message: this.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });

      return false;
    }
  }

  public async resetPassword({
    code,
    password,
  }: {
    code: string;
    password: string;
  }) {
    try {
      this.showLoadingScreen();
      if (!code || !password) throw 'complete form';
      const result = await this.api.resetUserPassword(code, password);
      if (result.response === 'SUCCESS') {
        Swal.fire({
          title: 'Éxito!',
          text: `Contraseña actualizada para el usuario ${this.username}.`,
          icon: 'success',
          confirmButtonText: 'Continuar',
        }).then(() => {
          location.href = '../sign-in/';
        });
      } else {
        throw 'Error al restablecer la contraseña';
      }
    } catch (error) {
      this.logger(error);
      this.showErrorAlert({
        message: this.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });
    } finally {
      this.hideLoadingScreen();
    }
  }

  public async codeVerification(code: number) {
    try {
      if (!code) {
        this.showErrorAlert({ message: 'Información invalida' });

        return;
      }
      const result = await this.api.verificateCode(code);
      await this.getUserInformation(result.data.accessToken.payload.username);
    } catch (error) {
      localStorage.clear();
      this.logger(error);
      this.showErrorAlert({
        message: this.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });
    }
  }

  public async login(newPassword: string) {
    try {
      this.showLoadingScreen();
      const result = await this.api.login({
        username: this.username,
        password: newPassword,
      });
      if (result.response === 'SUCCESS')
        await this.getUserInformation(result.data.accessToken.payload.username);
    } catch (error) {
      localStorage.clear();
      this.showErrorAlert({ message: 'Error al realizar login' });
      this.logger(error);
    }
  }

  private async getUserInformation(username: string) {
    try {
      const userInformation = await this.api.getUserInformation(username);
      const isUserDataValid = this.services.manageUserData(userInformation);
      if (!isUserDataValid) {
        this.api.cognitoUserPool.getCurrentUser()?.signOut();
        this.showErrorAlert({
          message: 'Ha ocurrido un error con la información del usuario.',
        });
        location.reload();
      }
      setTimeout(() => {
        this.pageRedirect('../../home/<USER>');
      }, 300);
    } catch (error) {
      localStorage.clear();
      this.logger(error);
      this.showErrorAlert({
        message: 'Error al almacenar la información del usuario',
      });
      this.api.cognitoUserPool.getCurrentUser()?.signOut();
    }
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer = this.services.getHTMLElement(
        'reset-password-container'
      );
      render(
        () => (
          <>
            <ResetPasswordContainer context={this} />
          </>
        ),
        userProfileContainer
      );
    }
  }
}
