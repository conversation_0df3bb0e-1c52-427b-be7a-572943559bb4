import { createSignal, Match, Switch } from 'solid-js';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../../assets/media/logos/bebetter.png';
import { RestePasswordPage } from '../main';
import { ViewOptions } from '../types';
import ResetPasswordForm from './reset-password-form';
import ResetPasswordPreForm from './reset-password-pre-form';

const [view, setView] = createSignal<ViewOptions>('PRE_FORM');
const ResetPasswordContainer = (props: { context: RestePasswordPage }) => {
  return (
    <>
      <div
        class="form w-100"
        id="kt_sign_in_form"
        data-kt-redirect-url="../home/"
      >
        <div class="d-flex flex-row-fluid flex-column text-center p-5 p-lg-10 pt-lg-20 pb-0">
          <a href="#" class="pt-1 pt-lg-10">
            <img alt="Logo" src={BeBetterLogo} class="h-150px h-lg-180px" />
          </a>
        </div>
        <div class="fv-row mb-8">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">
              Ingrese los datos para continuar.
            </label>
          </div>
        </div>
        <Switch>
          <Match when={view() === 'PRE_FORM'}>
            <ResetPasswordPreForm setView={setView} context={props.context} />
          </Match>
          <Match when={view() === 'FORM'}>
            <ResetPasswordForm context={props.context} />
          </Match>
        </Switch>
      </div>
    </>
  );
};
export default ResetPasswordContainer;
