import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { ResetPasswordPreFormProps } from '../types';

const [isLoading, setIsLoading] = createSignal<boolean>(false);
const [user, setUser] = createSignal('');
const ResetPasswordPreForm = (props: ResetPasswordPreFormProps) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    const isSuccess = await props.context.verifyUser(user());
    isSuccess && props.setView('FORM');
    setIsLoading(false);
  };

  return (
    <>
      <Form onSubmit={onFormSubmit}>
        <div class="mt-3" id="user-form">
          <div class="fv-row mb-10">
            <label class="form-label fs-6 fw-bold text-dark">
              Correo <PERSON>:
            </label>
            <input
              class="form-control form-control-lg form-control-solid"
              type="email"
              name="user"
              id="username"
              autocomplete="off"
              pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
              required
              value={user()}
              onInput={(e) => {
                setUser((e.target as HTMLInputElement).value || '');
              }}
              disabled={isLoading()}
            />
          </div>
          <div class="text-center">
            <button
              type="submit"
              class="btn btn-lg btn-primary w-100 mb-5"
              data-kt-indicator={isLoading() && 'on'}
              disabled={isLoading()}
            >
              <span class="indicator-label">Continuar</span>
              <span class="indicator-progress">
                Espere...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </button>
          </div>
          <div class="text-center">
            <a
              id="continue-btn"
              class="btn btn-lg btn-dark w-100 mb-5"
              href="../sign-in/"
            >
              <span class="indicator-label">Regresar</span>
            </a>
          </div>
        </div>
      </Form>
    </>
  );
};
export default ResetPasswordPreForm;
