import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { ResetPasswordFormProps } from '../types';

const [isLoading, setIsLoading] = createSignal<boolean>(false);
const [form, setForm] = createSignal({
  code: '',
  password: '',
});
const ResetPasswordForm = (props: ResetPasswordFormProps) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    await props.context.resetPassword(form());
    setIsLoading(false);
  };

  return (
    <>
      <Form onSubmit={onFormSubmit} autocomplete="none">
        <div class="mt-3" id="new-password-form">
          <div class="fv-row mb-10">
            <label class="form-label fs-6 fw-bold text-dark">Código:</label>
            <div>
              <small>Ingrese el código que se le ha enviado por correo.</small>
            </div>
            <input
              class="form-control form-control-lg form-control-solid"
              type="text"
              name="code"
              id="code"
              required
              autocomplete="none"
              value={form().code}
              onInput={(e) => {
                setForm((form) => {
                  form.code = (e.target as HTMLInputElement).value || '';

                  return form;
                });
              }}
              disabled={isLoading()}
            />
          </div>
          <div class="fv-row mb-5">
            <label class="form-label fs-6 fw-bold text-dark">
              Nueva Contraseña:
            </label>

            <input
              class="form-control form-control-lg form-control-solid"
              type="text"
              name="password"
              id="password"
              required
              pattern="(?=.*[a-z])(?=.*[A-Z]).{8,}"
              autocomplete="none"
              value={form().password}
              onInput={(e) => {
                setForm((form) => {
                  form.password = (e.target as HTMLInputElement).value || '';

                  return form;
                });
              }}
              disabled={isLoading()}
            />
          </div>
          <div class="mb-10">
            <small>
              Formato de la contraseña: 8 caracteres, debe contener al menos: 1
              numero, 1 letra mayúscula y 1 letra minúscula.
            </small>
          </div>
          <div class="text-center">
            <button
              type="submit"
              class="btn btn-lg btn-primary w-100 mb-5"
              data-kt-indicator={isLoading() && 'on'}
              disabled={isLoading()}
            >
              <span class="indicator-label">Restablecer</span>
              <span class="indicator-progress">
                Espere...
                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
              </span>
            </button>
          </div>
        </div>
      </Form>
    </>
  );
};
export default ResetPasswordForm;
