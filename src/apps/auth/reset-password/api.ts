/* eslint-disable @typescript-eslint/ban-types */
import {
  AuthenticationDetails,
  CognitoUser,
  CognitoUserPool,
} from 'amazon-cognito-identity-js';
import { API } from '../../../shared/services/app-base/auth-api-base';
import { UserProps } from '../sign-in/types';
import { AuthenticateUserResponse } from './types';

export class ResetPasswordAPI extends API {
  private cognitoUser: CognitoUser;

  public cognitoUserPool: CognitoUserPool;

  constructor() {
    super('user');
    this.initLogin();
  }

  private initLogin() {
    this.cognitoUserPool = new CognitoUserPool(this.poolData);
  }

  public sendVerificationCode(
    username: string
  ): Promise<AuthenticateUserResponse> {
    return new Promise((resolve, reject) => {
      const userData = {
        Username: username,
        Pool: this.cognitoUserPool,
      };
      this.cognitoUser = new CognitoUser(userData);
      this.cognitoUser.forgotPassword({
        onSuccess(data) {
          resolve({
            response: 'SUCCESS',
            data: data,
          });
        },
        onFailure(err) {
          reject(err);
        },
      });
    });
  }

  public resetUserPassword(
    code: string,
    newPassword: string
  ): Promise<AuthenticateUserResponse> {
    return new Promise((resolve, reject) => {
      this.cognitoUser.confirmPassword(code, newPassword, {
        onSuccess(data) {
          resolve({
            response: 'SUCCESS',
            data: data,
          });
        },
        onFailure(err) {
          reject(err);
        },
      });
    });
  }

  public login({
    username,
    password,
  }: {
    username: string;
    password: string;
  }): Promise<AuthenticateUserResponse> {
    const authenticationData = {
      Username: username,
      Password: password,
    };
    const authenticationDetails = new AuthenticationDetails(authenticationData);
    const userData = {
      Username: username,
      Pool: this.cognitoUserPool,
    };
    this.cognitoUser = new CognitoUser(userData);

    return new Promise((resolve, reject) => {
      this.cognitoUser.authenticateUser(authenticationDetails, {
        onSuccess: function (data) {
          resolve({
            response: 'SUCCESS',
            data: data,
          });
        },
        onFailure: function (err) {
          reject(err);
        },
        newPasswordRequired: function (userAttributes) {
          resolve({
            response: 'NEW_PASSWORD',
            data: userAttributes,
          });
        },
      });
    });
  }

  public verificateCode(code: number): Promise<AuthenticateUserResponse> {
    return new Promise((resolve, reject) => {
      this.cognitoUser.sendMFACode(code.toString(), {
        onSuccess: function (result) {
          resolve({
            response: 'SUCCESS',
            data: result,
          });
        },
        onFailure: function (err) {
          reject(err);
        },
      });
    });
  }

  public async getUserInformation(username: string) {
    const response = await this.request({
      method: 'GET',
      url: 'user',
      params: { id: username },
    });

    return response.data as UserProps;
  }
}
