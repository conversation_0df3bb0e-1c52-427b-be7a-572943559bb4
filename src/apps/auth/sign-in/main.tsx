import { ICognitoError } from 'shared/services/user/types';
import { render } from 'solid-js/web';
import { Page } from '../../../shared/services/app-base/page-base';
import { SignInAPI } from './api';
import SingInContainer from './components/sing-in-container';
import { SignInServices } from './services';
import { BodyAnimationService } from '../../../shared/services/utils/body-animation-service';

window.addEventListener('load', async () => {
  new SignIn();
});
export class SignIn extends Page {
  public api;

  public services;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public userAttributes: any;

  constructor() {
    super();
    this.api = new SignInAPI();
    this.services = new SignInServices();
    this.renderComponents();
    BodyAnimationService.changeOpacity();
  }

  private renderComponents() {
    if ('customElements' in window) {
      const userProfileContainer =
        this.services.getHTMLElement('sign-in-container');
      render(
        () => (
          <>
            <SingInContainer context={this} />
          </>
        ),
        userProfileContainer
      );
    }
  }

  public async login({
    username,
    password,
  }: {
    username: string;
    password: string;
  }) {
    try {
      if (!username || !password) {
        this.showErrorAlert({ message: 'Información invalida' });

        return;
      }
      const result = await this.api.login({
        username,
        password,
      });
      if (result.response === 'SUCCESS') {
        await this.getUserInformation(result.data.accessToken.payload.username);
      }
      if (result.response === 'NEW_PASSWORD') {
        this.userAttributes = result.data;
        delete this.userAttributes.email_verified;
      }

      return result;
    } catch (error) {
      localStorage.clear();
      this.logger(error);
      this.showErrorAlert({
        message: this.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });

      return null;
    }
  }

  public async codeVerification(code: string) {
    try {
      if (!code) {
        this.showErrorAlert({ message: 'Información invalida' });

        return;
      }
      const result = await this.api.verificateCode(code);
      await this.getUserInformation(result.data.accessToken.payload.username);
    } catch (error) {
      localStorage.clear();
      this.logger(error);
      this.showErrorAlert({
        message: this.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });
    }
  }

  public async setNewPassword({ password }: { password: string }) {
    try {
      const result = await this.api.setNewPassword(password, {});
      if (result.response === 'SUCCESS') {
        this.getUserInformation(result.data.accessToken.payload.username);
      }

      return result;
    } catch (error) {
      this.logger(error);
      this.showErrorAlert({
        message: 'Error al actualizar contraseña',
      });

      return null;
    }
  }

  private async getUserInformation(username: string) {
    try {
      const userInformation = await this.api.getUserInformation(username);
      const isUserDataValid = this.services.manageUserData(userInformation);
      if (!isUserDataValid) {
        this.api.cognitoUserPool.getCurrentUser()?.signOut();
        this.showErrorAlert({
          message: 'Ha ocurrido un error con la información del usuario.',
        });
        location.reload();
      }
      setTimeout(() => {
        this.pageRedirect('../../home/<USER>');
      }, 300);
    } catch (error) {
      localStorage.clear();
      this.logger(error);
      this.showErrorAlert({
        message: 'Error al almacenar la información del usuario',
      });
      this.api.cognitoUserPool.getCurrentUser()?.signOut();
    }
  }
}
