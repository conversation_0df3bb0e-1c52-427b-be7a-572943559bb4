import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { SingInSetNewPasswordProps } from '../types';
import { ICognitoError } from 'shared/services/user/types';

const [password, setNewPassword] = createSignal('');
const SingInSetNewPassword = (props: SingInSetNewPasswordProps) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const result = await props.context.setNewPassword({
        password: password(),
      });
      if (result?.response === 'NEW_PASSWORD') props.setView('NEW_PASSWORD');
      if (result?.response === 'MFA') props.setView('MFA');
    } catch (error) {
      props.context.showErrorAlert({
        message: props.context.services.getContextualizedErrorMessage(
          error as ICognitoError
        ),
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Form onSubmit={onFormSubmit} class="mt-3" id="new-passwod-form">
        <div class="fv-row mb-10">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">
              Nueva Contraseña
            </label>
          </div>
          <input
            id="new-password"
            class="form-control form-control-lg form-control-solid"
            type="password"
            name="password"
            autocomplete="off"
            pattern="(?=.*[a-z])(?=.*[A-Z]).{8,}"
            required
            onInput={(e) => {
              setNewPassword((e.target as HTMLInputElement).value || '');
            }}
          />
        </div>
        <small>
          Formato de la contraseña: 8 caracteres, debe contener al menos: 1
          numero, 1 letra mayúscula y 1 letra minúscula.
        </small>
        <div class="mt-3">
          <button
            type="submit"
            id="new-password-btn"
            class="btn btn-lg btn-primary w-100 mb-5"
            data-kt-indicator={isLoading() && 'on'}
            disabled={isLoading()}
          >
            <span class="indicator-label">Ingresar</span>
            <span class="indicator-progress">
              Espere...
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </button>
        </div>
      </Form>
    </>
  );
};
export default SingInSetNewPassword;
