import { Form } from 'solid-bootstrap';
import { createSignal } from 'solid-js';
import { SingInFormProps } from '../types';

const [isLoading, setIsLoading] = createSignal<boolean>(false);
const [showPassword, setShowPassword] = createSignal<boolean>(false);
const [form, setForm] = createSignal({
  username: '',
  password: '',
});
const SingInForm = (props: SingInFormProps) => {
  const onFormSubmit = async (e: Event) => {
    e.preventDefault();
    setIsLoading(true);
    const result = await props.context.login(form());
    if (result?.response === 'NEW_PASSWORD') props.setView('NEW_PASSWORD');
    if (result?.response === 'MFA') props.setView('MFA');
    setIsLoading(false);
  };
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword());
  };

  return (
    <>
      <Form onSubmit={onFormSubmit} id="login-form">
        <div class="fv-row mb-10">
          <label class="form-label fs-6 fw-bold text-dark">
            Correo Electrónico:
          </label>
          <input
            class="form-control form-control-lg form-control-solid"
            type="text"
            name="username"
            id="username"
            autocomplete="off"
            required
            value={form().username}
            onInput={(e) => {
              setForm((form) => {
                form.username = (e.target as HTMLInputElement).value || '';

                return form;
              });
            }}
            disabled={isLoading()}
          />
        </div>
        <div class="fv-row mb-10">
          <div class="d-flex flex-stack mb-2">
            <label class="form-label fw-bold text-dark fs-6 mb-0">
              Contraseña:
            </label>
            <a
              href="../reset-password/"
              role="button"
              class="link-primary fs-6 fw-bold"
            >
              ¿Olvidó su Contraseña?
            </a>
          </div>
          <div class="position-relative">
            <input
              class="form-control form-control-lg form-control-solid"
              type={showPassword() ? 'text' : 'password'}
              name="password"
              id="password"
              pattern="(?=.*[a-z])(?=.*[A-Z]).{8,}"
              autocomplete="off"
              required
              value={form().password}
              onInput={(e) => {
                setForm((form) => {
                  form.password = (e.target as HTMLInputElement).value || '';

                  return form;
                });
              }}
              disabled={isLoading()}
              style="padding-right: 3rem;"
            />
            <button
              type="button"
              class="btn btn-sm btn-icon position-absolute top-50 end-0 translate-middle-y me-3"
              onClick={togglePasswordVisibility}
              disabled={isLoading()}
              style="border: none; background: transparent; z-index: 5;"
              title={
                showPassword() ? 'Ocultar contraseña' : 'Mostrar contraseña'
              }
            >
              <i
                class={
                  showPassword() ? 'bi bi-eye-slash fs-4' : 'bi bi-eye fs-4'
                }
              ></i>
            </button>
          </div>
        </div>
        <div class="text-center">
          <button
            type="submit"
            class="btn btn-lg btn-primary w-100 mb-5"
            data-kt-indicator={isLoading() && 'on'}
            disabled={isLoading()}
          >
            <span class="indicator-label">Continuar</span>
            <span class="indicator-progress">
              Espere...
              <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
            </span>
          </button>
        </div>
      </Form>
    </>
  );
};
export default SingInForm;
