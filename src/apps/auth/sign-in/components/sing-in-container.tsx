import { createSignal, Match, onMount, Switch } from 'solid-js';
import Be<PERSON>etter<PERSON>ogo from '../../../../assets/media/logos/bebetter.png';
import { SignIn } from '../main';
import { ViewOptions } from '../types';
import SingInForm from './sing-in-form';
import './style.css';
import SingInSetNewPassword from './sing-in-set-new-password';

const [view, setView] = createSignal<ViewOptions>('SIGN_IN');
const [key] = createSignal('USER');
const SingInContainer = (props: { context: SignIn }) => {
  onMount(() => {
    props.context.api.init();
  });

  return (
    <>
      <div
        class="form w-100"
        id="kt_sign_in_form"
        data-kt-redirect-url="../home/"
      >
        <div class="d-flex flex-row-fluid flex-column text-center p-5 p-lg-10 pt-lg-20">
          <a href="#" class="py-1 py-lg-10">
            <img alt="Logo" src={BeBetterLogo} class="h-150px h-lg-200px" />
          </a>
        </div>
        <div class="text-center mb-10"></div>
        <Switch>
          <Match when={view() === 'SIGN_IN'}>
            <SingInForm
              form={key()}
              setView={setView}
              context={props.context}
            />
          </Match>
          <Match when={view() === 'NEW_PASSWORD'}>
            <SingInSetNewPassword setView={setView} context={props.context} />
          </Match>
        </Switch>
      </div>
    </>
  );
};
export default SingInContainer;
