/* eslint-disable @typescript-eslint/ban-types */
import {
  AuthenticationDetails,
  CognitoUser,
  CognitoUserPool,
} from 'amazon-cognito-identity-js';
import { API } from './auth-api';
import { AuthenticateUserResponse, AvailablePools, UserProps } from './types';

export class SignInAPI extends API {
  private cognitoUser: CognitoUser;

  public cognitoUserPool: CognitoUserPool;

  public userPool: AvailablePools;

  constructor() {
    super('user');
  }

  private initLogin() {
    this.cognitoUserPool = new CognitoUserPool(this.poolData);
  }

  public login({
    username,
    password,
  }: {
    username: string;
    password: string;
  }): Promise<AuthenticateUserResponse> {
    this.initLogin();
    const authenticationData = {
      Username: username,
      Password: password,
    };
    const authenticationDetails = new AuthenticationDetails(authenticationData);
    const userData = {
      Username: username,
      Pool: this.cognitoUserPool,
    };
    this.cognitoUser = new CognitoUser(userData);

    return new Promise((resolve, reject) => {
      this.cognitoUser.authenticateUser(authenticationDetails, {
        onSuccess: function (data) {
          resolve({
            response: 'SUCCESS',
            data: data,
          });
        },
        mfaRequired: function (challengeName, challengeParameters) {
          resolve({
            response: 'MFA',
            data: { challengeName, ...challengeParameters },
          });
        },
        onFailure: function (err) {
          reject(err);
        },
        newPasswordRequired: function (userAttributes) {
          resolve({
            response: 'NEW_PASSWORD',
            data: userAttributes,
          });
        },
      });
    });
  }

  public setNewPassword(
    newPassword: string,
    requiredAttributeData: {}
  ): Promise<AuthenticateUserResponse> {
    return new Promise((resolve, reject) => {
      this.cognitoUser.completeNewPasswordChallenge(
        newPassword,
        requiredAttributeData,
        {
          onSuccess: function (result) {
            resolve({
              response: 'SUCCESS',
              data: result,
            });
          },
          mfaRequired: function (challengeName, challengeParameters) {
            resolve({
              response: 'MFA',
              data: { challengeName, ...challengeParameters },
            });
          },
          onFailure: function (err) {
            reject(err);
          },
        }
      );
    });
  }

  public verificateCode(code: string): Promise<AuthenticateUserResponse> {
    return new Promise((resolve, reject) => {
      this.cognitoUser.sendMFACode(code.toString(), {
        onSuccess: function (result) {
          resolve({
            response: 'SUCCESS',
            data: result,
          });
        },
        onFailure: function (err) {
          reject(err);
        },
      });
    });
  }

  public resendVrificateCode(): Promise<void> {
    return new Promise((resolve) => {
      this.cognitoUser.resendConfirmationCode(() => {
        resolve();
      });
    });
  }

  public async getUserInformation(username: string) {
    const response = await this.request({
      method: 'GET',
      url: 'user',
      params: { id: username },
    });

    return response.data as UserProps;
  }
}
