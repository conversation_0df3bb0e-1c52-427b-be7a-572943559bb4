export type Habit = {
  id: string;
  //Diet
  oliveOil: number;
  greenLeafyVegetables: number;
  otherVegetables: number;
  berries: number;
  otherFruit: number;
  meat: number;
  fish: number;
  chicken: number;
  cheese: number;
  butter: number;
  beans: number;
  wholeGrains: number;
  sweetsAndPastries: number;
  nuts: number;
  fastFood: number;
  alcohol: number;
  //PA (in minutes)
  physicalActivity: number;
  //Nicotine
  nicotineExposure: NicotineExposure;
  //Sleep Health (hours per night)
  sleep: number;
  //BMI
  bodyWeight: number; //kilograms
  height: number; // meters
  //Blood Lipids
  cholesterol: number;
  //Blood glucose
  glucose: number; //HbA1c
  //Blood Preasure
  systolic: number;
  diastolic: number;
  //metadata
  createdAt: string;
  updatedAt: string;
};
export type NicotineExposure =
  | 'NEVER_SMOKE'
  | 'FORMER_SMOKER_MORE_THAN_5'
  | 'FORMER_SMOKER_BETWEEEN_1_AND_5'
  | 'FORMER_SMOKER_LESS_THAN_1'
  | 'CURRENT_SMOKER';
export type LifeEssentialDashboard = {
  date: string;
  overallScore: number;
  dietScore: number;
  physicalActivityScore: number;
  nicotineExposureScore: number;
  sleepScore: number;
  bodyWeightScore: number;
  cholesterolScore: number;
  glucoseScore: number;
  bloodPreasureScore: number;
};
