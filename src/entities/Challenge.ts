// eslint-disable-next-line @typescript-eslint/naming-convention
export interface Challenge {
  id: string;
  userId: string;
  name: string;
  description: string;
  reward: string;
  initialValue: number;
  goalType: string;
  goal: number;
  goalUnit: string;
  startDate: string;
  endDate: string;
  isRemainderActive: boolean;
  remainderType?: ChallengeNotificationOptions;
  progressType: ChallengesProgressType;
  participants?: Participant[];
  challengeParticipantType: ChallengesParticipanTypes;
  timeProgress: number;
  challengeProgress: number;
  isEvidenceRequired: boolean;
  updatedAt: string;
  createdAt: string;
}
export type Participant = {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  challengeId: string;
  createdAt: string;
  updatedAt: string;
  participantStatus: string;
};
export type ChallengeNotificationOptions = 'DAILY' | 'WEEKLY' | 'MONTHLY';
export type ChallengesParticipanTypes = 'INDIVIDUAL' | 'GROUP';
export type ChallengesProgressType = 'ACCUMULATIVE' | 'FINAL_RESULT' | 'HABIT';
export type PreConfiguredChallengeTypes =
  | 'WEIGHT'
  | 'GYM'
  | 'WATER_INTAKE'
  | 'STEPS'
  | 'SLEEP'
  | 'CALORIES'
  | 'FAT'
  | 'OTHER';
export type PreConfiguredChallengeTypesName =
  | 'Peso'
  | 'Gimnasio'
  | 'Consumo de agua'
  | 'Pasos'
  | 'Sueño'
  | 'Calorías'
  | 'Grasa corporal'
  | 'Otro';
export type PredefinedChallenges = {
  [key in PreConfiguredChallengeTypes]: PreConfiguredChallengeType;
};
export type GoalType = {
  ADD: string;
  REDUCE: string;
};
export type PreConfiguredChallengeType = {
  PROGRESS_TYPE: {
    ACCUMULATIVE: boolean;
    FINAL_RESULT: boolean;
    HABIT: boolean;
  };
  FINAL_RESULT: GoalType;
  ACCUMULATIVE: GoalType;
  HABIT: GoalType;
  UNIT: { id: string; EXTENDED_NAME: string };
};
