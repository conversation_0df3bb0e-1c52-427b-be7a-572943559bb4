export type Post = {
  id: string;
  userId: string;
  userName: string;
  content: string;
  imagesURL: Array<string>;
  externalLinks: Array<string>;
  contentStatus: PostStatus;
  likes: Array<UserPostLike>;
  createdAt?: string;
  updatedAt?: string;
};
export type UserPostLike = {
  userId: string;
  userName: string;
};
export type UserPostLikeStatus = 'ACTIVE' | 'ARCHIVED';
export type PostStatus = 'ACTIVE' | 'ARCHIVED';
