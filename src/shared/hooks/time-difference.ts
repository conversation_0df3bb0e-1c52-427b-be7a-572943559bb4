export function useCalculateTimeDifference(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  const timeDifference = Math.abs(endDate.getTime() - startDate.getTime());
  const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
  const weeksDifference = Math.floor(daysDifference / 7);
  if (daysDifference < 8) {
    return `${daysDifference} dia${daysDifference !== 1 ? 's' : ''}`;
  } else {
    return `${weeksDifference} semana${weeksDifference !== 1 ? 's' : ''}`;
  }
}
export function useCalculateDaysDifference(isoDateString: string): number {
  const currentDate = new Date();
  const isoDate = new Date(isoDateString);
  const timeDifference = currentDate.getTime() - isoDate.getTime();
  const daysDifference = Math.floor(timeDifference / (24 * 60 * 60 * 1000));

  return daysDifference;
}
