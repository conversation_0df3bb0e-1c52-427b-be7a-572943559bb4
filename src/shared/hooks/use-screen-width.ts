import { createEffect, createSignal } from 'solid-js';

export function useScreenWidth() {
  const [screenWidth, setScreenWidth] = createSignal(window.innerWidth);
  // Update screen width when the window is resized
  createEffect(() => {
    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  });

  return screenWidth;
}
