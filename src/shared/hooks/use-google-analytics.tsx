/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSignal, onMount } from 'solid-js';
import ReactGA from 'react-ga4';

export function useGoogleAnalytics(props: {
  trackingId: string;
  userId: string;
}) {
  const [initialized, setInitialized] = createSignal(false);
  const { trackingId, userId } = props;
  onMount(() => {
    if (!initialized()) {
      ReactGA.initialize(trackingId, {
        gaOptions: { userId },
      });
      setInitialized(true);
    }
  });
  const trackPageView = (path: string) => {
    ReactGA.send({
      hitType: 'pageview',
      page: path,
      title: path.replace('/', ''),
    });
  };
  const trackEvent = (
    category: string,
    action: string,
    label?: string,
    value?: number
  ) => {
    ReactGA.event({ category, action, label, value });
  };

  return {
    trackPageView,
    trackEvent,
  };
}
