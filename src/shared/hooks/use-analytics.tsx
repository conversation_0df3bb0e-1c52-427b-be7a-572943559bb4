import { onMount } from 'solid-js';
import { useRum } from './use-rum';
import { useGoogleAnalytics } from './use-google-analytics';
import { getCurrentUserData } from '../services/user/user-session-management';

export const useAnalytics = () => {
  const userOrNull = getCurrentUserData();
  const isDevStage = (import.meta.env.VITE_ENV || 'dev') === 'dev';
  const currentPage = getPage();
  const { trackPageView } = useGoogleAnalytics({
    trackingId: 'G-RT0H7HRTB3',
    userId: userOrNull?.id || '',
  });
  onMount(() => {
    if (isDevStage) return;
    setTimeout(() => {
      useRum({ currentPage });
      trackPageView(currentPage);
    }, 1500);
  });
};
const getPage = () => {
  try {
    return window.location.pathname.replace('/apps/', '');
  } catch (error) {
    return window.location.pathname;
  }
};
