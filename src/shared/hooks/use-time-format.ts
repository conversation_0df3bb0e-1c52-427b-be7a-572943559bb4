export const useTimeFormat = (isoDateString: string): string => {
  const isoDate = new Date(isoDateString);
  const day = isoDate.getDate().toString().padStart(2, '0');
  const month = (isoDate.getMonth() + 1).toString().padStart(2, '0');
  const year = isoDate.getFullYear();
  const hours = isoDate.getHours() % 12 || 12;
  const minutes = isoDate.getMinutes().toString().padStart(2, '0');
  const ampm = isoDate.getHours() >= 12 ? 'PM' : 'AM';
  const formattedString = `${day}/${month}/${year} ${hours}:${minutes} ${ampm}`;

  return formattedString;
};
