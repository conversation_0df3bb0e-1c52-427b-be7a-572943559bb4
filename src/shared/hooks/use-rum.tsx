import { AwsRum, AwsRumConfig } from 'aws-rum-web';
import { onMount } from 'solid-js';

export const useRum = (props: { currentPage: string }) => {
  const { currentPage } = props;
  const trackUser = () => {
    try {
      const config: AwsRumConfig = {
        sessionSampleRate: 1,
        guestRoleArn:
          'arn:aws:iam::644907879075:role/RUM-Monitor-us-east-1-644907879075-4746418490171-Unauth',
        identityPoolId: 'us-east-1:1aff51dd-15d4-4db8-bed9-4b0b31e4a681',
        endpoint: 'https://dataplane.rum.us-east-1.amazonaws.com',
        telemetries: ['performance', 'errors', 'http'],
        allowCookies: true,
        enableXRay: false,
      };
      const APPLICATION_ID = '1d9acf30-ff2b-4492-beef-c272ba441866';
      const APPLICATION_VERSION = '1.0.0';
      const APPLICATION_REGION = 'us-east-1';
      const awsRum: AwsRum = new AwsRum(
        APPLICATION_ID,
        APPLICATION_VERSION,
        APPLICATION_REGION,
        config
      );
      awsRum.recordEvent('pageView', { path: currentPage });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('RUM Error:', error);
    }
  };
  onMount(() => {
    trackUser();
  });
};
