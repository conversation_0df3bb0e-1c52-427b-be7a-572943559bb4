import { ApexOptions } from 'apexcharts';
import { UserHealthData } from 'entities/UserMedicalDataReport';
import { Accessor } from 'solid-js';

export const useLabReportChart = ({
  heathData,
  screenWidth,
}: {
  heathData: UserHealthData;
  screenWidth: Accessor<number>;
}) => {
  const CHART_BASE_OPTIONS: ApexOptions = {
    chart: {},
    dataLabels: { enabled: true, style: { colors: ['#7D0096'] }, offsetY: -10 },
    stroke: { show: true, curve: 'smooth', colors: ['#BC36F0'] },
    markers: {
      size: 10,
      colors: ['#7D0096'],
    },
    tooltip: { enabled: false },
    noData: { text: 'No se encontró información' },
  };
  const renderChartSeries = (heathData: UserHealthData) => {
    const values = heathData.values
      ?.map((e) => e.examnResult)
      .map((e) => (isValidNumber(parseFloat(e)) ? parseFloat(e) : 0))
      .reverse();

    return [{ name: 'Total', data: values.reverse() }];
  };
  const renderChartOptions = (
    heathData: UserHealthData,
    screenWidth: Accessor<number>
  ): ApexOptions => {
    const categories = heathData.values.map((data) => data.date.split(' ')[0]);

    return {
      ...CHART_BASE_OPTIONS,
      xaxis: { categories: categories, labels: { show: true } },
      chart: {
        height: screenWidth() > 900 ? 500 : 400,
        toolbar: {
          show: false,
        },
      },
    };
  };
  const isValidNumber = (value: unknown): boolean => {
    return typeof value === 'number' && !Number.isNaN(value);
  };
  const series = renderChartSeries(heathData);
  const options = renderChartOptions(heathData, screenWidth);

  return { options, series };
};
