/* eslint-disable @typescript-eslint/naming-convention */
/** @jsxImportSource solid-js */
import { createSignal, For } from 'solid-js';
import { SidebarServiceResponse } from '../types';
import rocheWhiteLogo from '../../../../assets/media/logos/roche-logo-blue.png';
import Be<PERSON><PERSON>r<PERSON>ogo from '../../../../assets/media/logos/bebetter.png';
import '../style.css';
import heartIcon from './../../../../assets/media/icons/Heart.svg';
import goalIcon from './../../../../assets/media/icons/Target, Success.svg';
import chartIcon from './../../../../assets/media/icons/File bar chart.svg';
import settingsIcon from './../../../../assets/media/icons/Settings.svg';
import { useScreenWidth } from '../../../hooks/use-screen-width';
import { getCurrentUserData } from '../../../services/user/user-session-management';
import sidebarBackground from '../../../../assets/media/logos/degraded-background.png';

const sidebarStyle = {
  'background-image': `url(${sidebarBackground})`,
  'background-position': 'top',
  'background-size': 'cover',
};
const SidebarComponent = () => {
  const { userType } = getCurrentUserData();
  const SIDEBAR_CONTENT: SidebarServiceResponse = {
    ADMIN: {
      apps: [
        {
          id: 1,
          title: 'Personal',
          content: [
            {
              appId: 1,
              title: 'Tu Salud',
              icon: heartIcon,
              subcontent: [
                {
                  title: 'Hábitos',
                  href: '../health/?view=HEALTH',
                },
                {
                  title: 'Laboratorio',
                  href: '../health/?view=LAB_EXAMS',
                },
                {
                  title: 'Expediente Médico',
                  href: '../medical-record/',
                },
              ],
            },
            {
              appId: 1,
              title: 'Retos y Metas',
              icon: goalIcon,
              subcontent: [
                {
                  title: 'Activos',
                  href: '../challenges/?view=HOME',
                },
                {
                  title: 'Completados',
                  href: '../challenges/?view=COMPLETED',
                },
              ],
            },
          ],
        },
        {
          id: 1,
          title: 'Reportes',
          content: [
            {
              appId: 1,
              title: 'Reportes',
              icon: chartIcon,
              subcontent: [
                {
                  title: 'Reporte de Progreso',
                  href: '../reports/?report=PROGRESS',
                },
                {
                  title: 'Reporte de Estudios',
                  href: '../reports/?report=STUDIES',
                },
                {
                  title: 'Reporte de Exámenes',
                  href: '../reports/?report=EXAM',
                },
              ],
            },
          ],
        },
        {
          id: 1,
          title: 'Administrador',
          content: [
            {
              appId: 1,
              title: 'Administrador',
              icon: settingsIcon,
              subcontent: [
                {
                  title: 'Usuarios',
                  href: '../admin/',
                },
                {
                  title: 'Contenido Médico',
                  href: '../content/',
                },
              ],
            },
          ],
        },
      ],
    },
    DESITION_MAKER: {
      apps: [
        {
          id: 1,
          title: 'Personal',
          content: [
            {
              appId: 1,
              title: 'Tu Salud',
              icon: heartIcon,
              subcontent: [
                {
                  title: 'Hábitos',
                  href: '../health/?view=HEALTH',
                },
                {
                  title: 'Laboratorio',
                  href: '../health/?view=LAB_EXAMS',
                },
                {
                  title: 'Expediente Médico',
                  href: '../medical-record/',
                },
              ],
            },
            {
              appId: 1,
              title: 'Retos y Metas',
              icon: goalIcon,
              subcontent: [
                {
                  title: 'Activos',
                  href: '../challenges/?view=HOME',
                },
                {
                  title: 'Completados',
                  href: '../challenges/?view=COMPLETED',
                },
              ],
            },
          ],
        },
        {
          id: 1,
          title: 'Reportes',
          content: [
            {
              appId: 1,
              title: 'Reportes',
              icon: chartIcon,
              subcontent: [
                {
                  title: 'Reporte de Progreso',
                  href: '../reports/?report=PROGRESS',
                },
                {
                  title: 'Reporte de Estudios',
                  href: '../reports/?report=STUDIES',
                },
                {
                  title: 'Reporte de Examenes',
                  href: '../reports/?report=EXAMN',
                },
              ],
            },
          ],
        },
      ],
    },
    CONTENT_CREATOR: {
      apps: [
        {
          id: 1,
          title: 'Personal',
          content: [
            {
              appId: 1,
              title: 'Tu Salud',
              icon: heartIcon,
              subcontent: [
                {
                  title: 'Hábitos',
                  href: '../health/?view=HEALTH',
                },
                {
                  title: 'Laboratorio',
                  href: '../health/?view=LAB_EXAMS',
                },
                {
                  title: 'Expediente Médico',
                  href: '../medical-record/',
                },
              ],
            },
            {
              appId: 1,
              title: 'Retos y Metas',
              icon: goalIcon,
              subcontent: [
                {
                  title: 'Activos',
                  href: '../challenges/?view=HOME',
                },
                {
                  title: 'Completados',
                  href: '../challenges/?view=COMPLETED',
                },
              ],
            },
          ],
        },
        {
          id: 1,
          title: 'Administrador',
          content: [
            {
              appId: 1,
              title: 'Administrador',
              icon: settingsIcon,
              subcontent: [
                {
                  title: 'Contenido Médico',
                  href: '../content/',
                },
              ],
            },
          ],
        },
      ],
    },
    USER: {
      apps: [
        {
          id: 1,
          title: 'Personal',
          content: [
            {
              appId: 1,
              title: 'Tu Salud',
              icon: heartIcon,
              subcontent: [
                {
                  title: 'Hábitos',
                  href: '../health/?view=HEALTH',
                },
                {
                  title: 'Laboratorio',
                  href: '../health/?view=LAB_EXAMS',
                },
                {
                  title: 'Expediente Médico',
                  href: '../medical-record/',
                },
              ],
            },
            {
              appId: 1,
              title: 'Retos y Metas',
              icon: goalIcon,
              subcontent: [
                {
                  title: 'Activos',
                  href: '../challenges/?view=HOME',
                },
                {
                  title: 'Completados',
                  href: '../challenges/?view=COMPLETED',
                },
              ],
            },
          ],
        },
      ],
    },
  };
  const [sidebarContent] = createSignal(SIDEBAR_CONTENT[userType].apps);
  const screenWidth = useScreenWidth();

  return (
    <div
      id="kt_aside"
      class="aside aside-default aside-hoverable"
      data-kt-drawer="true"
      data-kt-drawer-name="aside"
      data-kt-drawer-activate="{default: true, lg: false}"
      data-kt-drawer-overlay="true"
      data-kt-drawer-width="{default:'200px', '300px': '250px'}"
      data-kt-drawer-direction="start"
      data-kt-drawer-toggle="#kt_aside_toggle"
      style={sidebarStyle}
    >
      <div
        class="aside-logo p-0 mx-5 my-5"
        style={{ 'background-color': 'white', 'border-radius': '1.5rem' }}
      >
        <div class="flex-column-auto pt-3 pb-0 m-auto" id="kt_aside_logo">
          <a href="/apps/home/">
            <img
              alt="Logo"
              src={BeBetterLogo}
              class="max-h-100px logo-default theme-light-show mx-auto"
              height={100}
              style={' height: 100px !important'}
            />

            <img
              alt="Logo"
              src={BeBetterLogo}
              class="test max-h-100px logo-default theme-dark-show mx-auto"
              height={100}
              style={' height: 100px !important'}
            />
            <img
              alt="Logo"
              src={BeBetterLogo}
              class="logo-minimize m-0 p-0"
              style={'max-height:30px'}
              height={40}
            />
          </a>
        </div>
      </div>

      <div class="aside-menu flex-column-fluid px-4 px-lg-7">
        <div
          class="menu menu-sub-indention menu-column menu-rounded menu-title-gray-600 menu-icon-gray-400 menu-active-bg menu-state-primary menu-arrow-gray-500 fw-semibold fs-6 my-5 mt-lg-2 mb-lg-0"
          id="kt_aside_menu"
          data-kt-menu="true"
        >
          <div
            class="hover-scroll-y me-lg-n5 pe-lg-5 h-100"
            id="kt_aside_menu_wrapper"
            data-kt-scroll="true"
            data-kt-scroll-activate="{default: false, lg: true}"
            data-kt-scroll-height="auto"
            data-kt-scroll-wrappers="#kt_aside_menu"
            data-kt-scroll-offset="20px"
            data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer"
          >
            <For each={sidebarContent()}>
              {(section) => (
                <>
                  <div class="menu-item pt-5">
                    <div class="menu-content">
                      <span class="fw-bold text-white text-uppercase fs-7">
                        {section.title}
                      </span>
                    </div>
                  </div>
                  <For each={section.content}>
                    {(content) => (
                      <>
                        <div
                          data-kt-menu-trigger="click"
                          class="menu-item menu-accordion"
                        >
                          <span class="menu-link">
                            <span class="menu-icon">
                              <img src={content.icon} alt="Icon" height={25} />
                            </span>
                            <span class="menu-title">{content.title}</span>
                            <span class="menu-arrow"></span>
                          </span>
                          <div class="menu-sub menu-sub-accordion">
                            <For each={content.subcontent}>
                              {(subcontent) => (
                                <div class="menu-item">
                                  <a class="menu-link" href={subcontent.href}>
                                    <span class="menu-bullet">
                                      <span class="bullet bullet-dot"></span>
                                    </span>
                                    <span class="menu-title">
                                      {subcontent.title}
                                    </span>
                                  </a>
                                </div>
                              )}
                            </For>
                          </div>
                        </div>
                      </>
                    )}
                  </For>
                </>
              )}
            </For>
          </div>
        </div>
      </div>
      <div class="aside-footer flex-column-auto pb-5" id="kt_aside_footer">
        <div class="w-100 text-center px-5 pt-10 pb-0 mb-0 m-auto">
          {screenWidth() > 900 && (
            <>
              <img
                alt="Logo"
                src={rocheWhiteLogo}
                class="logo-minimize m-0 p-0"
                style={'max-height:50px; height: revert-layer'}
                height={40}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export default SidebarComponent;
