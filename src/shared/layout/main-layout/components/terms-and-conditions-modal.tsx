import { createSignal, createEffect, createMemo, Show } from 'solid-js';
import { <PERSON><PERSON>, But<PERSON> } from 'solid-bootstrap';
import { useCurrentUser } from './global-user-context';
import { useTerms } from '../../../../shared/infra/hooks/use-terms';
import { LoadingAlert } from '../../../../shared/components/alert/loading-alert';
import { ErrorAlert } from '../../../../shared/components/alert/error-alert';
import { useUserTermsAcceptance } from '../../../../shared/infra/hooks/use-user-terms-acceptance';

export const TermsAndConditionsModal = () => {
  const context = useCurrentUser();
  if (!context) return null;
  const [show, setShow] = createSignal(false);
  const [isButtonReady, setIsButtonReady] = createSignal(false);
  const { isLoading: isTermsLoading, error: termsError, terms } = useTerms();
  const {
    isLoading: isUserTermsAcceptanceLoading,
    error: userTermsAcceptanceError,
    acceptTerms,
  } = useUserTermsAcceptance();
  const isTermsAccepted = createMemo(() =>
    context.user.hasAcceptedTerms === undefined
      ? false
      : context.user.hasAcceptedTerms
  );
  const handleAccept = async () => {
    if (!terms) return;
    const user = await acceptTerms({
      userId: context.user.id,
      version: terms.version,
    });
    if (!user) return;
    user &&
      user.hasAcceptedTerms &&
      context.setUser({ hasAcceptedTerms: true });
    setShow(false);
  };
  createEffect(() => {
    if (!isTermsAccepted()) {
      setShow(true);
      setTimeout(() => setIsButtonReady(true), 5000);
    }
  });
  const isModalLoading = () =>
    isTermsLoading() || isUserTermsAcceptanceLoading();
  const hasModalError = () => termsError() || userTermsAcceptanceError();

  return (
    <Modal size="lg" centered show={show()} backdrop="static" keyboard={false}>
      <Modal.Header>
        <Modal.Title>Términos & Condiciones</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <LoadingAlert
          class="w-100 my-5 py-5"
          loadingFlags={[isTermsLoading, isUserTermsAcceptanceLoading]}
          showLoadingAnimation
        />
        <ErrorAlert error={[termsError, userTermsAcceptanceError]} />
        <Show when={!isModalLoading() && !hasModalError()}>
          <div
            style={{
              // eslint-disable-next-line @typescript-eslint/naming-convention
              'max-height': '60vh',
              // eslint-disable-next-line @typescript-eslint/naming-convention
              'overflow-y': 'auto',
            }}
            innerHTML={terms.content}
          ></div>
        </Show>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          onClick={handleAccept}
          disabled={
            isModalLoading() || !isButtonReady() || hasModalError() !== null
          }
        >
          Acepto
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
