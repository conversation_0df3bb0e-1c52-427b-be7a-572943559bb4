import {
  isUserLoggedIn,
  logoutService,
} from '../../../services/user/user-session-management';
import {
  clientBasicInformation,
  projectBasicInformation,
} from '../../../../config';
import icon from '../../../../assets/media/icons/icon.png';
import { useAnalytics } from '../../../hooks/use-analytics';
import { onMount } from 'solid-js';

const HeadComponent = () => {
  const isCurrentUserLoggedIn = isUserLoggedIn();
  if (!isCurrentUserLoggedIn) logoutService();
  onMount(() => {
    useAnalytics();
  });

  return (
    <>
      <base href="" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link
        rel="stylesheet"
        type="text/css"
        href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"
      />
      <title>{projectBasicInformation.title}</title>
      <meta name="description" content={projectBasicInformation.description} />
      <meta property="og:locale" content="en_US" />
      <meta property="og:type" content="system" />
      <meta property="og:title" content={clientBasicInformation.client} />
      <meta property="og:url" content={clientBasicInformation.clientURL} />
      <meta property="og:site_name" content={clientBasicInformation.client} />
      <link rel="shortcut icon" href={icon} />
    </>
  );
};
export default HeadComponent;
