import { For, Match, Switch, createSignal } from 'solid-js';
import { Offcanvas } from 'solid-bootstrap';
import Animation from '../../../../components/lottie-player/animation';
import NotificationElement from '../../../../components/notification/notification-element';
import { useNotification } from '../../../../infra/hooks/user-notification';

const NotificationCenter = () => {
  const [show, setShow] = createSignal(false);
  const handleOpen = () => setShow(true);
  const handleClose = () => setShow(false);
  const {
    isLoading,
    error,
    notificationList,
    markNotificationAsRead,
    deleteNotification,
    deleteAllNotifications,
  } = useNotification({ refresh: show });

  return (
    <>
      <div class="d-flex align-items-center ms-1 ms-lg-2">
        <button
          id="kt_drawer_example_basic_button"
          class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px"
          onClick={handleOpen}
        >
          {notificationList.filter(
            (notification) => notification.isRead === false
          ).length > 0 ? (
            <span class="position-relative">
              <i class="bi bi-bell-fill"></i>
              <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger px-3">
                {
                  notificationList.filter(
                    (notification) => notification.isRead === false
                  ).length
                }
              </span>
            </span>
          ) : (
            <i class="bi bi-bell"></i>
          )}
        </button>
      </div>
      <Offcanvas show={show()} onHide={handleClose} placement="end">
        <Offcanvas.Header closeButton>
          <div class="d-flex justify-content-between align-items-center w-100">
            <Offcanvas.Title> Centro de Notificaciones</Offcanvas.Title>
            {notificationList.length > 0 && !isLoading() && (
              <button
                class="btn btn-sm btn-outline-white"
                onClick={deleteAllNotifications}
                title="Eliminar todas las notificaciones"
              >
                <i class="bi bi-trash"></i> Eliminar todas
              </button>
            )}
          </div>
        </Offcanvas.Header>
        <Offcanvas.Body class="pt-1">
          <div class="card-body px-0 pt-0">
            <Switch>
              <Match when={isLoading() === true}>
                <div>
                  <Animation height="100px" width="100px" />
                  <p class="text-center notification-content mt-0">
                    Cargando....
                  </p>
                </div>
              </Match>
              <Match when={error()}>
                <div>
                  <p class="text-center notification-content mt-0">{error()}</p>
                </div>
              </Match>
              <Match when={notificationList.length == 0}>
                <div>
                  <p class="text-center notification-content mt-5">
                    No existen notificaciones
                  </p>
                </div>
              </Match>
              <Match when={isLoading() === false && !error()}>
                <For each={notificationList}>
                  {(notification, i) => (
                    <NotificationElement
                      {...{
                        notification,
                        i: i(),
                        markAsRead: markNotificationAsRead,
                        deleteNotification,
                      }}
                    />
                  )}
                </For>
              </Match>
            </Switch>
          </div>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};
export default NotificationCenter;
