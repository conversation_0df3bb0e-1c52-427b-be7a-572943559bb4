import { User } from 'entities/User';
import { getCurrentUserData } from '../../../services/user/user-session-management';
import { createContext, createEffect, useContext } from 'solid-js';
import { SetStoreFunction, createStore } from 'solid-js/store';

export interface IGlobalUserContextModel {
  user: User;
  setUser: SetStoreFunction<User>;
}
const GlobalUserContext = createContext<IGlobalUserContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function GlobalUserProvider(props: any) {
  const userData = getCurrentUserData();
  const [user, setUser] = createStore<User>(userData);
  const value: IGlobalUserContextModel = {
    user,
    setUser,
  };
  createEffect(() => {
    user && localStorage.setItem('user-local-data', JSON.stringify(user));
  });

  return (
    <GlobalUserContext.Provider value={value}>
      {props.children}
    </GlobalUserContext.Provider>
  );
}
export function useCurrentUser(): IGlobalUserContextModel | undefined {
  return useContext(GlobalUserContext);
}
