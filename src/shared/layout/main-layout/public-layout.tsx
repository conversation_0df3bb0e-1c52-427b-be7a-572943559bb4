import { render } from 'solid-js/web';
import * as popper from '@popperjs/core';
import FooterComponent from './components/footer-component';
import HeadComponent from './components/head-component';
import HeaderComponent from './components/header-component';
import LoaderComponent from './components/loader-component';
import SidebarComponent from './components/sidebar-component';
import { GlobalUserProvider } from './components/global-user-context';
import { GlobalThis } from './types';
import { BodyAnimationService } from '../../services/utils/body-animation-service';
import '../../../assets/css/body-animation.css';
import '../../../assets/css/style.bundle.css';
import '../../../assets/css/bootstrap-icons.css';

const HEADER_CUSTOM_ELEMENT = 'custom-header';
const SIDEBAR_CUSTOM_ELEMENT = 'custom-sidebar';
const FOOTER_CUSTOM_ELEMENT = 'custom-footer';
const LOADER_CUSTOM_ELEMENT = 'custom-loader';
export class MainPublicLayout {
  constructor() {
    (window.globalThis as GlobalThis).Popper = popper;
    this.renderComponents();
  }

  renderComponents() {
    if ('customElements' in window) {
      this.renderHead();
      this.renderHeader();
      this.renderFooter();
      this.renderLoader();
      this.renderSidebar();
      BodyAnimationService.changeOpacity();
    }
  }

  private renderHead() {
    const headContainer = document.getElementsByTagName('head')[0];
    headContainer && render(() => <HeadComponent />, headContainer);
  }

  private renderHeader() {
    const headerContainer = document.getElementsByTagName(
      HEADER_CUSTOM_ELEMENT
    )[0];
    headerContainer &&
      render(
        () => (
          <GlobalUserProvider>
            <HeaderComponent />
          </GlobalUserProvider>
        ),
        headerContainer
      );
  }

  private renderSidebar() {
    const sidebarContainer = document.getElementsByTagName(
      SIDEBAR_CUSTOM_ELEMENT
    )[0];
    sidebarContainer && render(() => <SidebarComponent />, sidebarContainer);
  }

  private renderFooter() {
    const footerContainer = document.getElementsByTagName(
      FOOTER_CUSTOM_ELEMENT
    )[0];
    footerContainer && render(() => <FooterComponent />, footerContainer);
  }

  private renderLoader() {
    const loaderContainer = document.getElementsByTagName(
      LOADER_CUSTOM_ELEMENT
    )[0];
    loaderContainer && render(() => <LoaderComponent />, loaderContainer);
  }
}
new MainPublicLayout();
