import {
  isUserLoggedIn,
  logoutService,
} from '../../services/user/user-session-management';
import { render } from 'solid-js/web';
import FooterComponent from './components/footer-component';
import HeadComponent from './components/head-component';
import HeaderComponent from './components/header-component';
import LoaderComponent from './components/loader-component';
import SidebarComponent from './components/sidebar-component';
import { GlobalUserProvider } from './components/global-user-context';
import '../../../assets/css/body-animation.css';
import { BodyAnimationService } from '../../services/utils/body-animation-service';
import '../../../assets/css/style.bundle.css';
import '../../../assets/css/bootstrap-icons.css';
import * as popper from '@popperjs/core';
import { GlobalThis } from './types';
import { TermsAndConditionsModal } from './components/terms-and-conditions-modal';

const HEADER_CUSTOM_ELEMENT = 'custom-header';
const SIDEBAR_CUSTOM_ELEMENT = 'custom-sidebar';
const FOOTER_CUSTOM_ELEMENT = 'custom-footer';
const LOADER_CUSTOM_ELEMENT = 'custom-loader';
export class MainLayout {
  constructor() {
    (window.globalThis as GlobalThis).Popper = popper;
    this.init();
    this.renderComponents();
  }

  renderComponents() {
    if ('customElements' in window) {
      this.renderHead();
      this.renderHeader();
      this.renderFooter();
      this.renderLoader();
      this.renderSidebar();
      BodyAnimationService.changeOpacity();
    }
  }

  private async init() {
    const isUserActive = await isUserLoggedIn();
    if (!isUserActive) logoutService();
  }

  private renderHead() {
    const headContainer = document.getElementsByTagName('head')[0];
    headContainer && render(() => <HeadComponent />, headContainer);
  }

  private renderHeader() {
    const headerContainer = document.getElementsByTagName(
      HEADER_CUSTOM_ELEMENT
    )[0];
    headerContainer &&
      render(
        () => (
          <GlobalUserProvider>
            <TermsAndConditionsModal />
            <HeaderComponent />
          </GlobalUserProvider>
        ),
        headerContainer
      );
  }

  private renderSidebar() {
    const sidebarContainer = document.getElementsByTagName(
      SIDEBAR_CUSTOM_ELEMENT
    )[0];
    sidebarContainer && render(() => <SidebarComponent />, sidebarContainer);
  }

  private renderFooter() {
    const footerContainer = document.getElementsByTagName(
      FOOTER_CUSTOM_ELEMENT
    )[0];
    footerContainer && render(() => <FooterComponent />, footerContainer);
  }

  private renderLoader() {
    const loaderContainer = document.getElementsByTagName(
      LOADER_CUSTOM_ELEMENT
    )[0];
    loaderContainer && render(() => <LoaderComponent />, loaderContainer);
  }
}
new MainLayout();
