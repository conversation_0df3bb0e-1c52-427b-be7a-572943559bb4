import { clientBasicInformation, projectBasicInformation } from '../../../config';
import icon from '../../../assets/media/icons/icon.png';

export class PageHead {
  public async init() {
    this.setHeadContent();
  }

  private setHeadContent() {
    document.getElementsByTagName('head')[0].innerHTML += `
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="shortcut icon" href="${icon}" />
          <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
          <title>Sign In | ${projectBasicInformation.title}</title>
          <meta name="description" content="${projectBasicInformation.description}" />
          <meta property="og:locale" content="en_US" />
          <meta property="og:type" content="system" />
          <meta property="og:title" content="${clientBasicInformation.client}" />
          <meta property="og:url" content="${clientBasicInformation.clientURL}" />
          <meta property="og:site_name" content="${clientBasicInformation.client}" />
        `;
  }
}
new PageHead();
