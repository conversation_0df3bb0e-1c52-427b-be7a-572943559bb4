import { isUserLoggedIn } from '../../services/user/user-session-management';
import { PageFooter } from './page-footer';
import { PageHead } from './page-header';
import { PageLoader } from './page-loader';
import '../../../assets/css/style.bundle.css';
import '../../../assets/css/bootstrap-icons.css';
import * as popper from '@popperjs/core';
import { GlobalThis } from './types';

const FOOTER_CUSTOM_ELEMENT = 'custom-footer';
const LOADER_CUSTOM_ELEMENT = 'custom-loader';
export class MainLayout {
  private pageHead;

  constructor() {
    (window.globalThis as GlobalThis).Popper = popper;
    this.pageHead = new PageHead();
    this.renderComponents();
    this.init();
  }

  private async init() {
    const isUserActive = await isUserLoggedIn();
    if (isUserActive) {
      window.location.href = '../../home/<USER>';
    }
  }

  renderComponents() {
    this.pageHead.init();
    if ('customElements' in window) {
      this.renderFooter();
      this.renderLoader();
    }
  }

  private renderFooter() {
    customElements.define(FOOTER_CUSTOM_ELEMENT, PageFooter);
  }

  private renderLoader() {
    customElements.define(LOADER_CUSTOM_ELEMENT, PageLoader);
  }
}
new MainLayout();
