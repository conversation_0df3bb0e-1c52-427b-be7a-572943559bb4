import { ChallengesParticipanTypes } from 'entities/Challenge';
import {
  PreConfiguredChallenge,
  GoalUnit,
  ChallengeListElement,
} from '../../apps/challenges/types';
import { PredefinedChallenges } from 'entities/Challenge';

export const PREDEFINED_CHALLENGES: PredefinedChallenges = {
  STEPS: {
    PROGRESS_TYPE: { ACCUMULATIVE: false, FINAL_RESULT: false, HABIT: true },
    FINAL_RESULT: { ADD: 'Llegar a', REDUCE: '' },
    ACCUMULATIVE: { ADD: 'Acumular', REDUCE: '' },
    HABIT: { ADD: 'Meta diaria', REDUCE: '' },
    UNIT: { id: 'STEPS', EXTENDED_NAME: 'Pasos' },
  },
  GYM: {
    PROGRESS_TYPE: { ACCUMULATIVE: true, FINAL_RESULT: false, HABIT: false },
    FINAL_RESULT: { ADD: 'Llegar a', REDUCE: '' },
    ACCUMULATIVE: { ADD: 'Acumular', REDUCE: '' },
    HABIT: { ADD: '', REDUCE: '' },
    UNIT: { id: 'DAYS', EXTENDED_NAME: 'Días' },
  },
  WATER_INTAKE: {
    PROGRESS_TYPE: { ACCUMULATIVE: false, FINAL_RESULT: false, HABIT: true },
    FINAL_RESULT: { ADD: 'Llegar a', REDUCE: '' },
    ACCUMULATIVE: { ADD: 'Acumular', REDUCE: '' },
    HABIT: { ADD: 'Meta diaria', REDUCE: '' },
    UNIT: { id: 'ML', EXTENDED_NAME: 'Mililitros' },
  },
  WEIGHT: {
    PROGRESS_TYPE: { ACCUMULATIVE: true, FINAL_RESULT: false, HABIT: false },
    FINAL_RESULT: { ADD: '', REDUCE: '' },
    ACCUMULATIVE: { ADD: 'Aumentar', REDUCE: 'Disminuir' },
    HABIT: { ADD: '', REDUCE: '' },
    UNIT: { id: 'KG', EXTENDED_NAME: 'Kilogramos' },
  },
  SLEEP: {
    PROGRESS_TYPE: { ACCUMULATIVE: false, FINAL_RESULT: false, HABIT: true },
    FINAL_RESULT: { ADD: '', REDUCE: '' },
    ACCUMULATIVE: { ADD: 'Acumular', REDUCE: '' },
    HABIT: { ADD: 'Meta diaria', REDUCE: '' },
    UNIT: { id: 'HOURS', EXTENDED_NAME: 'Horas' },
  },
  CALORIES: {
    PROGRESS_TYPE: { ACCUMULATIVE: true, FINAL_RESULT: true, HABIT: false },
    FINAL_RESULT: { ADD: '', REDUCE: '' },
    ACCUMULATIVE: { ADD: '', REDUCE: '' },
    HABIT: { ADD: '', REDUCE: '' },
    UNIT: { id: 'KCAL', EXTENDED_NAME: 'Calorias' },
  },
  FAT: {
    PROGRESS_TYPE: { ACCUMULATIVE: true, FINAL_RESULT: false, HABIT: false },
    FINAL_RESULT: { ADD: 'Llegar a', REDUCE: '' },
    ACCUMULATIVE: { ADD: '', REDUCE: 'Disminuir' },
    HABIT: { ADD: '', REDUCE: '' },
    UNIT: { id: 'KG', EXTENDED_NAME: 'Kilogramos' },
  },
  OTHER: {
    PROGRESS_TYPE: {
      ACCUMULATIVE: true,
      FINAL_RESULT: true,
      HABIT: true,
    },
    FINAL_RESULT: { ADD: 'Aumentar a', REDUCE: 'Reducir a' },
    ACCUMULATIVE: { ADD: 'Acumular', REDUCE: 'Disminuir' },
    HABIT: { ADD: 'Meta diaria', REDUCE: '' },
    UNIT: { id: 'KG', EXTENDED_NAME: 'Kilogramos' },
  },
};
export const CHALLENGE_LIBRARY: PreConfiguredChallenge[] = [
  {
    unit: {
      id: 'KG',
      name: 'Kg',
      extendedName: 'Kilogramos',
    },
    id: 'WEIGHT',
    name: 'Peso',
  },
  {
    unit: {
      id: 'DAYS',
      name: 'Dias',
      extendedName: 'Dias',
    },
    id: 'GYM',
    name: 'Gimnasio',
  },
  {
    unit: {
      id: 'ML',
      name: 'ml',
      extendedName: 'Mililitros',
    },
    id: 'WATER_INTAKE',
    name: 'Consumo de agua',
  },
  {
    unit: {
      id: 'STEPS',
      name: 'Pasos',
      extendedName: 'Pasos',
    },
    id: 'STEPS',
    name: 'Pasos',
  },
  {
    unit: {
      id: 'HOURS',
      name: 'Horas',
      extendedName: 'Horas',
    },
    id: 'SLEEP',
    name: 'Sueño',
  },
  {
    unit: {
      id: 'KCAL',
      name: 'Kcal',
      extendedName: 'Calorías',
    },
    id: 'CALORIES',
    name: 'Calorías',
  },
  {
    unit: {
      id: 'KG',
      name: 'Kg',
      extendedName: 'Kilogramos',
    },
    id: 'FAT',
    name: 'Grasa corporal',
  },
  {
    id: 'OTHER',
    name: 'Otro',
  },
];
export const CHALLENGE_TYPE_LIBRARY = [
  {
    name: '⭐️ Meta individual',
    type: 'INDIVIDUAL' as ChallengesParticipanTypes,
    description:
      'La meta individual la vivís como a ti te guste. Establece tu meta, monitorea tu progreso y supérate  a vos mismo.',
  },
  {
    name: '💥 Reto grupal',
    type: 'GROUP' as ChallengesParticipanTypes,
    description:
      'Invita a compañeros a unirse a un desafío entre ustedes. Definan su propia meta y ¡que gane el más activo!',
  },
];
export const GOAL_UNIT_OPTIONS: GoalUnit[] = [
  {
    id: 'KG',
    name: 'Kg',
    extendedName: 'Kilogramos',
  },
  {
    id: 'LB',
    name: 'Lb',
    extendedName: 'Libras',
  },
  {
    id: 'KCAL',
    name: 'Kcal',
    extendedName: 'Calorías',
  },
  {
    id: 'STEPS',
    name: 'Pasos',
    extendedName: 'Pasos',
  },
  {
    id: 'DAYS',
    name: 'Dias',
    extendedName: 'Dias',
  },
  {
    id: 'WEEKS',
    name: 'Semanas',
    extendedName: 'Semanas',
  },
  {
    id: 'HOURS',
    name: 'Horas',
    extendedName: 'Horas',
  },
  {
    id: 'MINUTES',
    name: 'Minutos',
    extendedName: 'Minutos',
  },
  {
    id: 'ML',
    name: 'ml',
    extendedName: 'Mililitros',
  },
];
// eslint-disable-next-line @typescript-eslint/naming-convention
export const DEFAULT_CHALLENGE_DATA: ChallengeListElement = {
  id: '',
  userId: '',
  name: '',
  description: '',
  reward: '',
  initialValue: 0,
  goalType: 'ADD',
  goal: 0,
  goalUnit: '',
  startDate: '',
  endDate: '',
  isRemainderActive: false,
  progressType: 'FINAL_RESULT',
  challengeParticipantType: 'INDIVIDUAL',
  challengeProgress: 0,
  isEvidenceRequired: true,
  timeProgress: 0,
  updatedAt: '',
  createdAt: '',
  time: '',
  lastUpdate: '',
  participants: [],
  goalUnitHuman: '',
  challengeProgressTypeHuman: '',
};
