// FileUpload.tsx
import { Accessor, For, Signal, createMemo } from 'solid-js';
import './custom-file-uploader.css';
import { IoAttach } from 'solid-icons/io';

type FileUploadProps = {
  id: string;
  fileList: Signal<File[]>;
  title?: string;
  numberOfFiles?: number;
  required?: boolean;
  acceptedTypes?: string;
  isEnabled?: Accessor<boolean>;
};
const CustomFileUploader = (props: FileUploadProps) => {
  const [fileList, setFileList] = props.fileList;
  const isEnabled = createMemo(() =>
    props.isEnabled === undefined || props.isEnabled() ? true : false
  );
  const handleDrop = (event: Event) => {
    if (!isEnabled()) return;
    event.preventDefault();
    const files = (event as DragEvent).dataTransfer?.files;
    if (files) {
      const filesArray = Array.from(files);
      setFileList(filesArray);
    }
  };
  const handleFileChange = (event: Event) => {
    if (!isEnabled()) return;
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      setFileList(Array.from(files));
    }
  };
  const handleClick = () => {
    if (!isEnabled()) return;
    const fileInput = document.getElementById(props.id);
    if (fileInput) {
      fileInput.click();
    }
  };
  const handleClear = () => {
    if (!isEnabled()) return;
    setFileList([]);
    const fileInput = document.getElementById(props.id) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };
  const preventDefault = (event: Event) => {
    event.preventDefault();
  };

  return (
    <div
      class={
        props.isEnabled && props.isEnabled() === true
          ? 'file-upload'
          : 'file-upload-disabled'
      }
      onDrop={handleDrop}
      onDragOver={preventDefault}
      onDragEnter={preventDefault}
      onClick={handleClick}
    >
      <input
        type="file"
        id={props.id}
        multiple={
          !props.numberOfFiles || props.numberOfFiles <= 1 ? false : true
        }
        class="file-input"
        required={props.required}
        accept={props.acceptedTypes}
        onChange={handleFileChange}
        disabled={!isEnabled()}
      />
      <label for="fileInput">
        {fileList().length > 0 && (
          <>
            <div class="file-list">
              <For each={fileList()}>
                {(file) => <div class="file-item">{file.name}</div>}
              </For>
            </div>
            <button class="btn btn-sm btn-primary mt-10" onClick={handleClear}>
              Limpiar
            </button>
          </>
        )}
      </label>

      {fileList().length === 0 && (
        <>
          <IoAttach
            size={40}
            color={
              props.isEnabled && props.isEnabled() === true
                ? '#c3c3cf'
                : '#eaecf0'
            }
          />
          <div class="file-upload-text">
            {props.title ||
              'Arrastra y suelta los archivos aquí o haz clic para seleccionarlos'}
          </div>
        </>
      )}
    </div>
  );
};
export default CustomFileUploader;
