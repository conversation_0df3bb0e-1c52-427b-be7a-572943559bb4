import { SetStoreFunction, createStore } from 'solid-js/store';
import { createContext, useContext } from 'solid-js';
import { ChallengeProgressLog } from './challenge-progress-types';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';

export type ChallengeProgressModalContextModel = {
  challengeProgressLog: ChallengeProgressLog;
  setChallengeProgressLog: SetStoreFunction<ChallengeProgressLog>;
};
const ChallengeProgressModalContext =
  createContext<ChallengeProgressModalContextModel>();
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ChallengeProgressModalProvider = (props: any) => {
  const { id: userId } = getCurrentUserData();
  const [challengeProgressLog, setChallengeProgressLog] =
    createStore<ChallengeProgressLog>({
      userId,
      progressRecordList: [],
    });
  const value: ChallengeProgressModalContextModel = {
    challengeProgressLog,
    setChallengeProgressLog,
  };

  return (
    <ChallengeProgressModalContext.Provider value={value}>
      {props.children}
    </ChallengeProgressModalContext.Provider>
  );
};
export function useChallengeProgressModal():
  | ChallengeProgressModalContextModel
  | undefined {
  return useContext(ChallengeProgressModalContext);
}
