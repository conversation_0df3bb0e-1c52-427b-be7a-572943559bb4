import { Col, Form, FormGroup, Row } from 'solid-bootstrap';
import { createEffect, createMemo, createSignal } from 'solid-js';
import CustomFileUploader from '../custom-file-uploader/custom-file-uploader';
import {
  NumericProgressSectionProps,
  ProgressRecord,
} from './challenge-progress-types';
import { useChallengeProgressModal } from './challenge-progress-modal-context';
import { createStore } from 'solid-js/store';

const NumericProgressSection = (props: NumericProgressSectionProps) => {
  const progressContext = useChallengeProgressModal();
  const [fileList, setFileList] = createSignal<File[]>([]);
  const [progressRecord, setProgressRecord] = createStore<ProgressRecord>({
    progress: 0,
    date: new Date().toISOString(),
    evidence: null,
  });
  const progressDescription = createMemo(() => {
    switch (props.progressType) {
      case 'ACCUMULATIVE':
        return 'Registra tu progreso, este se acumulara a lo largo del tiempo.';
      case 'FINAL_RESULT':
        return 'Registra tu progreso, este registro se establecerá como el resultado final.';
      default:
        return 'Registra tu progreso';
    }
  });
  createEffect(() => {
    const file = fileList()[0] || null;
    setProgressRecord('evidence', file);
  });
  createEffect(() => {
    progressContext &&
      progressContext.setChallengeProgressLog('progressRecordList', [
        progressRecord,
      ]);
  });

  return (
    <>
      <Form.Label class="form-label mt-0">{progressDescription()}</Form.Label>
      <Row class="mt-8">
        <Col sm={8} md={5} lg={5}>
          <FormGroup>
            <FormGroup>
              <Form.Control
                class="form-control"
                type="number"
                min="0"
                id="value"
                name="value"
                value={progressRecord.progress}
                onInput={(e: any) => {
                  const value = parseInt(e.target.value) || 0;
                  setProgressRecord('progress', value);
                }}
                required
              />
            </FormGroup>
          </FormGroup>
        </Col>
        <Col sm={4} md={5} lg={5}>
          <input
            class="form-control form-control-transparent"
            value={props.unit}
            disabled
          ></input>
        </Col>
      </Row>
      <input
        style={{ display: 'none' }}
        id="goalUnit"
        name="goalUnit"
        class="form-control form-control-transparent"
        disabled
      ></input>
      <FormGroup class="my-10">
        <Form.Label class="mb-8">{`Documenta este registro: ${
          !props.isEvidenceRequired ? '(opcional)' : ''
        }`}</Form.Label>
        <CustomFileUploader
          id={'evidence-file'}
          fileList={[fileList, setFileList]}
          numberOfFiles={1}
          acceptedTypes="image/*"
          title="Sube una imagen de tu progreso"
        />
      </FormGroup>
      <div class="w-100 text-center">
        <small>Formatos aceptados: Imagen </small>
      </div>
    </>
  );
};
export default NumericProgressSection;
