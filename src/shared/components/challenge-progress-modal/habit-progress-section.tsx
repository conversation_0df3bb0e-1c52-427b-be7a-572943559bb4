import { Form, Row } from 'solid-bootstrap';
import { Index, Show, createMemo } from 'solid-js';
import { useChallengeDates } from '../../../apps/challenges/hooks/use-challenge-dates';
import {
  HabitProgressSectionProps,
  ProgressRecord,
} from './challenge-progress-types';
import { useChallengeProgressModal } from './challenge-progress-modal-context';
import CustomCheckBoxForm from '../custom-checkbox/custom-checkbox-form';
import { ChallengeServices } from '../../../apps/challenges/services/challenge-services';
import { useChallengeProgress } from '../../../shared/infra/hooks/use-challenge-progress';
import { ErrorAlert } from '../alert/error-alert';
import { LoadingAlert } from '../alert/loading-alert';

const HabitProgressSection = (props: HabitProgressSectionProps) => {
  const progressContext = useChallengeProgressModal();
  const {
    isLoading,
    error,
    challengeProgress: userChallengeProgress,
  } = useChallengeProgress({
    id: props.challengeId,
  });
  const challengeDates = createMemo(() =>
    useChallengeDates({ startDate: props.startDate, endDate: props.endDate })
  );
  const addProgressRecord = (progressRecord: ProgressRecord) => {
    progressContext &&
      progressContext.setChallengeProgressLog('progressRecordList', (prev) => [
        ...prev,
        progressRecord,
      ]);
  };
  const removeProgressRecord = (date: string) => {
    progressContext &&
      progressContext.setChallengeProgressLog('progressRecordList', (prev) =>
        prev.filter((d) => d.date !== date)
      );
  };
  const doesProgressRecordExist = (date: string) => {
    return (
      (progressContext &&
        progressContext.challengeProgressLog.progressRecordList.some(
          (progress) => progress.date === date
        )) ||
      false
    );
  };

  return (
    <>
      <div>
        <ErrorAlert error={[error]} />
        <LoadingAlert loadingFlags={[isLoading]} showLoadingAnimation />
        <Show when={!isLoading()}>
          <Form.Label class="form-label mt-0 mb-10">
            Selecciona el dia que deseas registrar.
          </Form.Label>
          <div
            class="w-100 d-flex pb-5 mb-2 gap-5"
            // eslint-disable-next-line @typescript-eslint/naming-convention
            style={{ 'overflow-x': 'auto' }}
          >
            <Index each={challengeDates()}>
              {(date, i) => {
                const isChecked = createMemo(
                  () =>
                    ChallengeServices.IsProgressPresentForThatDate({
                      date: date().toISOString(),
                      userChallengeProgress,
                    }) || doesProgressRecordExist(date().toISOString())
                );
                const isEvidencePrevUploaded = createMemo(() =>
                  ChallengeServices.IsProgressPresentForThatDate({
                    date: date().toISOString(),
                    userChallengeProgress,
                  })
                );
                return (
                  <Row id={i.toString()}>
                    <CustomCheckBoxForm
                      date={date()}
                      isChecked={isChecked}
                      addProgressRecord={addProgressRecord}
                      removeProgressRecord={removeProgressRecord}
                      isEvidencePrevUploaded={isEvidencePrevUploaded}
                      isEvidenceRequired
                    />
                  </Row>
                );
              }}
            </Index>
          </div>
          <div class="w-100 text-center">
            <small>Formatos aceptados: Imagen </small>
          </div>
        </Show>
      </div>
    </>
  );
};
export default HabitProgressSection;
