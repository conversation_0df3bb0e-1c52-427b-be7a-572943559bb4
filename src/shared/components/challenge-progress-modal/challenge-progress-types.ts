export type ChallengeProgressLog = {
  userId: string;
  progressRecordList: ProgressRecord[];
};
export type ProgressRecord = {
  date: string;
  evidence: File | null;
  progress: number;
};
export type HabitProgressSectionProps = {
  challengeId: string;
  startDate: string;
  endDate: string;
};
export type NumericProgressSectionProps = {
  isEvidenceRequired: boolean;
  unit: string;
  progressType: string;
};
export type ChallengeProgressEntry = {
  challengeId: string;
  userId: string;
  userName: string;
  value: number;
  evidence: {
    name: string;
    size: number;
    type: string;
    file: string;
  } | null;
  challengeProgressType: 'HABIT' | 'NUMERIC';
  progressDate?: string;
};
