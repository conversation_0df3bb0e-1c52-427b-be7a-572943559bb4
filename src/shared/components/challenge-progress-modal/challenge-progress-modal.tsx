/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { Button, Form, Modal } from 'solid-bootstrap';
import {
  Match,
  Show,
  Switch,
  createEffect,
  createMemo,
  createSignal,
} from 'solid-js';
import { ChallengeProgressModalProps } from '../../../apps/challenges/types';
import { useChallengesContext } from '../../../apps/challenges/components/context';
import HabitProgressSection from './habit-progress-section';
import NumericProgressSection from './numeric-progress-section';
import { useChallengeProgressModal } from './challenge-progress-modal-context';
import { ChallengeProgressServices } from '../../services/challenge/progress-form-services';
import Animation from '../lottie-player/animation';
import { usePostChallengeProgress } from '../../../shared/infra/hooks/use-post-challenge-progress';
import { useChallengeDates } from '../../../apps/challenges/hooks/use-challenge-dates';
import { ErrorAlert } from '../alert/error-alert';
import { ProgressRecord } from './challenge-progress-types';
import './styles.css';

const ChallengeProgressModal = (props: ChallengeProgressModalProps) => {
  const [formError, setFormError] = createSignal<string | null>(null);
  const {
    isLoading,
    setIsLoading,
    error,
    resetStatus,
    postBatchChallengeProgress,
  } = usePostChallengeProgress();
  const context = useChallengesContext();
  const progressContext = useChallengeProgressModal();
  if (!context || !props.challenge()) return;
  const challengeProgressServices = new ChallengeProgressServices({
    challengeId: props.challenge().id,
    challengeProgressType: props.challenge().progressType,
  });
  const isHabitChallenge = createMemo(
    () => props.challenge().progressType === 'HABIT'
  );
  const [isChallengeStarted, setIsChallengeStarted] = createSignal(true);
  const updateChallengeProgress = async (e: Event) => {
    e.preventDefault();
    setFormError(null);
    try {
      if (!progressContext) throw 'CONTEXT_NOT_FOUND';
      const progress = progressContext.challengeProgressLog.progressRecordList;
      validateFormData(progress);
      setIsLoading(true);
      const challengeProgressEntryList = await mapProgressData(progress);
      await postBatchChallengeProgress(challengeProgressEntryList);
      props.callbackAfterUpdate && props.callbackAfterUpdate();
      progressContext.setChallengeProgressLog('progressRecordList', []);
    } catch (error) {
      const textError = challengeProgressServices.mapErrorToText(error);
      setFormError(textError);
      setIsLoading(false);
    }
  };
  const validateFormData = (progress: ProgressRecord[]) => {
    challengeProgressServices.validateProgressForm({
      isHabit: isHabitChallenge(),
      isEvidenceRequired: props.challenge().isEvidenceRequired,
      progress,
    });
  };
  const mapProgressData = async (progress: ProgressRecord[]) => {
    return await challengeProgressServices.mapProgressRecordListToChallengeProgressEntry(
      progress
    );
  };
  const cleanChallengeProgressEntry = () => {
    progressContext &&
      progressContext.setChallengeProgressLog('progressRecordList', []);
  };
  createEffect(() => {
    if (!props.showAddProgressModal()) {
      cleanChallengeProgressEntry();
      resetStatus();
      setFormError(null);
    }
  });
  createEffect(() => {
    if (props.challenge() && props.challenge().startDate) {
      const challengeDates = useChallengeDates({
        startDate: props.challenge().startDate,
        endDate: props.challenge().endDate,
      });
      setIsChallengeStarted(challengeDates.length > 0);
    }
  });

  return (
    <>
      <Modal
        show={props.showAddProgressModal()}
        onHide={() => props.setShowAddProgressModal(false)}
        aria-labelledby="contained-modal-title-vcenter"
        dialogClass="progress-modal"
        centered
      >
        <Form onSubmit={async (e) => await updateChallengeProgress(e)}>
          <Modal.Header closeButton>
            <h1 class="modal-option-h1 mt-3 mb-0">Registra tu progreso</h1>
          </Modal.Header>
          <Modal.Body>
            <Switch>
              <Match when={!isChallengeStarted()}>
                <p>
                  Este reto aún no ha iniciado, por lo que aún no permite el
                  registro de progreso.
                </p>
              </Match>
              <Match when={isChallengeStarted() && !isLoading()}>
                <Show when={isHabitChallenge()}>
                  <HabitProgressSection
                    startDate={props.challenge().startDate}
                    endDate={props.challenge().endDate}
                    challengeId={props.challenge().id}
                  />
                  <p class="text-center mt-5 font-weight-bold mb-0">
                    {props.challenge().isEvidenceRequired
                      ? 'Evidencia: requerida*'
                      : 'Evidencia: Opcional'}
                  </p>
                </Show>
                <Show when={!isHabitChallenge()}>
                  <NumericProgressSection
                    isEvidenceRequired={props.challenge().isEvidenceRequired}
                    progressType={props.challenge().progressType}
                    unit={props.challenge().goalUnitHuman}
                  />
                </Show>
                <div class="pt-5">
                  <ErrorAlert error={[error, formError]} />
                </div>
              </Match>
              <Match when={isChallengeStarted() && isLoading()}>
                <Animation />
              </Match>
            </Switch>
          </Modal.Body>
          <Modal.Footer>
            <Button
              type="button"
              class="btn btn-outline btn-outline btn-outline-primary btn-active-light-primary my-5 mt-5 ms-3"
              onClick={() => props.setShowAddProgressModal(false)}
              disabled={isLoading()}
            >
              Regresar
            </Button>
            <Button
              type="submit"
              disabled={!isChallengeStarted() || isLoading()}
              class="btn btn-primary btn-active-light-primary my-5 mt-5 ms-3"
            >
              Guardar
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </>
  );
};
export default ChallengeProgressModal;
