import { Form } from 'solid-bootstrap';
import { Show, createEffect, createMemo, createSignal } from 'solid-js';
import CustomEvidenceUploader from './custom-evidence-uploader';
import { ProgressRecord } from '../challenge-progress-modal/challenge-progress-types';
import { createStore } from 'solid-js/store';
import { CustomCheckBoxFormProps } from './custom-checkbox-types';

const CustomCheckBoxForm = (props: CustomCheckBoxFormProps) => {
  const isEvidenceRequired = createMemo(
    () => props.isEvidenceRequired || false
  );
  const [fileList, setFileList] = createSignal<File[]>([]);
  const [progressRecord, setProgressRecord] = createStore<ProgressRecord>({
    progress: 0,
    date: props.date.toISOString(),
    evidence: null,
  });
  const displayName = createMemo(() =>
    props.date.toLocaleDateString('es-MX', {
      weekday: 'long',
    })
  );
  const dayNumber = createMemo(() => props.date.getDate());
  const monthName = createMemo(() =>
    props.date.toLocaleDateString('es-MX', {
      month: 'long',
    })
  );
  const onChecked = () => {
    if (props.isChecked()) {
      props.removeProgressRecord(props.date.toISOString());
      setFileList([]);
    } else {
      props.addProgressRecord(progressRecord);
    }
  };
  createEffect(() => {
    const file = fileList()[0] || null;
    setProgressRecord('evidence', file);
  });
  const isUploaderEnabled = createMemo(
    () => !props.isEvidencePrevUploaded() && props.isChecked()
  );

  return (
    <>
      <div class="w-100 text-center">
        <div class="w-100 m-auto px-10 text-center custom-checkbox-container pt-2 pb-5">
          <p class="custom-form-title mb-0 text-capitalize mt-2">
            {monthName()}
          </p>
          <p class="custom-form-label mb-0 text-capitalize mt-2">
            {displayName()}
          </p>
          <p class="custom-form-label-number mb-8 mt-1">{dayNumber()}</p>
          <div class="mb-5">
            <Form.Check type="checkbox">
              <Form.Check.Input
                type="checkbox"
                class="custom-form-check-input"
                checked={props.isChecked()}
                onChange={onChecked}
                disabled={props.isEvidencePrevUploaded()}
              />
            </Form.Check>
            <Show when={isEvidenceRequired()}>
              <div class="mt-8">
                <CustomEvidenceUploader
                  id={props.date.toISOString()}
                  fileList={[fileList, setFileList]}
                  numberOfFiles={1}
                  acceptedTypes="image/jpg, image/jpeg, image/png"
                  isEnabled={isUploaderEnabled}
                />
              </div>
            </Show>
          </div>
        </div>
      </div>
    </>
  );
};
export default CustomCheckBoxForm;
