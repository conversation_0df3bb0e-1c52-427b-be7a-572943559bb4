import { Accessor } from 'solid-js';
import { ProgressRecord } from '../challenge-progress-modal/challenge-progress-types';

export type CustomCheckBoxFormProps = {
  date: Date;
  isEvidenceRequired?: boolean;
  isChecked: Accessor<boolean>;
  isEvidencePrevUploaded: Accessor<boolean>;
  addProgressRecord: (progressRecord: ProgressRecord) => void;
  removeProgressRecord: (date: string) => void;
};
export type CustomCheckBoxDetailProps = {
  dayName: string;
  monthName: string;
  dayNumber: number;
  isChecked: boolean;
  evidence?: string;
};
