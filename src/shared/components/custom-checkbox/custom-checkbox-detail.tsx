import { Form, Row } from 'solid-bootstrap';
import { CustomCheckBoxDetailProps } from './custom-checkbox-types';
import ChallengeEvidenceModal from '../challenge/challenge-evidence-modal';
import { createSignal } from 'solid-js';
import './custom-checkbox-style.css';

const CustomCheckBoxDetail = ({
  dayName,
  monthName,
  dayNumber,
  isChecked,
  evidence,
}: CustomCheckBoxDetailProps) => {
  const [show, setShow] = createSignal(false);
  const viewEvidence = (event: Event) => {
    event.preventDefault();
    evidence && setShow(true);
  };

  return (
    <>
      <Row class="mx-2">
        <div class="w-100 text-center px-0">
          <div class="w-100 m-auto px-3 text-center custom-checkbox-container pt-2 pb-5">
            <p class="custom-form-title mb-0 text-capitalize mt-2">
              {monthName}
            </p>
            <div>
              <p class="custom-form-label mb-0 text-capitalize">{dayName}</p>
            </div>
            <div>
              <p class="custom-form-label-number mb-8 mt-3">{dayNumber}</p>
            </div>
            <Form.Group>
              <div
                class="mb-5"
                style={{ cursor: evidence && 'pointer' }}
                onClick={viewEvidence}
              >
                <Form.Check type="checkbox" id={'habit-checkbox'}>
                  <Form.Check.Input
                    type="checkbox"
                    class={
                      evidence
                        ? 'custom-form-check-input-with-evidence'
                        : 'custom-form-check-input'
                    }
                    checked={isChecked}
                    disabled
                  />
                </Form.Check>
              </div>
            </Form.Group>
          </div>
        </div>
      </Row>
      <ChallengeEvidenceModal
        show={show}
        setShow={setShow}
        evidence={evidence}
      />
    </>
  );
};
export default CustomCheckBoxDetail;
