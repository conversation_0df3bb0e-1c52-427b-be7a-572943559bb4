import { Accessor, Show, Signal, createMemo } from 'solid-js';
import '../custom-file-uploader/custom-file-uploader.css';
import { IoAttach, IoTrash } from 'solid-icons/io';
import { Button, Form } from 'solid-bootstrap';

type CustomEvidenceUploaderProps = {
  id: string;
  fileList: Signal<File[]>;
  numberOfFiles?: number;
  required?: boolean;
  acceptedTypes?: string;
  isEnabled?: Accessor<boolean>;
};
const CustomEvidenceUploader = (props: CustomEvidenceUploaderProps) => {
  const [fileList, setFileList] = props.fileList;
  const isEnabled = createMemo(() =>
    props.isEnabled === undefined ? false : props.isEnabled()
  );
  const handleDrop = (event: Event) => {
    if (!isEnabled()) return;
    const files = (event as DragEvent).dataTransfer?.files;
    if (files) {
      const filesArray = Array.from(files);
      setFileList(filesArray);
    }
  };
  const handleFileChange = (event: Event) => {
    if (!isEnabled()) return;
    const files = (event.target as HTMLInputElement).files;
    if (files) {
      setFileList(Array.from(files));
    }
  };
  const handleClick = () => {
    if (!isEnabled()) return;
    const fileInput = document.getElementById(props.id);
    if (fileInput) {
      fileInput.click();
    }
  };
  const handleClear = () => {
    if (!isEnabled()) return;
    setFileList([]);
    const fileInput = document.getElementById(props.id) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };
  const preventDefault = (event: Event) => {
    event.preventDefault();
  };

  return (
    <Form onSubmit={preventDefault}>
      <div
        class={
          props.isEnabled && props.isEnabled() === true
            ? 'file-upload'
            : 'file-upload-disabled'
        }
        onDrop={handleDrop}
        onDragOver={preventDefault}
        onDragEnter={preventDefault}
        onClick={handleClick}
      >
        <Form.Group>
          <input
            type="file"
            id={props.id}
            multiple={
              !props.numberOfFiles || props.numberOfFiles <= 1 ? false : true
            }
            class="file-input"
            required={props.required}
            accept={props.acceptedTypes}
            onChange={handleFileChange}
            disabled={!isEnabled()}
          />
          <label for="fileInput">
            <Show when={fileList().length > 0}>
              <Button class="btn btn-sm btn-primary px-3" onClick={handleClear}>
                <IoTrash size={24} />
              </Button>
            </Show>
          </label>
        </Form.Group>

        <Show when={fileList().length === 0}>
          <button
            class={`btn btn-sm ${
              props.isEnabled && props.isEnabled() === true
                ? 'btn-secondary'
                : 'btn-light'
            }btn-secondary px-3`}
          >
            <IoAttach
              size={24}
              color={
                props.isEnabled && props.isEnabled() === true
                  ? '#c3c3cf'
                  : '#eaecf0'
              }
            />
          </button>
        </Show>
      </div>
    </Form>
  );
};
export default CustomEvidenceUploader;
