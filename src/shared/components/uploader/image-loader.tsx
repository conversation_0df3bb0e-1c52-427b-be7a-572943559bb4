import { createSignal, onCleanup } from 'solid-js';

export type ImageLoaderProps = {
  src: string;
  class?: string;
  width?: string | number;
  height?: string | number;
};
function ImageLoader(props: ImageLoaderProps) {
  const [imageSrc, setImageSrc] = createSignal(props.src);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal(false);
  const loadImage = () => {
    const img = new Image();
    img.src = props.src;
    img.onload = () => {
      setImageSrc(props.src);
      setLoading(false);
    };
    img.onerror = () => {
      setLoading(false);
      setError(true);
    };
    onCleanup(() => {
      // Cleanup if component unmounts before the image loads
      img.onload = null;
      img.onerror = null;
    });
  };
  // Load the image when the component mounts
  loadImage();

  return (
    <div class="image-loader">
      {loading() && (
        <div class="loading w-100">
          <p class="text-center">Loading...</p>
        </div>
      )}
      {error() && (
        <div class="error w-100">
          <p class="text-center"> Error loading image</p>
        </div>
      )}
      {!loading() && !error() && (
        <img
          class={props.class}
          src={imageSrc()}
          height={props.height}
          width={props.width}
          alt="Loaded Image"
        />
      )}
    </div>
  );
}
export default ImageLoader;
