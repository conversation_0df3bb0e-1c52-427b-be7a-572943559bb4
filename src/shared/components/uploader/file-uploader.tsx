import { FileUploaderProps } from 'apps/profile/types';

const FileUploader = (props: FileUploaderProps) => {
  return (
    <>
      <div class="fv-row">
        <div class="dropzone">
          <div class="dz-message needsclick">
            <i class="bi bi-file-earmark-arrow-up text-primary fs-3x"></i>
            <div class="ms-4">
              <input
                class="form-control"
                id={props.id}
                name={props.id}
                type="file"
                accept="image/*"
                required={props.isRequired || false}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default FileUploader;
