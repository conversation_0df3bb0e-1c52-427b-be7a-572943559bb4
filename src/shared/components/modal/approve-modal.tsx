import { Modal, Button, Form } from 'solid-bootstrap';
import { Accessor, createSignal, Setter } from 'solid-js';

type ApproveModal = {
  title: string;
  show: Accessor<boolean>;
  setShow: Setter<boolean>;
  onSave: any;
};
const ApproveModal = (props: ApproveModal) => {
  const [detail, setDetail] = createSignal<string>('');
  const handleClose = () => props.setShow(false);

  return (
    <>
      <Modal
        show={props.show()}
        onHide={handleClose}
        size="lg"
        aria-labelledby="contained-modal-title-vcenter"
        centered
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            {props.title}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group class="mb-3" controlId="exampleForm.ControlTextarea1">
              <Form.Label>Detalles:</Form.Label>
              <Form.Control
                as="textarea"
                rows={6}
                value={detail()}
                onInput={(e) => {
                  setDetail((e.target as HTMLInputElement).value || '');
                }}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button class="btn btn-sm btn-secondary" onClick={handleClose}>
            Close
          </Button>
          <Button
            class="btn btn-sm btn-success"
            onClick={() => {
              props.onSave(detail());
            }}
          >
            Aprobar
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
export default ApproveModal;
