import RangeTable from '../../../apps/health/components/lab-examn/components/range-table';
import { UserHealthData } from 'entities/UserMedicalDataReport';
import { useScreenWidth } from '../../hooks/use-screen-width';
import { MedicalDataService } from '../../services/medical-data/medical-data-services';
import { SolidApexCharts } from 'solid-apexcharts';
import { Show } from 'solid-js';
import { useLabReportChart } from '../../hooks/use-lab-report-chart';

export type LabReportChartProps = {
  data: UserHealthData;
};
export const LabReportChart = (props: LabReportChartProps) => {
  const screenWidth = useScreenWidth();
  const chartOptions = useLabReportChart({
    heathData: props.data,
    screenWidth,
  });
  const detail = MedicalDataService.getLabExamDetailById(props.data.id);
  const rangeTable = MedicalDataService.getLabExamRangeTableByIdAndCountry(
    props.data.id,
    'OTHER'
  );
  if (!detail) return null;

  return (
    <div>
      <div class="ml-20 mb-10 p-4 health-item-container">
        <h3>{`${detail.title} ${detail.unit || ''}`}</h3>
        <p class="font-italic">{detail.description}</p>
        <Show when={detail.reference}>
          <p
            class={`health-page-subheader ${
              screenWidth() > 900 ? 'mb-20' : 'mb-3'
            } `}
          >
            Referencia: &nbsp;
            <a
              target="_blank"
              rel="noopener noreferrer"
              href={detail.reference}
            >
              Link
            </a>
          </p>
        </Show>
        <h1>{`${props.data.lastValue} ${rangeTable?.unit || ''}`}</h1>
      </div>
      <div class="mt-10">
        <div class="ml-20 mb-10 p-4 health-item-container">
          <SolidApexCharts class="my-chart" type="line" {...chartOptions} />
        </div>
        <Show when={rangeTable}>
          <RangeTable data={rangeTable} />
        </Show>
      </div>
    </div>
  );
};
