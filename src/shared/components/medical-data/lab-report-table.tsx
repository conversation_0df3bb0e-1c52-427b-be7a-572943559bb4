import RangeTable from '../../../apps/health/components/lab-examn/components/range-table';
import { UserHealthData } from 'entities/UserMedicalDataReport';
import { useScreenWidth } from '../../hooks/use-screen-width';
import { For, Show } from 'solid-js';
import { MedicalDataService } from '../../services/medical-data/medical-data-services';
import { Table } from 'solid-bootstrap';

export type LabReportTableProps = {
  data: UserHealthData;
};
export const LabReportTable = (props: LabReportTableProps) => {
  const screenWidth = useScreenWidth();
  const detail = MedicalDataService.getLabExamDetailById(props.data.id);
  const rangeTable = MedicalDataService.getLabExamRangeTableByIdAndCountry(
    props.data.id,
    'OTHER'
  );
  if (!detail) return null;

  return (
    <div class="my-3">
      <div class="ml-20 p-4 health-item-container">
        <h3>{`${detail.title} ${detail.unit || ''}`}</h3>
        <p class="font-italic">{detail.description}</p>
        <Show when={detail.reference}>
          <p
            class={`health-page-subheader ${
              screenWidth() > 900 ? 'mb-10' : 'mb-3'
            } `}
          >
            Referencia: &nbsp;
            <a
              target="_blank"
              rel="noopener noreferrer"
              href={detail.reference}
            >
              Link
            </a>
          </p>
        </Show>
        <h1>{`Ultimo Resultado: ${props.data.lastValue} ${
          rangeTable?.unit || ''
        }`}</h1>
      </div>
      <div class="mt-0">
        <div class="ml-20 mb-5 p-4 health-item-container">
          <Table
            class="table table-rounded table-striped border gy-7 gs-7"
            striped
            bordered
            hover
          >
            <thead>
              <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                <th>Nombre</th>
                <th>Resultado</th>
                <th>Fecha</th>
              </tr>
            </thead>
            <tbody>
              <For each={props.data.values}>
                {(userHealthDataValues) => (
                  <tr>
                    <td>{props.data.title}</td>
                    <td>{userHealthDataValues.examnResult}</td>
                    <td>{userHealthDataValues.date}</td>
                  </tr>
                )}
              </For>
            </tbody>
          </Table>
        </div>
        <Show when={rangeTable}>
          <div class="p-4 health-item-container">
            <RangeTable data={rangeTable} isClosed />
          </div>
        </Show>
      </div>
    </div>
  );
};
