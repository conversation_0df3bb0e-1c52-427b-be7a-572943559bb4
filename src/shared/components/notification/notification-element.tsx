/* eslint-disable @typescript-eslint/naming-convention */
import { Notification } from 'entities/Notification';
import { Button } from 'solid-headless';
import { createMemo, createSignal } from 'solid-js';

export interface INotificationElementProps {
  notification: Notification;
  i: number;
  markAsRead: (id: string) => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
}
const NotificationElement = ({
  notification,
  i,
  markAsRead,
  deleteNotification,
}: INotificationElementProps) => {
  const { id, title, message, callToAction, isRead } = notification;
  const [hovered, setHovered] = createSignal(false);
  const handleCTAClick = async () => {
    if (!isRead) {
      await markAsRead(id);
    }
    window.location.assign(callToAction);
  };
  const notificationContainer = createMemo(() => ({
    border: `solid 1px ${isRead ? '#f2f2f2' : '#0077F5'}`,
    'border-radius': '15px',
    cursor: !isRead && hovered() ? 'pointer' : 'unset',
    'background-color': !isRead && hovered() ? '#e8f1fb' : 'transparent',
  }));
  const notificationContent = {
    color: '#716E6E',
    'font-size': '14px',
    'font-style': 'normal',
    'font-weight': '400',
    'line-height': 'normal',
  };
  const notificationTitle = {
    color: '#000',
    'font-size': '14px',
    'font-style': 'normal',
    'line-height': 'normal',
  };

  return (
    <div
      class={`notification py-0 my-2`}
      style={`${i === 0 && 'border-top: 0px'}`}
    >
      <div
        class="p-4 pt-1 my-2 pb-8 px-8"
        style={notificationContainer()}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        <div class="w-100 text-end">
          <Button
            class="btn btn-sm btn-icon btn-active-light-primary"
            onclick={() => {
              deleteNotification(id);
            }}
          >
            <i class="bi bi-x"></i>
          </Button>
        </div>
        <div
          onClick={() => {
            // eslint-disable-next-line no-console
            !isRead && markAsRead(id);
          }}
        >
          <h3 style={notificationTitle}>{title}</h3>
          <p
            class="notification-content"
            style={notificationContent}
            innerHTML={message}
          ></p>
          {callToAction && (
            <a
              class="btn btn-sm btn-primary btn-sm align-self-center my-2 px-10 py-1 text-end"
              onClick={handleCTAClick}
            >
              Ver
            </a>
          )}
        </div>
      </div>
    </div>
  );
};
export default NotificationElement;
