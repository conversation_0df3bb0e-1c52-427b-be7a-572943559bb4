/* eslint-disable @typescript-eslint/naming-convention */
import { useScreenWidth } from '../../hooks/use-screen-width';
import icon from '../../../assets/media/icons/empty-table.png';

type EmptyTableIconProps = {
  class?: string;
  style?: string;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const EmptyTableIcon = (props: EmptyTableIconProps) => {
  const screenWidth = useScreenWidth();

  return (
    <div class={`w-100 text-center mt-20 ${props.class}}`}>
      <img
        style={{
          'max-width': `${screenWidth() > 900 ? '10vw' : '30vw'}`,
          'border-radius': '0.625rem',
        }}
        src={icon}
        alt="empty-table-icon"
      />
    </div>
  );
};
export default EmptyTableIcon;
