import { User } from 'entities/User';
import { Accessor } from 'solid-js';

export type UserTableProps = {
  title?: string;
  header: TableHeader[];
  control: TableControlHeader[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Accessor<User[]>;
  pageSize?: number;
  isTableBordered?: boolean;
  isTableStriped?: boolean;
  isSearchEnabled?: boolean;
  isExportEnabled?: boolean;
  cssClass?: string;
  filterByUser?: boolean;
};
export type TableProps = {
  title?: string;
  header: TableHeader[];
  control: TableControlHeader[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Accessor<any[]>;
  pageSize?: number;
  isTableBordered?: boolean;
  isTableStriped?: boolean;
  isSearchEnabled?: boolean;
  isExportEnabled?: boolean;
  cssClass?: string;
  filterByUser?: boolean;
};
export type StoreTableProps = {
  title?: string;
  header: TableHeader[];
  control: TableControlHeader[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  pageSize?: number;
  isTableBordered?: boolean;
  isTableStriped?: boolean;
  isSearchEnabled?: boolean;
  isExportEnabled?: boolean;
};
export type TableHeader = {
  name: string;
  title: string;
  type: 'text' | 'number';
  width?: number;
  css?: string;
  cssClass?: string;
};
export type TableControlHeader = {
  name: string;
  title: string;
  controlType: 'danger' | 'primary' | 'secondary';
  // eslint-disable-next-line
  callback: any;
  width?: number;
  css?: string;
  cssClass?: string;
  icon?: string;
  // eslint-disable-next-line
  dynamicClass?: (row: any) => string;
  // eslint-disable-next-line
  dynamicText?: (row: any) => string;
  // eslint-disable-next-line
  isDisabled?: (row: any) => boolean;
};
export type Sorter = {
  property: string;
  isDescending?: boolean;
};
