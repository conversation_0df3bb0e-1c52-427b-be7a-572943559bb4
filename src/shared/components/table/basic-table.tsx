import {
  arraySorter,
  arraySplitter,
  arrayFilter,
  arrangeProperties,
} from '../../services/utils/array-services';
import { createEffect, createSignal, For, Index } from 'solid-js';
import { TableControlHeader, Sorter, TableProps, TableHeader } from './types';
import { exportToExcel } from '../../services/utils/excel-services';
import { Card, Pagination, Row } from 'solid-bootstrap';
import EmptyTableIcon from './empty-table-icon';
import { getCurrentUserData } from '../../services/user/user-session-management';

const PAGE_SIZE_DEFAULT = 15;
const [searchKeyword, setSearchKeyword] = createSignal('');
const [currentPage, setCurrentPage] = createSignal(0);
const [sorter, setSorter] = createSignal<Sorter>({
  property: '',
  isDescending: false,
});
const renderControlButton = (
  control: TableControlHeader,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  row: any,
  props: TableProps
) => {
  const { callback } = control;
  const currentUserId = getCurrentUserData().id;
  if (
    props.filterByUser === true &&
    control.controlType === 'danger' &&
    row.userId !== currentUserId
  ) {
    return <></>;
  }

  return (
    <button
      type="button"
      role="button"
      class={`btn btn-${control.controlType} py-1 m-auto text-center`}
      onClick={() => {
        callback(row);
      }}
      disabled={
        props.filterByUser === true &&
        control.controlType === 'danger' &&
        row.userId !== currentUserId
      }
    >
      {control.icon ? <i class={control.icon + ' p-0'}></i> : control.title}
    </button>
  );
};
function BasicTableComponent(props: TableProps) {
  const prepareHeaderBasedOnColumType = (
    header: TableHeader[],
    control: TableControlHeader[]
  ) => {
    const controlColumns = control.map((control) => {
      return {
        name: control.name,
        title: control.title,
        width: control.width,
        css: control.css,
        cssClass: control.cssClass,
      };
    });

    return [...header, ...controlColumns];
  };
  const header = prepareHeaderBasedOnColumType(props.header, props.control);
  /**
   *  Unify data and control columns
   * @param data actual table data
   * @param control list of control columns
   * @returns
   */
  const prepareDataBasedOnColumType = (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any[],
    control: TableControlHeader[]
  ) => {
    if (control.length > 0) {
      const unifiedData = data.map((row) => {
        control.forEach((control) => {
          const controlRowContent = renderControlButton(control, row, props);
          row[`${control.name}`] = controlRowContent;
        });

        return {
          ...row,
        };
      });

      return arrangeProperties(
        unifiedData,
        header.map((e) => e.name)
      );
    }

    return data;
  };
  const [data, setData] = createSignal(
    prepareDataBasedOnColumType(props.data(), props.control)
  );
  const [dataSplitted, setDataSplitted] = createSignal(
    arraySplitter(data(), props.pageSize || PAGE_SIZE_DEFAULT)
  );
  const [pageData, setPageData] = createSignal(dataSplitted()[0]);
  const sortData = (property: string) => {
    setSorter({
      property: property,
      isDescending: !sorter().isDescending,
    });
  };
  const prepareTableData = () => {
    setCurrentPage(0);
    const arrayFiltered = arrayFilter(data(), searchKeyword());
    const arraySorted = arraySorter(
      arrayFiltered,
      sorter().property,
      sorter().isDescending
    );

    return arraySorted;
  };
  /**
   * Effect that listens to changes in pagination
   */
  createEffect(() => {
    setData(prepareDataBasedOnColumType(props.data(), props.control));
  });
  /**
   * Effect that listens to changes in pagination
   */
  createEffect(() => {
    setPageData(dataSplitted()[currentPage()]);
  });
  /**
   * Effect that listens to changes in sorting behavior
   */
  createEffect(() => {
    const tableDataPrepared = prepareTableData();
    setDataSplitted(
      arraySplitter(tableDataPrepared, props.pageSize || PAGE_SIZE_DEFAULT)
    );
  });
  /**
   * Effect that listens to searchbar
   */
  createEffect(() => {
    const tableDataPrepared = prepareTableData();
    setDataSplitted(
      arraySplitter(tableDataPrepared, props.pageSize || PAGE_SIZE_DEFAULT)
    );
  });

  return (
    <>
      <div class="card-flush w-100">
        {(props.isSearchEnabled || props.isExportEnabled) && (
          <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <div class="card-title">
              <div class="d-flex align-items-center position-relative gap-5"></div>
            </div>
            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
              {props.isSearchEnabled && (
                <input
                  type="text"
                  value={searchKeyword()}
                  onInput={(e) => {
                    setSearchKeyword(
                      (e.target as HTMLInputElement).value || ''
                    );
                  }}
                  data-kt-filter="search"
                  class="form-control form-control-sm form-control-solid w-250px ps-14"
                  placeholder="Buscar..."
                />
              )}
              {props.isExportEnabled && (
                <button
                  type="button"
                  role="button"
                  class="btn btn-sm btn-secondary"
                  data-kt-menu-trigger="click"
                  data-kt-menu-placement="bottom-end"
                  onClick={() => {
                    const tableDataPrepared = prepareTableData();
                    exportToExcel(tableDataPrepared, props.title);
                  }}
                >
                  Exportar
                </button>
              )}
            </div>
          </div>
        )}

        {props.data().length === 0 && (
          <Card>
            <Card.Body>
              <Row>
                <EmptyTableIcon
                  class="m-auto mt-5"
                  style="height:200px"
                ></EmptyTableIcon>
                <label class="m-auto mt-8 text-center">
                  No se encontraron datos
                </label>
              </Row>
            </Card.Body>
          </Card>
        )}
        {props.data().length > 0 && (
          <>
            <div class={`card-body ${props.cssClass}`}>
              <div class="table-responsive table-loading">
                {/* <div class="table-loading-message">Loading...</div> */}
                <table
                  class={`table ${props.isTableBordered && 'border'} ${
                    props.isTableStriped && 'table-striped'
                  } gy-7 gs-7`}
                >
                  <thead>
                    <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                      <Index each={header}>
                        {(header) => {
                          const { name, title, width, css, cssClass } =
                            header();
                          const minColumWidth =
                            width && `min-width: ${width}px;`;

                          return (
                            <th
                              role="button"
                              class={`custom-table-header text-capitalize ${
                                sorter().property === name
                                  ? sorter().isDescending
                                    ? 'table-sort-desc'
                                    : 'table-sort-asc'
                                  : ''
                              } ${cssClass}`}
                              style={`${minColumWidth} ${css} `}
                              onclick={() => {
                                sortData(name);
                              }}
                            >
                              {title}
                            </th>
                          );
                        }}
                      </Index>
                    </tr>
                  </thead>
                  <tbody>
                    <Index each={pageData()}>
                      {(row) => (
                        <tr>
                          {Object.entries(row())
                            .filter((data) =>
                              header.find((e) => e.name === data[0])
                            )
                            .map((data) => (
                              <td>{data[1] as string}</td>
                            ))}
                        </tr>
                      )}
                    </Index>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="card-footer text-right">
              <div class="row">
                <div class="col-12 d-flex align-items-center justify-content-center justify-content-md-end">
                  <div class="dataTables_paginate paging_simple_numbers">
                    {dataSplitted().length > 1 && (
                      <Pagination>
                        <For each={dataSplitted()}>
                          {(_row, i) => (
                            <Pagination.Item
                              class={`btn ${
                                currentPage() === i()
                                  ? 'btn-bg-primary btn-sm btn-color-white'
                                  : 'btn-bg-light btn-color-gray-400'
                              } px-3 py-1 mx-1`}
                              active={currentPage() === i()}
                              onClick={() => {
                                setCurrentPage(i());
                              }}
                            >
                              {i() + 1}
                            </Pagination.Item>
                          )}
                        </For>
                      </Pagination>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
}
export default BasicTableComponent;
