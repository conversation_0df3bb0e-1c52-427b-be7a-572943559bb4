import { useScreenWidth } from '../../hooks/use-screen-width';
import gifImage from '../../../assets/media/icons/Refresh animated.gif';
import { createEffect, createSignal } from 'solid-js';

const Animation = ({
  height = '400px',
  width = '400px',
}: {
  height?: string;
  width?: string;
}) => {
  const screenWidth = useScreenWidth();
  const [animationHeight, setAnimationHeight] = createSignal<string>(height);
  const [animationWidth, setAnimationWidth] = createSignal<string>(height);
  createEffect(() => {
    if (screenWidth()) {
      setAnimationHeight(screenWidth() > 900 ? height : '200px');
      setAnimationWidth(screenWidth() > 900 ? width : '200px');
    }
  });

  return (
    <div class="text-center m-auto">
      <img
        src={gifImage}
        alt="Moving GIF"
        style={{ height: animationHeight(), width: animationWidth() }}
      />
    </div>
  );
};
export default Animation;
