import { Accessor } from 'solid-js';

export type ErrorAlertProps = {
  error: Accessor<string | null> | Accessor<string | null>[];
};
export const ErrorAlert = (props: ErrorAlertProps) => {
  const renderErrorMessage = () => {
    if (Array.isArray(props.error)) {
      return props.error
        .filter((error) => error())
        .map((error) => {
          return (
            <div class="alert alert-danger" role="alert">
              {error()}
            </div>
          );
        });
    } else {
      return (
        props.error() && (
          <div class="alert alert-danger" role="alert">
            {props.error()}
          </div>
        )
      );
    }
  };

  return <>{renderErrorMessage()}</>;
};
