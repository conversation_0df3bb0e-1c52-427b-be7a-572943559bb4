import Animation from '../lottie-player/animation';
import { Accessor, Show, createEffect, createSignal } from 'solid-js';

export type LoadingAlertProps = {
  loadingFlags: Accessor<boolean> | Accessor<boolean>[];
  showLoadingAnimation?: boolean;
  class?: string;
};
export const LoadingAlert = (props: LoadingAlertProps) => {
  const [alert, setAlert] = createSignal(false);
  createEffect(() => {
    setAlert(isSystemFetchingData(props.loadingFlags));
  });

  return (
    <>
      <Show when={alert()}>
        <div class={props.class}>
          <Show when={props.showLoadingAnimation === true}>
            <div class="card-body card-toolbar">
              <Animation />
            </div>
          </Show>
          <Show when={!props.showLoadingAnimation}>
            <div class="alert alert-info" role="alert">
              Cargando...
            </div>
          </Show>
        </div>
      </Show>
    </>
  );
};
export const isSystemFetchingData = (
  loadingFlags: Accessor<boolean> | Accessor<boolean>[]
) => {
  if (Array.isArray(loadingFlags)) {
    return loadingFlags.some((flag) => flag());
  } else {
    return loadingFlags();
  }
};
