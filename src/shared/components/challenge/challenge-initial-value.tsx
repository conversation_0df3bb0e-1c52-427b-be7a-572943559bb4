import {
  ChallengeListElement,
  PreConfiguredChallengeOption,
} from '../../../apps/challenges/types';
import {
  ChallengesParticipanTypes,
  ChallengesProgressType,
} from 'entities/Challenge';
import { Col, Form, FormGroup, Row } from 'solid-bootstrap';
import { Accessor } from 'solid-js';

export const ChallengeInitialValue = (props: {
  challengeData: ChallengeListElement | undefined;
  challengeParticipantType: Accessor<ChallengesParticipanTypes>;
  challengeProgressType: Accessor<ChallengesProgressType>;
  challengeOptions: PreConfiguredChallengeOption;
}) => {
  return (
    <>
      {props.challengeParticipantType() === 'INDIVIDUAL' &&
        props.challengeProgressType() === 'FINAL_RESULT' && (
          <Row>
            <Form.Label for="initValue">
              ¿Con cuánto inicias tu meta?
            </Form.Label>
            <Col sm={6} md={6} lg={4}>
              <FormGroup>
                <Form.Control
                  type="number"
                  id="initialValue"
                  name="initialValue"
                  value={props.challengeData?.initialValue}
                  class="form-control"
                  required
                />
              </FormGroup>
            </Col>
            {props.challengeOptions.id !== 'OTHER' && (
              <Col class="d-flex" sm={6} md={6} lg={4}>
                <div class="d-flex">
                  <p class="my-auto">
                    {props.challengeOptions.UNIT?.EXTENDED_NAME}
                  </p>
                </div>
              </Col>
            )}
          </Row>
        )}
    </>
  );
};
