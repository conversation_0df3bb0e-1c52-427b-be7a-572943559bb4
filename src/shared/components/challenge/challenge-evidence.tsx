import { Form } from 'solid-bootstrap';
import { Accessor } from 'solid-js';

export const ChallengeEvidence = (props: {
  isEvidenceRequired: Accessor<boolean>;
  handleIsEvidenceRequired: (event: Event) => void;
}) => {
  return (
    <>
      <Form.Group>
        <Form.Label class="mb-5">
          ¿Te gustaría que la evidencia sea obligatoria a la hora de subir el
          progreso de la meta?
        </Form.Label>
        <div>
          <Form.Check
            type="radio"
            name="ChallengeEvidenceTrue"
            value="Si"
            label="Sí"
            checked={props.isEvidenceRequired()}
            onChange={props.handleIsEvidenceRequired}
          />
          <Form.Check
            type="radio"
            name="ChallengeEvidenceFalse"
            value="No"
            label="No"
            checked={props.isEvidenceRequired() === false}
            onChange={props.handleIsEvidenceRequired}
          />
        </div>
      </Form.Group>
    </>
  );
};
