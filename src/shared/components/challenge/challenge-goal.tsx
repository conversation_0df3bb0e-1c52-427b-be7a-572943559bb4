import {
  ChallengeListElement,
  PreConfiguredChallengeOption,
} from '../../../apps/challenges/types';
import { useScreenWidth } from '../../hooks/use-screen-width';
import { Col, Form, FormGroup, Row } from 'solid-bootstrap';
import { ChallengesProgressType } from 'entities/Challenge';
import { Accessor, For } from 'solid-js';
import { GOAL_UNIT_OPTIONS } from '../../libraries/challenge-library';

export const ChallengeGoal = (props: {
  challengeData: ChallengeListElement | undefined;
  challengeProgressType: Accessor<ChallengesProgressType>;
  challengeOptions: PreConfiguredChallengeOption;
}) => {
  const screenWidth = useScreenWidth();

  return (
    <>
      <Row>
        <Form.Label for="date">¿Cuál es tu meta?</Form.Label>
        {props.challengeProgressType() === 'HABIT' && (
          <p class="progress-type-description mt-1">
            Numero de unidades a completar de diariamente
          </p>
        )}
        <Row>
          <Col sm={12} md={5} lg={5}>
            <Form.Group controlId="progress">
              <Form.Select
                id="goalType"
                name="goalType"
                value={props.challengeData?.goalType}
                class="form-control"
                required
              >
                {props.challengeOptions[props.challengeProgressType()].ADD && (
                  <option value="ADD">
                    {props.challengeOptions[props.challengeProgressType()].ADD}
                  </option>
                )}
                {props.challengeOptions[props.challengeProgressType()]
                  .REDUCE && (
                  <option value="REDUCE">
                    {
                      props.challengeOptions[props.challengeProgressType()]
                        .REDUCE
                    }
                  </option>
                )}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col sm={12} md={3} lg={3}>
            <FormGroup class={`${screenWidth() <= 600 && 'mt-3'} `}>
              <Form.Control
                class="form-control"
                type="number"
                id="goal"
                name="goal"
                value={props.challengeData?.goal}
                required
              />
            </FormGroup>
          </Col>
          <Col sm={12} md={3} lg={3}>
            {props.challengeOptions.id !== 'OTHER' && (
              <>
                <input
                  class="form-control form-control-transparent px-0 mx-0"
                  disabled
                  value={
                    props.challengeData?.goalUnit ||
                    props.challengeOptions.UNIT.EXTENDED_NAME
                  }
                ></input>
                <input
                  style={{ display: 'none' }}
                  id="goalUnit"
                  name="goalUnit"
                  class="form-control form-control-transparent"
                  disabled
                  value={
                    props.challengeData?.goalUnit ||
                    props.challengeOptions.UNIT.id
                  }
                ></input>
              </>
            )}
            {props.challengeOptions.id === 'OTHER' && (
              <Form.Group controlId="progress">
                <Form.Select
                  id="goalUnit"
                  name="goalUnit"
                  value={
                    props.challengeData?.goalUnit ||
                    props.challengeOptions.UNIT.id
                  }
                  class="form-control"
                >
                  <For each={GOAL_UNIT_OPTIONS}>
                    {(unit) => (
                      <option value={unit.id}>{unit.extendedName}</option>
                    )}
                  </For>
                </Form.Select>
              </Form.Group>
            )}
          </Col>
        </Row>
      </Row>
    </>
  );
};
