/* eslint-disable @typescript-eslint/naming-convention */
import { Accessor, For, Show, createMemo } from 'solid-js';
import { DateServices } from '../../services/utils/date-services';
import { ChallengeListElement } from '../../../apps/challenges/types';
import CustomCheckBoxDetail from '../custom-checkbox/custom-checkbox-detail';
import { useUserHabitChallengeProgress } from '../../../apps/challenges/hooks/use-user-habit-challenge-progress';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { LoadingAlert } from '../alert/loading-alert';

export const ChallengeHabitProgressDetail = (props: {
  progressData: ChallengeProgress[];
  isLoading: Accessor<boolean>;
  challenge: ChallengeListElement;
}) => {
  const dates = DateServices.getDatesBetween(
    props.challenge.startDate,
    props.challenge.endDate
  );
  const { isLoading, userHabitChallengeProgress } =
    useUserHabitChallengeProgress(props.progressData, dates);
  const isProgressDoneLoading = createMemo(
    () => !props.isLoading() && !isLoading()
  );

  return (
    <>
      <div
        class="habit-challenge-progress w-100 d-flex pb-5 mb-10"
        style={{ 'overflow-x': 'auto' }}
      >
        <LoadingAlert
          class="w-100 my-5 py-5"
          loadingFlags={[props.isLoading]}
        />
        <Show when={isProgressDoneLoading()}>
          <For each={userHabitChallengeProgress}>
            {(userChallengeProgress) => (
              <>
                <CustomCheckBoxDetail {...{ ...userChallengeProgress }} />
              </>
            )}
          </For>
        </Show>
      </div>
    </>
  );
};
