import { ChallengeListElement } from '../../../apps/challenges/types';
import { Form, FormGroup } from 'solid-bootstrap';

export const ChallengeBasicInfo = (props: {
  challengeData: ChallengeListElement | undefined;
}) => {
  return (
    <>
      <FormGroup>
        <Form.Label for="name">Ponle un nombre a tu meta</Form.Label>
        <Form.Control
          type="text"
          id="name"
          name="name"
          value={props.challengeData?.name}
          placeholder="🏁 ¡Por 5kg menos!"
          class="form-control"
          maxlength="75"
          required
        />
      </FormGroup>
      <FormGroup>
        <Form.Label for="name">Ponle una descripción a tu meta</Form.Label>
        <Form.Control
          type="text"
          id="description"
          name="description"
          value={props.challengeData?.description}
          class="form-control"
          as="textarea"
          maxlength="200"
          rows={3}
          required
        />
      </FormGroup>
      <FormGroup>
        <Form.Label for="price">¿Cuál será tu recompensa?</Form.Label>
        <Form.Control
          type="text"
          id="reward"
          name="reward"
          value={props.challengeData?.reward}
          class="form-control"
          placeholder="🏆 ¡Fin de semana en la playa!"
          maxlength="50"
          required
        />
      </FormGroup>
    </>
  );
};
