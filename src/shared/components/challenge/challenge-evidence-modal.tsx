import { Modal } from 'solid-bootstrap';
import { <PERSON><PERSON>, Setter } from 'solid-js';
import ImageLoader from '../uploader/image-loader';

type ChallengeEvidenceModalProps = {
  evidence?: string;
  show: Accessor<boolean>;
  setShow: Setter<boolean>;
};
const ChallengeEvidenceModal = (props: ChallengeEvidenceModalProps) => {
  return (
    <Modal
      size="lg"
      show={props.show()}
      onHide={() => props.setShow(false)}
      aria-labelledby="contained-modal-title-vcenter"
      centered
    >
      <Modal.Header closeButton>
        <h1 class="card-title">Evidencia</h1>
      </Modal.Header>
      <Modal.Body>
        <div class="p-10 my-5">
          <ImageLoader class="w-100" src={props.evidence || ''} />
        </div>
      </Modal.Body>
    </Modal>
  );
};
export default ChallengeEvidenceModal;
