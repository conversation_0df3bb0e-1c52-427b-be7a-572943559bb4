import { ChallengeNotificationOptions } from 'entities/Challenge';
import { Form } from 'solid-bootstrap';
import { Accessor } from 'solid-js';

export const ChallengeRemainder = (props: {
  hasNotifications: Accessor<boolean>;
  notificationOption: Accessor<ChallengeNotificationOptions>;
  handleHasNotificationRadioChange: (event: Event) => void;
  handleRadioChange: (event: Event) => void;
}) => {
  return (
    <>
      <Form.Group>
        <Form.Label class="mb-5">
          ¿Te gustaría dar seguimiento esta meta?
        </Form.Label>
        <div>
          <Form.Check
            type="radio"
            name="Si"
            value="Si"
            label="Sí"
            checked={props.hasNotifications()}
            onChange={props.handleHasNotificationRadioChange}
          />
          <Form.Check
            type="radio"
            name="No"
            value="No"
            label="No"
            checked={props.hasNotifications() === false}
            onChange={props.handleHasNotificationRadioChange}
          />
        </div>
      </Form.Group>
      {props.hasNotifications() && (
        <Form.Group>
          <Form.Label class="mb-5">
            ¿Cómo te gustaría dar seguimiento esta meta?
          </Form.Label>
          <div>
            <Form.Check
              type="radio"
              name="DAILY"
              value="DAILY"
              label="Diario"
              checked={props.notificationOption() === 'DAILY'}
              onChange={props.handleRadioChange}
            />
            <Form.Check
              type="radio"
              name="WEEKLY"
              value="WEEKLY"
              label="Semanal"
              checked={props.notificationOption() === 'WEEKLY'}
              onChange={props.handleRadioChange}
            />
            <Form.Check
              type="radio"
              name="MONTHLY"
              value="MONTHLY"
              label="Mensual"
              checked={props.notificationOption() === 'MONTHLY'}
              onChange={props.handleRadioChange}
            />
          </div>
        </Form.Group>
      )}
    </>
  );
};
