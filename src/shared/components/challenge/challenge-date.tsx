import { ChallengeListElement } from '../../../apps/challenges/types';
import { Col, Form, FormGroup, Row } from 'solid-bootstrap';

export const ChallengeDate = (props: {
  challengeData: ChallengeListElement | undefined;
}) => {
  return (
    <>
      <Row>
        <Form.Label for="date">Rango de tiempo de la meta</Form.Label>
        <Row>
          <Col sm={12} md={6} lg={6}>
            <FormGroup>
              <Form.Label for="startDate">Del</Form.Label>
              <Form.Control
                type="date"
                id="startDate"
                name="startDate"
                value={props.challengeData?.startDate.split('T')[0]}
                class="form-control"
                required
              />
            </FormGroup>
          </Col>
          <Col sm={12} md={6} lg={6}>
            <FormGroup>
              <Form.Label for="endDate">Hasta</Form.Label>
              <Form.Control
                type="date"
                id="endDate"
                name="endDate"
                value={props.challengeData?.endDate.split('T')[0]}
                class="form-control"
                required
              />
            </FormGroup>
          </Col>
        </Row>
      </Row>
    </>
  );
};
