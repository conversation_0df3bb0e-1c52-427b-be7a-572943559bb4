/* eslint-disable @typescript-eslint/naming-convention */
import { Card, Nav, Row, Table } from 'solid-bootstrap';
import { Accessor, Index, Show, createMemo, createSignal } from 'solid-js';
import { TableControlHeader, TableHeader } from '../table/types';
import EmptyTableIcon from '../table/empty-table-icon';
import { LoadingAlert } from '../alert/loading-alert';
import { getCurrentUserData } from '../../services/user/user-session-management';

export const ChallengeProgressTable = (props: {
  header: TableHeader[];
  control: TableControlHeader[];
  data: any[];
  isHabit: boolean;
  actualProgress?: Accessor<string>;
  isLoading: Accessor<boolean>;
}) => {
  const [currentPage, setCurrentPage] = createSignal(1);
  const itemsPerPage = 5;
  const paginatedItems = createMemo(() => {
    const startIndex = (currentPage() - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    return [...props.data].slice(startIndex, endIndex);
  });
  const { id } = getCurrentUserData();
  const isTableEmpty = createMemo(() => [...props.data].length === 0);
  const ChallengeProgressHeader = () => {
    const headerTitle = props.isHabit
      ? 'Registro de Progreso 📝'
      : 'Progreso de tu meta 📈';

    return (
      <>
        <div>
          <Card.Title class="mt-3">{headerTitle}</Card.Title>
          <Show when={!props.isHabit}>
            <Card.Subtitle class="my-10">
              Progreso Actual:{' '}
              {props.actualProgress ? props.actualProgress() : '0'}
            </Card.Subtitle>
          </Show>
        </div>
      </>
    );
  };

  return (
    <>
      <Card>
        <Card.Header style={{ 'border-bottom': '0px' }}>
          <ChallengeProgressHeader />
        </Card.Header>
        <Card.Body class="px-0 pt-0">
          <LoadingAlert class="my-5 py-5" loadingFlags={[props.isLoading]} />
          <Show when={!props.isLoading()}>
            <Show when={isTableEmpty()}>
              <Row>
                <EmptyTableIcon
                  class="m-auto mt-5"
                  style="height:200px"
                ></EmptyTableIcon>
                <label class="m-auto mt-8 text-center">
                  No se encontraron datos
                </label>
              </Row>
            </Show>
            <Show when={!isTableEmpty()}>
              <div class="table-responsive">
                <Table bordered class="table gy-7 gs-7">
                  <thead>
                    <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                      <Index each={props.header}>
                        {(header) => {
                          const { title, width, css, cssClass } = header();
                          const minColumWidth =
                            width && `min-width: ${width}px;`;

                          return (
                            <th
                              role="button"
                              class={`custom-table-header text-capitalize text-truncate ${cssClass}`}
                              style={`${minColumWidth} ${css} `}
                            >
                              {title}
                            </th>
                          );
                        }}
                      </Index>
                      <Index each={props.control}>
                        {(control) => (
                          <th
                            role="button"
                            class={`custom-table-header text-capitalize text-truncate`}
                          >
                            {control().title}
                          </th>
                        )}
                      </Index>
                    </tr>
                  </thead>
                  <tbody>
                    <Index each={paginatedItems()}>
                      {(data) => (
                        <tr>
                          <Index each={props.header}>
                            {(header) => (
                              <td>{data()[header().name] as string}</td>
                            )}
                          </Index>
                          <Index each={props.control}>
                            {(control) => {
                              if (
                                !data().evidence &&
                                control().name === 'detail'
                              )
                                return <td></td>;
                              if (
                                data().userId !== id &&
                                control().name === 'eliminar'
                              )
                                return <td></td>;

                              return (
                                <td>
                                  <button
                                    type="button"
                                    role="button"
                                    class={`btn btn-${
                                      control().controlType
                                    } py-1 m-auto text-center`}
                                    onClick={() => control().callback(data())}
                                  >
                                    {control().icon ? (
                                      <i class={control().icon + ' p-0'}></i>
                                    ) : (
                                      control().title
                                    )}
                                  </button>
                                </td>
                              );
                            }}
                          </Index>
                        </tr>
                      )}
                    </Index>
                  </tbody>
                </Table>
              </div>
            </Show>
          </Show>
        </Card.Body>
        <Show when={[...props.data].length > itemsPerPage}>
          <Card.Footer class="text-right">
            <Row>
              <div class="col-12 d-flex align-items-center justify-content-center justify-content-md-end">
                <Nav>
                  <ul class="pagination">
                    {Array(Math.ceil(props.data.length / itemsPerPage))
                      .fill(null)
                      .map((_, index) => (
                        <li
                          class={`page-item ${
                            currentPage() === index + 1 ? 'active' : ''
                          }`}
                        >
                          <a
                            class="page-link"
                            style={{ cursor: 'pointer' }}
                            onClick={() => setCurrentPage(index + 1)}
                          >
                            {index + 1}
                          </a>
                        </li>
                      ))}
                  </ul>
                </Nav>
              </div>
            </Row>
          </Card.Footer>
        </Show>
      </Card>
    </>
  );
};
