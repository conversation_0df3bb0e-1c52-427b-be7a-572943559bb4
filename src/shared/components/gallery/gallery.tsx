import { Accessor, createSignal, onCleanup } from 'solid-js';

export interface IGalleryProps {
  images: Accessor<string[]>;
}
// eslint-disable-next-line @typescript-eslint/naming-convention
const responsiveImageStyle = { 'max-width': '-webkit-fill-available' };
function Gallery({ images }: IGalleryProps) {
  const [currentIndex, setCurrentIndex] = createSignal(0);
  // Cleanup when the component is unmounted
  onCleanup(() => setCurrentIndex(0));
  function selectImage(index: number) {
    setCurrentIndex(index);
  }

  return (
    <div class="relative flex flex-col items-center">
      <div class="text-center image-container w-full bg-gray-300 relative flex flex-col items-center rounded p-2">
        <img
          src={images()[currentIndex()]}
          alt={`Image ${currentIndex() + 1}`}
          class="rounded gallery-image"
          style={responsiveImageStyle}
        />
      </div>
      <div class="text-center thumbnail-list flex space-x-2 overflow-x-auto mt-4 cursor-pointer">
        {images().map((image, index) => (
          <img
            src={image}
            alt={`Thumbnail ${index + 1}`}
            onClick={() => selectImage(index)}
            classList={{ selected: index === currentIndex() }}
            style={{ height: '80px' }}
            class="rounded"
          />
        ))}
      </div>
    </div>
  );
}
export default Gallery;
