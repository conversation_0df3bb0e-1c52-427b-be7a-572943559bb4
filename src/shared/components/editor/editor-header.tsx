import { Editor } from '@tiptap/core';

type EditorHeaderProps = {
  editor: Editor;
};
const EditorHeader = (props: EditorHeaderProps) => {
  return (
    <>
      {props.editor && (
        <div class="card-toolbar gap-2 gap-md-5">
          <button
            onClick={() => props.editor.chain().focus().toggleBold().run()}
            disabled={!props.editor.can().chain().focus().toggleBold().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('bold') ? 'is-active' : ''
            }`}
            type="button"
          >
            bold
          </button>
          <button
            onClick={() => props.editor.chain().focus().toggleItalic().run()}
            disabled={!props.editor.can().chain().focus().toggleItalic().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('italic') ? 'is-active' : ''
            }`}
            type="button"
          >
            italic
          </button>
          <button
            onClick={() => props.editor.chain().focus().toggleStrike().run()}
            disabled={!props.editor.can().chain().focus().toggleStrike().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('strike') ? 'is-active' : ''
            }`}
            type="button"
          >
            strike
          </button>
          <button
            onClick={() => props.editor.chain().focus().toggleCode().run()}
            disabled={!props.editor.can().chain().focus().toggleCode().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('code') ? 'is-active' : ''
            }`}
            type="button"
          >
            code
          </button>
          <button
            onClick={() => props.editor.chain().focus().unsetAllMarks().run()}
            class={`btn btn-light-info mx-2 py-2 my-3`}
            type="button"
          >
            clear marks
          </button>
          <button
            onClick={() => props.editor.chain().focus().clearNodes().run()}
            class={`btn btn-light-info mx-2 py-2 my-3`}
            type="button"
          >
            clear nodes
          </button>
          <button
            onClick={() => props.editor.chain().focus().setParagraph().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('paragraph') ? 'is-active' : ''
            }`}
            type="button"
          >
            paragraph
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 1 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('heading', { level: 1 }) ? 'is-active' : ''
            }`}
            type="button"
          >
            h1
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 2 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3`}
          >
            h2
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 3 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('heading', { level: 3 }) ? 'is-active' : ''
            }`}
            type="button"
          >
            h3
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 4 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('heading', { level: 4 }) ? 'is-active' : ''
            }`}
            type="button"
          >
            h4
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 5 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('heading', { level: 5 }) ? 'is-active' : ''
            }`}
          >
            h5
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleHeading({ level: 6 }).run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('heading', { level: 6 }) ? 'is-active' : ''
            }`}
            type="button"
          >
            h6
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleBulletList().run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('bulletList') ? 'is-active' : ''
            }`}
            type="button"
          >
            bullet list
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleOrderedList().run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('orderedList') ? 'is-active' : ''
            }`}
            type="button"
          >
            ordered list
          </button>
          <button
            onClick={() => props.editor.chain().focus().toggleCodeBlock().run()}
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('codeBlock') == true ? 'is-active' : ''
            }`}
            type="button"
          >
            code block
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().toggleBlockquote().run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3 ${
              props.editor.isActive('blockquote') ? 'is-active' : ''
            }`}
            type="button"
          >
            blockquote
          </button>
          <button
            onClick={() =>
              props.editor.chain().focus().setHorizontalRule().run()
            }
            class={`btn btn-light-info mx-2 py-2 my-3`}
          >
            horizontal rule
          </button>
          <button
            onClick={() => props.editor.chain().focus().setHardBreak().run()}
            class={`btn btn-light-info mx-2 py-2 my-3`}
            type="button"
          >
            hard break
          </button>
        </div>
      )}
    </>
  );
};
export default EditorHeader;
