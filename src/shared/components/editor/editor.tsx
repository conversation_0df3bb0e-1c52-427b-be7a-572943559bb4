import StarterKit from '@tiptap/starter-kit';
import EditorHeader from './editor-header';
import { Setter, createSignal, Show } from 'solid-js';
import { createTiptapEditor } from 'solid-tiptap';
import Link from '@tiptap/extension-link';

type CustomEditorProps = {
  setContent: Setter<string>;
  isEnabled: boolean;
  content: string;
};
const CustomEditor = (props: CustomEditorProps) => {
  const [container, setContainer] = createSignal<HTMLDivElement>();
  const editor = createTiptapEditor(() => ({
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    element: container()!,
    extensions: [
      StarterKit,
      Link.configure({
        linkOnPaste: true,
        autolink: true,
      }),
    ],
    editorProps: {
      attributes: {
        class: 'p-8 focus:outline-none prose max-w-full bg-gray-50 m-2 rounded',
      },
    },
    content: props.content,
    onUpdate({ editor }) {
      props.setContent(editor.getHTML());
    },
    editable: props.isEnabled,
  }));

  return (
    <>
      {props.isEnabled && (
        <Show when={editor()} keyed>
          {(instance) => <EditorHeader editor={instance} />}
        </Show>
      )}
      <div
        class="h-[40vh] bg-gray-50 overflow-y-scroll rounded-lg"
        ref={setContainer}
      />
    </>
  );
};
export default CustomEditor;
