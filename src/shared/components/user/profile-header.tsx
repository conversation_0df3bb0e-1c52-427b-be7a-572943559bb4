import { Accessor, Show } from 'solid-js';
import { useUser } from '../../../apps/profile/components/profile-context';
import userDefaultPicture from '../../../assets/media/icons/blank.png';
import { useScreenWidth } from '../../hooks/use-screen-width';
import { User } from '../../../entities/User';

const ProfileHeader = (props: { user: Accessor<User> }) => {
  const screenWidth = useScreenWidth();
  const context = useUser();
  const handleImageLoadError = (event: Event) => {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = userDefaultPicture;
  };

  return (
    <>
      <div class="card mb-5">
        <div class="card-body pt-8 pb-4">
          <div class="d-flex flex-wrap flex-sm-nowrap">
            <div class="me-7 mb-4">
              <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
                <img
                  class="object-fit-cover"
                  src={props.user().profilePicture}
                  alt="profile-picture"
                  onError={handleImageLoadError}
                />
              </div>
            </div>

            <div class="flex-grow-1">
              <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
                <div class="d-flex flex-column">
                  <div class="d-flex align-items-center mb-2">
                    <label class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">
                      {props.user().name}
                    </label>
                  </div>

                  <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
                    <a
                      href="#"
                      class="d-flex align-items-center text-gray-400 text-hover-primary mb-2"
                    >
                      <span class="svg-icon svg-icon-4 me-1">
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            opacity="0.3"
                            d="M21 19H3C2.4 19 2 18.6 2 18V6C2 5.4 2.4 5 3 5H21C21.6 5 22 5.4 22 6V18C22 18.6 21.6 19 21 19Z"
                            fill="currentColor"
                          />
                          <path
                            d="M21 5H2.99999C2.69999 5 2.49999 5.10005 2.29999 5.30005L11.2 13.3C11.7 13.7 12.4 13.7 12.8 13.3L21.7 5.30005C21.5 5.10005 21.3 5 21 5Z"
                            fill="currentColor"
                          />
                        </svg>
                      </span>
                      {props.user().email}
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div class={screenWidth() < 900 ? `w-100 text-end ` : ``}>
              <Show when={context?.view() === 'FORM'}>
                <a
                  onClick={(e) => {
                    e.preventDefault();
                    context?.setView('DETAIL');
                  }}
                  class={
                    screenWidth() < 900
                      ? `btn btn-sm btn-secondary align-self-center mb-3`
                      : `btn btn-sm btn-secondary align-self-center`
                  }
                >
                  Regresar
                </a>
              </Show>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default ProfileHeader;
