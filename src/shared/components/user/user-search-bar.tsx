import { Col, Form, Row, ListGroup } from 'solid-bootstrap';
import {
  Accessor,
  For,
  Setter,
  createEffect,
  createSignal,
  onCleanup,
} from 'solid-js';
import logo from '../../../assets/media/icons/blank.png';
import './user-search-bar.css';
import { User as UserEntity, UserCountry } from '../../../entities/User';
import { getCurrentUserData } from '../../services/user/user-session-management';

export type User = {
  id: string;
  name: string;
  email: string;
  profilePicture: string;
  country?: UserCountry;
};
type UserSearchBarProps = {
  userList: Accessor<UserEntity[]>;
  selectedUser: Accessor<User[]>;
  setSelectedUser: Setter<User[]>;
  isDisabled?: boolean;
  placeholder?: string;
  maxResults?: number;
  enableCountryFilter?: boolean;
  selectedCountries?: Accessor<UserCountry[]>;
  setSelectedCountries?: Setter<UserCountry[]>;
};
// Country labels for display
const COUNTRY_LABELS: Record<UserCountry, string> = {
  COSTA_RICA: 'Costa Rica',
  EL_SALVADOR: 'El Salvador',
  PANAMA: 'Panamá',
  GUATEMALA: 'Guatemala',
  HONDURAS: 'Honduras',
};
const UserSearchBar = (props: UserSearchBarProps) => {
  const [searchQuery, setSearchQuery] = createSignal('');
  const [usersList, setUsersList] = createSignal<User[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = createSignal(false);
  const [selectedIndex, setSelectedIndex] = createSignal(-1);
  const [showAdvancedFilters, setShowAdvancedFilters] = createSignal(false);
  const [debounceTimer, setDebounceTimer] = createSignal<NodeJS.Timeout | null>(
    null
  );
  let searchContainerRef: HTMLDivElement | undefined;
  let inputContainerRef: HTMLDivElement | undefined;
  let inputRef: HTMLInputElement | undefined;
  // Convert UserEntity to User type
  const convertUserEntityToUser = (userEntity: UserEntity): User => ({
    id: userEntity.id,
    name: userEntity.name,
    email: userEntity.email,
    profilePicture: userEntity.profilePicture,
    country: userEntity.country,
  });
  // Get filtered users based on search, selection, and country
  const getFilteredUsers = (query: string) => {
    let filteredUsers = props.userList();
    // Filter out current session user
    const currentUser = getCurrentUserData();
    if (currentUser.id) {
      filteredUsers = filteredUsers.filter(
        (user) => user.id !== currentUser.id
      );
    }
    // Filter by country if enabled and countries are selected
    if (
      props.enableCountryFilter &&
      props.selectedCountries &&
      props.selectedCountries().length > 0
    ) {
      const selectedCountries = props.selectedCountries();
      filteredUsers = filteredUsers.filter((user) =>
        selectedCountries.includes(user.country)
      );
    }
    // Filter by search query
    if (query.trim() !== '') {
      filteredUsers = filteredUsers.filter(
        (user) =>
          user.name.toLowerCase().includes(query.toLowerCase()) ||
          user.email.toLowerCase().includes(query.toLowerCase())
      );
    }
    // Filter out already selected users
    const selectedUserIds = props.selectedUser().map((user) => user.id);
    filteredUsers = filteredUsers.filter(
      (user) => !selectedUserIds.includes(user.id)
    );

    // Convert to User type and limit results
    return filteredUsers
      .map(convertUserEntityToUser)
      .slice(0, props.maxResults || 10);
  };
  // Debounced search function
  const performSearch = (query: string) => {
    if (query.trim() === '') {
      setUsersList([]);
      setIsDropdownOpen(false);

      return;
    }
    const filteredUsers = getFilteredUsers(query);
    setUsersList(filteredUsers);
    setIsDropdownOpen(true);
    setSelectedIndex(-1);
  };
  const handleInputChange = (event: Event) => {
    const value = (event.target as HTMLInputElement).value;
    setSearchQuery(value);
    // Clear existing timer
    const currentTimer = debounceTimer();
    if (currentTimer) {
      clearTimeout(currentTimer);
    }
    // Set new timer for debounced search
    const timer = setTimeout(() => performSearch(value), 300);
    setDebounceTimer(timer);
  };
  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isDropdownOpen()) return;
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex((prev) =>
          prev < usersList().length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        event.preventDefault();
        if (selectedIndex() >= 0 && selectedIndex() < usersList().length) {
          handleUserSelect(usersList()[selectedIndex()]);
        }
        break;
      case 'Escape':
        setIsDropdownOpen(false);
        setSelectedIndex(-1);
        inputRef?.blur();
        break;
    }
  };
  const handleUserSelect = (newUser: User) => {
    if (!props.selectedUser().find((user) => user.id === newUser.id)) {
      props.setSelectedUser((prev) => [...prev, newUser]);
    }
    setSearchQuery('');
    setUsersList([]);
    setIsDropdownOpen(false);
    setSelectedIndex(-1);
  };
  const handleDeleteUser = (userId: string) => {
    props.setSelectedUser((prev) => prev.filter((user) => user.id !== userId));
  };
  const handleImageLoadError = (event: Event) => {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = logo;
  };
  // Close dropdown when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (
      inputContainerRef &&
      !inputContainerRef.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
      setSelectedIndex(-1);
    }
  };
  // Update search results when filters change
  createEffect(() => {
    if (searchQuery().trim() !== '') {
      const filteredUsers = getFilteredUsers(searchQuery());
      setUsersList(filteredUsers);
    }
  });
  createEffect(() => {
    document.addEventListener('click', handleClickOutside);

    return () => document.removeEventListener('click', handleClickOutside);
  });
  onCleanup(() => {
    const currentTimer = debounceTimer();
    if (currentTimer) {
      clearTimeout(currentTimer);
    }
    document.removeEventListener('click', handleClickOutside);
  });

  return (
    <>
      <div class="user-search-wrapper" ref={searchContainerRef}>
        {props.enableCountryFilter &&
          props.selectedCountries &&
          props.setSelectedCountries && (
            <div class="user-search-advanced-filters">
              <button
                type="button"
                class="user-search-advanced-toggle"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters())}
                aria-expanded={showAdvancedFilters()}
                aria-controls="advanced-filters-content"
              >
                <i
                  class={`bi ${
                    showAdvancedFilters() ? 'bi-chevron-up' : 'bi-chevron-down'
                  }`}
                ></i>
                Filtros Avanzados
              </button>

              {showAdvancedFilters() && (
                <div
                  id="advanced-filters-content"
                  class="user-search-advanced-content"
                >
                  <div class="user-search-country-filter mt-5">
                    <Form.Label class="user-search-country-label mb-5">
                      Filtrar por países:
                    </Form.Label>
                    <div class="user-search-country-checkboxes">
                      <For each={Object.entries(COUNTRY_LABELS)}>
                        {([countryCode, countryLabel]) => (
                          <Form.Check
                            type="checkbox"
                            id={`country-${countryCode}`}
                            label={countryLabel}
                            checked={
                              props
                                .selectedCountries?.()
                                .includes(countryCode as UserCountry) || false
                            }
                            onChange={(e) => {
                              const isChecked = (e.target as HTMLInputElement)
                                .checked;
                              const country = countryCode as UserCountry;
                              const setCountries = props.setSelectedCountries;
                              if (setCountries) {
                                if (isChecked) {
                                  setCountries((prev) => [...prev, country]);
                                } else {
                                  setCountries((prev) =>
                                    prev.filter((c) => c !== country)
                                  );
                                }
                              }
                            }}
                            class="user-search-country-checkbox"
                          />
                        )}
                      </For>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        <div class="user-search-input-container" ref={inputContainerRef}>
          <Form.Control
            ref={inputRef}
            type="text"
            class="user-search-input"
            placeholder={props.placeholder || 'Buscar por nombre o email'}
            value={searchQuery()}
            onInput={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={props.isDisabled || false}
            autocomplete="off"
            role="combobox"
            aria-expanded={isDropdownOpen()}
            aria-haspopup="listbox"
            aria-autocomplete="list"
          />

          {isDropdownOpen() && (
            <div class="user-search-dropdown">
              <ListGroup class="user-search-list" role="listbox">
                {usersList().length === 0 ? (
                  <ListGroup.Item
                    disabled
                    class="user-search-no-results"
                    role="option"
                  >
                    No se encontraron resultados
                  </ListGroup.Item>
                ) : (
                  <For each={usersList()}>
                    {(user, index) => (
                      <ListGroup.Item
                        action
                        class={`user-search-item ${
                          selectedIndex() === index()
                            ? 'user-search-item-selected'
                            : ''
                        }`}
                        onClick={() => handleUserSelect(user)}
                        role="option"
                        aria-selected={selectedIndex() === index()}
                      >
                        <div class="user-search-item-content">
                          <img
                            src={user.profilePicture}
                            alt={`${user.name} profile`}
                            class="user-search-avatar"
                            onError={handleImageLoadError}
                          />
                          <div class="user-search-info">
                            <div class="user-search-name">{user.name}</div>
                            <div class="user-search-email">{user.email}</div>
                          </div>
                        </div>
                      </ListGroup.Item>
                    )}
                  </For>
                )}
              </ListGroup>
            </div>
          )}
        </div>

        {props.selectedUser().length > 0 && (
          <div class="user-search-selected-section">
            <div class="user-search-selected-header">
              <p class="user-search-selected-title">Usuarios Seleccionados:</p>
            </div>
            <div class="user-search-selected-list">
              <For each={props.selectedUser()}>
                {(user) => (
                  <div class="user-search-selected-item">
                    <Row class="align-items-center">
                      <Col>
                        <div class="user-search-selected-user">
                          <img
                            src={user.profilePicture}
                            alt={`${user.name} profile`}
                            class="user-search-selected-avatar"
                            onError={handleImageLoadError}
                          />
                          <div class="user-search-selected-info">
                            <div class="user-search-selected-name">
                              {user.name}
                            </div>
                            <div class="user-search-selected-email">
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </Col>
                      <Col xs="auto">
                        <button
                          type="button"
                          class="user-search-remove-btn"
                          onClick={() => handleDeleteUser(user.id)}
                          aria-label={`Remover ${user.name}`}
                          title={`Remover ${user.name}`}
                        >
                          <i class="bi bi-x-lg"></i>
                        </button>
                      </Col>
                    </Row>
                  </div>
                )}
              </For>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
export default UserSearchBar;
