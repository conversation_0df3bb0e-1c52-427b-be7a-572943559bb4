/* User Search Bar Styles */
.user-search-wrapper {
  width: 100%;
  margin-top: 16px;
}

/* Advanced Filters Styles */
.user-search-advanced-filters {
  margin-bottom: 16px;
}

.user-search-advanced-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-search-advanced-toggle:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.user-search-advanced-toggle:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

.user-search-advanced-toggle i {
  font-size: 12px;
  transition: transform 0.2s ease;
}


.user-search-advanced-content {
  margin-top: 12px;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Country Filter Styles */
.user-search-country-filter {
  margin: 0;
}

.user-search-country-label {
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  margin-bottom: 16px;
  display: block;
}

.user-search-country-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
}

.user-search-country-checkbox {
  margin-bottom: 2px !important;
  margin-top: 2px !important;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-search-country-checkbox:hover {
  background-color: #f8f9fa;
}

.user-search-country-checkbox .form-check-input {
  margin-top: 0.125rem;
}

.user-search-country-checkbox .form-check-label {
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  font-weight: 500;
}

.user-search-clear-filters {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.user-search-clear-filters:hover {
  background-color: #c82333;
}

.user-search-clear-filters:focus {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

.user-search-clear-filters i {
  font-size: 12px;
}

.user-search-input-container {
  position: relative;
  width: 100%;
}

.user-search-input {
  width: 100%;
  border: 1px solid #D9D9D9;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.user-search-input:focus {
  border-color: #0077F5;
  box-shadow: 0 0 0 3px rgba(0, 119, 245, 0.1);
  outline: none;
}

.user-search-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* Floating Dropdown Overlay */
.user-search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  margin-top: 4px;
  background: white;
  border: 1px solid #D9D9D9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 300px;
  overflow-y: auto;
  animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-search-list {
  border: none;
  margin: 0;
}

.user-search-item {
  border: none;
  border-bottom: 1px solid #f1f3f4;
  padding: 0;
  transition: background-color 0.2s ease;
}

.user-search-item:last-child {
  border-bottom: none;
}

.user-search-item:hover,
.user-search-item-selected {
  background-color: #f8f9fa;
}

.user-search-item-selected {
  background-color: #e3f2fd !important;
}

.user-search-item-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.user-search-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
  border: 2px solid #f1f3f4;
}

.user-search-info {
  flex: 1;
  min-width: 0;
}

.user-search-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-search-email {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-search-no-results {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
  border: none;
}

/* Selected Users Section */
.user-search-selected-section {
  margin-top: 20px;
}

.user-search-selected-header {
  border-bottom: 1px solid #D9D9D9;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

.user-search-selected-title {
  color: #716E6E;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.user-search-selected-list {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

.user-search-selected-item {
  padding: 12px 0;
  border-bottom: 1px solid #f1f3f4;
}

.user-search-selected-item:last-child {
  border-bottom: none;
}

.user-search-selected-item .row {
  margin: 0;
  align-items: center;
  flex-wrap: nowrap;
  /* Prevent wrapping to new line */
}

.user-search-selected-item .col {
  padding: 0;
  min-width: 0;
  /* Allow flex shrinking */
  flex: 1;
}

.user-search-selected-item .col[class*="xs-auto"] {
  flex: 0 0 auto;
  padding-left: 12px;
}

.user-search-selected-user {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
  /* Allow flex shrinking */
}

.user-search-selected-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
  border: 2px solid #f1f3f4;
}

.user-search-selected-info {
  flex: 1;
  min-width: 0;
  /* Critical for text truncation */
  overflow: hidden;
  /* Ensure container doesn't expand */
}

.user-search-selected-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-search-selected-email {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-search-remove-btn {
  background: none;
  border: 1px solid #dc3545;
  color: #dc3545;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  min-width: 32px;
  /* Prevent shrinking */
  min-height: 32px;
  /* Prevent shrinking */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  flex-shrink: 0;
  /* Prevent button from shrinking */
}

.user-search-remove-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: scale(1.05);
}

.user-search-remove-btn:focus {
  outline: 2px solid #dc3545;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-search-advanced-toggle {
    padding: 10px 12px;
    font-size: 13px;
  }

  .user-search-advanced-content {
    padding: 12px;
  }

  .user-search-country-checkboxes {
    gap: 10px;
  }

  .user-search-country-checkbox .form-check-label {
    font-size: 13px;
  }

  .user-search-clear-filters {
    padding: 6px 10px;
    font-size: 12px;
  }

  .user-search-dropdown {
    max-height: 250px;
  }

  .user-search-selected-list {
    max-height: 200px;
  }

  .user-search-item-content {
    padding: 10px 12px;
  }

  .user-search-avatar,
  .user-search-selected-avatar {
    width: 28px;
    height: 28px;
  }

  .user-search-remove-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    font-size: 10px;
  }

  .user-search-selected-item .col[class*="xs-auto"] {
    padding-left: 8px;
  }

  .user-search-selected-user {
    gap: 8px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .user-search-dropdown {
    animation: none;
  }

  .user-search-remove-btn {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .user-search-input {
    border-width: 2px;
  }

  .user-search-dropdown {
    border-width: 2px;
  }

  .user-search-item:hover,
  .user-search-item-selected {
    background-color: #000;
    color: #fff;
  }
}