import { <PERSON><PERSON>, <PERSON>, Col, ListGroup, Row } from 'solid-bootstrap';
import { Accessor, For } from 'solid-js';
import { SetStoreFunction } from 'solid-js/store';
import './style.css';

export interface IColumn {
  id: string;
  name: string;
}
interface IColumnListProps {
  options: IColumn[];
  setOptions: SetStoreFunction<IColumn[]>;
  selected: IColumn[];
  setSelected: SetStoreFunction<IColumn[]>;
  isEnabled: Accessor<boolean>;
}
const ColumnList = (props: IColumnListProps) => {
  const handleAddOption = (option: IColumn, e: Event) => {
    e.preventDefault();
    if (!props.isEnabled()) return;
    props.setSelected(
      [...props.selected, option].sort((a, b) => a.name.localeCompare(b.name))
    );
    props.setOptions(
      props.options
        .filter((o) => o.id !== option.id)
        .sort((a, b) => a.name.localeCompare(b.name))
    );
  };
  const handleRemoveOption = (option: IColumn, e: Event) => {
    e.preventDefault();
    if (!props.isEnabled()) return;
    props.setSelected(
      props.selected
        .filter((o) => o.id !== option.id)
        .sort((a, b) => a.name.localeCompare(b.name))
    );
    props.setOptions(
      [...props.options, option].sort((a, b) => a.name.localeCompare(b.name))
    );
  };
  const handleAddAllOptions = () => {
    props.setSelected(
      [...props.selected, ...props.options].sort((a, b) =>
        a.name.localeCompare(b.name)
      )
    );
    props.setOptions([]); // Sorting is not needed here since the array is empty
  };
  const handleRemoveAllOptions = () => {
    props.setOptions(
      [...props.options, ...props.selected].sort((a, b) =>
        a.name.localeCompare(b.name)
      )
    );
    props.setSelected([]); // Sorting is not needed here since the array is empty
  };

  return (
    <Card class="px-5 py-10 my-5 mt-0">
      <Row>
        <Col xs={12} md={4} lg={5} xl={5}>
          <h1 class="cl-h1">Opciones</h1>
          <div class="cl-list-container">
            <ListGroup>
              <For each={props.options}>
                {(option) => (
                  <ListGroup.Item
                    class={`text-truncate cl-option${
                      !props.isEnabled() ? '-disabled' : ''
                    }`}
                    action
                    onClick={(e: Event) => handleAddOption(option, e)}
                  >
                    {option.name}
                  </ListGroup.Item>
                )}
              </For>
            </ListGroup>
          </div>
        </Col>
        <Col xs={12} md={4} lg={2} xl={2}>
          <div class="mt-20">
            <div class="text-center m-auto my-4">
              <Button
                class="cl-button btn btn-sm btn-secondary"
                onClick={handleAddAllOptions}
                disabled={!props.isEnabled()}
              >
                Add All
              </Button>
            </div>
            <div class="text-center m-auto my-4">
              <Button
                class="cl-button btn btn-sm btn-secondary"
                onClick={handleRemoveAllOptions}
                disabled={!props.isEnabled()}
              >
                Remove All
              </Button>
            </div>
          </div>
        </Col>
        <Col xs={12} md={4} lg={5} xl={5}>
          <h1 class="cl-h1">Selección</h1>
          <div class="cl-list-container">
            <ListGroup>
              <For each={props.selected}>
                {(option) => (
                  <ListGroup.Item
                    class={`text-truncate cl-selected${
                      !props.isEnabled() ? '-disabled' : ''
                    }`}
                    action
                    onClick={(e: Event) => handleRemoveOption(option, e)}
                  >
                    {option.name}
                  </ListGroup.Item>
                )}
              </For>
            </ListGroup>
          </div>
        </Col>
      </Row>
    </Card>
  );
};
export default ColumnList;
