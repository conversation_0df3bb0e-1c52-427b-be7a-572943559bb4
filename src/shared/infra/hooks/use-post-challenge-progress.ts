import { createSignal, onCleanup, onMount } from 'solid-js';
import { ChallengeAPI } from '../api/challenge';
import { ChallengeProgressEntry } from 'shared/components/challenge-progress-modal/challenge-progress-types';

export const usePostChallengeProgress = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const postChallengeProgress = async (
    challengeProgressEntry: ChallengeProgressEntry
  ) => {
    try {
      setIsLoading(true);
      await new ChallengeAPI().postChallengeProgress(challengeProgressEntry);
    } catch (error) {
      setError('Error guardando el progreso de la meta');
    } finally {
      setIsLoading(false);
    }
  };
  const postBatchChallengeProgress = async (
    challengeProgressEntryList: ChallengeProgressEntry[]
  ) => {
    try {
      setIsLoading(true);
      const batchUpdatePromise = challengeProgressEntryList.map(
        (challengeProgressEntry) =>
          new ChallengeAPI().postChallengeProgress(challengeProgressEntry)
      );
      const result = await Promise.allSettled(batchUpdatePromise);
      const errors = result.filter((r) => r.status === 'rejected');
      if (errors.length > 0) {
        setError(
          `Error guardando el progreso de la meta, cantidad: ${errors.length}`
        );
      }
    } catch (error) {
      setError('Error guardando el progreso de la meta');
      // eslint-disable-next-line no-console
      console.error('Error al registrar el progreso', error);
    } finally {
      setIsLoading(false);
    }
  };
  const resetStatus = () => {
    setIsLoading(false);
    setError(null);
  };
  onMount(() => resetStatus());
  onCleanup(() => resetStatus());

  return {
    isLoading,
    setIsLoading,
    error,
    resetStatus,
    postChallengeProgress,
    postBatchChallengeProgress,
  };
};
