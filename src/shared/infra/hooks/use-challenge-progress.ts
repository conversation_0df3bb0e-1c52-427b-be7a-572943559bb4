import { createSignal, onMount } from 'solid-js';
import { createStore } from 'solid-js/store';
import { ChallengeAPI } from '../api/challenge';
import { ChallengeProgress } from 'entities/ChallengeProgress';

export const useChallengeProgress = (props: { id: string }) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(true);
  const [error, setError] = createSignal<string | null>(null);
  const [challengeProgress, setChallengeProgress] = createStore<
    ChallengeProgress[]
  >([]);
  const fetchChallengeProgress = async () => {
    try {
      setIsLoading(true);
      const challengeProgress = await new ChallengeAPI().getChallengesProgress(
        props.id
      );
      setChallengeProgress(challengeProgress);
    } catch (error) {
      setError('Error obteniendo el progreso de la meta');
    } finally {
      setIsLoading(false);
    }
  };
  const deleteChallengeProgress = async (id: string) => {
    try {
      setIsLoading(true);
      await new ChallengeAPI().archiveChallengeProgress(id);
      setChallengeProgress(challengeProgress.filter((p) => p.id !== id));
    } catch (error) {
      setError('Error eliminando el progreso de la meta');
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    fetchChallengeProgress();
  });
  const refetchChallengeProgress = () => {
    fetchChallengeProgress();
  };

  return {
    isLoading,
    error,
    challengeProgress,
    fetchChallengeProgress,
    deleteChallengeProgress,
    refetchChallengeProgress,
  };
};
