import { createSignal, onMount } from 'solid-js';
import { createStore } from 'solid-js/store';
import { ChallengeAPI } from '../api/challenge';
import { Challenge } from 'entities/Challenge';

export const useChallenge = (props: { id: string }) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(true);
  const [error, setError] = createSignal<string | null>(null);
  const [challenge, setChallenge] = createStore<Challenge>({
    id: '',
    userId: '',
    name: '',
    description: '',
    reward: '',
    initialValue: 0,
    goalType: '',
    goal: 0,
    goalUnit: '',
    startDate: '',
    endDate: '',
    isRemainderActive: false,
    progressType: 'ACCUMULATIVE',
    challengeParticipantType: 'GROUP',
    timeProgress: 0,
    challengeProgress: 0,
    isEvidenceRequired: false,
    updatedAt: '',
    createdAt: '',
  });
  const fetchChallenge = async (id: string) => {
    try {
      setIsLoading(true);
      const challenge = await new ChallengeAPI().getChallengeById(id);
      setChallenge(challenge);
    } catch (error) {
      setError('Error obteniendo la meta');
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    fetchChallenge(props.id);
  });

  return {
    isLoading,
    error,
    challenge,
  };
};
