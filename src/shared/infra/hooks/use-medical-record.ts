import { createSignal, onMount } from 'solid-js';
import { MedicalRecord } from 'entities/MedicalRecord';
import { MedicalRecordAPI } from '../api/medical-record';
import { createStore } from 'solid-js/store';

export const useMedicalRecord = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [data, setData] = createStore<MedicalRecord>({
    basicInfo: {
      name: '',
      documentId: '',
      birthDate: '',
      country: '',
      age: 0,
      height: 0,
      weight: 0,
      BMI: 0,
    },
    contactInfo: {
      phone: 0,
      email: '',
    },
    labReport: {
      identification: '',
      data: [],
    },
  });
  const requestUserMedicalRecordFile = async () => {
    try {
      setIsLoading(true);
      const data = await new MedicalRecordAPI().getUserMedicalData();
      setData(data);
    } catch (error) {
      setError('Error al generar expediente médico');
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    requestUserMedicalRecordFile();
  });

  return { isLoading, error, data };
};
