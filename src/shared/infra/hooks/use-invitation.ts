import { createSignal } from 'solid-js';
import { InvitationAPI } from '../api/invitation';

export const useInvitation = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const changeInvitationStatus = async (
    id: string,
    operation: 'REJECT' | 'ACCEPT'
  ) => {
    try {
      setIsLoading(true);
      if (operation === 'ACCEPT') {
        await new InvitationAPI().acceptInvitation(id);
      }
      if (operation === 'REJECT') {
        await new InvitationAPI().rejectInvitation(id);
      }
    } catch (error) {
      setError('Error al actualizar la invitación al reto');
    } finally {
      setIsLoading(false);
    }
  };

  return { isLoading, error, changeInvitationStatus };
};
