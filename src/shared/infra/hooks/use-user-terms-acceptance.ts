import { createSignal } from 'solid-js';
import { TermsAPI } from '../api/terms';

export const useUserTermsAcceptance = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const acceptTerms = async (payload: { userId: string; version: number }) => {
    try {
      setIsLoading(true);
      const user = await new TermsAPI().acceptTerms(payload);

      return user;
    } catch (error) {
      setError('Error al aceptar los términos y condiciones');

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { isLoading, error, acceptTerms };
};
