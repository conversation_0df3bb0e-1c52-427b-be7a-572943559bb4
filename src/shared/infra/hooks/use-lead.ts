import { createSignal } from 'solid-js';
import { LeadAPI } from '../api/lead';
import {
  clientBasicInformation,
  projectBasicInformation,
} from '../../../config';
import { getCurrentUserData } from '../../services/user/user-session-management';

export const useLead = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const createLead = async () => {
    const userData = getCurrentUserData();
    const { client } = clientBasicInformation;
    const { title } = projectBasicInformation;
    try {
      setIsLoading(true);
      import.meta.env.VITE_ENV === 'prod' &&
        (await new LeadAPI().createLead({
          name: userData.name,
          email: userData.email,
          source: 'PAGE_FOOTER',
          platform: `${title}`,
          client,
          date: new Date().toISOString(),
        }));

      return true;
    } catch (error) {
      setError('Error obteniendo las notificaciones');

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    createLead,
  };
};
