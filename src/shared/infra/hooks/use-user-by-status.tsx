import { User, UserStatus } from 'entities/User';
import { createSignal, onMount } from 'solid-js';
import { createStore } from 'solid-js/store';
import { UserAPI } from '../api/user';

export const useUsersByStatus = (status: UserStatus) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [data, setData] = createStore<User[]>([]);
  const getUserByStatus = async (status: UserStatus) => {
    try {
      setIsLoading(true);
      const userListByStatus = await new UserAPI().getUsersByStatus(status);
      setData(userListByStatus);
    } catch (error) {
      setError('Error fetching users by status');
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    getUserByStatus(status);
  });

  return {
    isLoading,
    error,
    data,
  };
};
