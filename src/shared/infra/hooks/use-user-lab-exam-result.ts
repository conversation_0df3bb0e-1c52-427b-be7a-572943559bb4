import { createEffect, createSignal } from 'solid-js';
import { createStore } from 'solid-js/store';
import { UserLabExamResultAPI } from '../api/user-lab-exam-result';
import { getCurrentUserData } from '../../../shared/services/user/user-session-management';
import { UserLabExamResult } from 'entities/UserLabExamResult';
import { NewLabExam } from 'apps/health/components/lab-examn/types';

export const useUserLabExamResult = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [data, setData] = createStore<UserLabExamResult[]>([]);
  const getUserLabExamResults = async () => {
    try {
      setIsLoading(true);
      const result =
        await new UserLabExamResultAPI().getUserLabExamResultByStatus();
      setData(result);
    } catch (error) {
      setError('Error obteniendo las notificaciones');
    } finally {
      setIsLoading(false);
    }
  };
  const create = async (data: NewLabExam) => {
    try {
      setIsLoading(true);
      const { id: userId } = getCurrentUserData();
      await new UserLabExamResultAPI().createUserLabExamResult({
        ...data,
        userId,
      });
    } catch (error) {
      setError('Error creando el examen de laboratorio');
    } finally {
      setIsLoading(false);
      getUserLabExamResults();
    }
  };
  const edit = async (data: NewLabExam) => {
    try {
      setIsLoading(true);
      await new UserLabExamResultAPI().updateUserLabExamResult(data);
    } catch (error) {
      setError('Error editando el examen de laboratorio');
    } finally {
      setIsLoading(false);
      getUserLabExamResults();
    }
  };
  const archive = async (id: string) => {
    try {
      setIsLoading(true);
      await new UserLabExamResultAPI().archiveUserLabExamResult(id);
    } catch (error) {
      setError('eliminando');
    } finally {
      setIsLoading(false);
      getUserLabExamResults();
    }
  };
  createEffect(() => {
    getUserLabExamResults();
  });
  createEffect(() => {
    isLoading() === true && setError(null);
  });

  return {
    isLoading,
    error,
    data,
    create,
    edit,
    archive,
  };
};
