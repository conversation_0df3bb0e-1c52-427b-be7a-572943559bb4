import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';
import { createEffect, createSignal } from 'solid-js';
import { createStore } from 'solid-js/store';
import { MedicalDataAPI } from '../api/medical-data';

export const useMedicalDataReport = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [data, setData] = createStore<UserMedicalDataReport>({
    identification: '',
    data: [],
  });
  const fetchMedicalReport = async () => {
    try {
      setIsLoading(true);
      const result =
        await new MedicalDataAPI().getUserMedicalDataReportByIdentification();
      setData(() => result);
    } catch (error) {
      setError('Error obteniendo las notificaciones');
    } finally {
      setIsLoading(false);
    }
  };
  createEffect(() => {
    fetchMedicalReport();
  });
  createEffect(() => {
    isLoading() === true && setError(null);
  });

  return { isLoading, error, data, fetchMedicalReport };
};
