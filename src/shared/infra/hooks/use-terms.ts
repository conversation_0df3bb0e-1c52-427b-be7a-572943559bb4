import { Terms } from 'entities/Terms';
import { createSignal, onMount } from 'solid-js';
import { createStore } from 'solid-js/store';
import { TermsAPI } from '../api/terms';

export const useTerms = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [terms, setTerms] = createStore<Terms>({
    id: '',
    content: '',
    version: 0,
    termsStatus: 'FETCHING',
    createdAt: '',
    updatedAt: '',
  });
  const fetchLatestTerms = async () => {
    try {
      setIsLoading(true);
      const terms = await new TermsAPI().getLatestTerms();
      if (!terms) throw 'TERMS_NOT_FOUND';
      terms && setTerms(terms);
    } catch (error) {
      const errorMessage =
        error === 'TERMS_NOT_FOUND'
          ? 'No se encontraron términos y condiciones disponibles, por favor conecte al administrador.'
          : 'Error inesperado al obtener los términos y condiciones, por favor conecte al administrador.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    fetchLatestTerms();
  });

  return { isLoading, error, terms };
};
