import { createSignal } from 'solid-js';
import { ChallengeAPI } from '../api/challenge';

export const useChallengeInvitation = () => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const updateChallengeInitialValue = async (data: {
    challengeId: string;
    userId: string;
    initialValue: string;
  }) => {
    try {
      setIsLoading(true);
      await new ChallengeAPI().postParticipationInitialValue(data);
    } catch (error) {
      setError('Error al actualizar los valores iniciales del reto');
    } finally {
      setIsLoading(false);
    }
  };

  return { isLoading, error, updateChallengeInitialValue };
};
