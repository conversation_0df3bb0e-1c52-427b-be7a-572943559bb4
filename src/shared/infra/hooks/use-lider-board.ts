import { createSignal, onMount } from 'solid-js';
import { ChallengeAPI } from '../api/challenge';
import { GroupChallengeProgressResponse } from 'apps/challenges/types';
import { createStore } from 'solid-js/store';

export const useLeaderBoard = (props: { id: string }) => {
  const [isLoading, setIsLoading] = createSignal<boolean>(false);
  const [error, setError] = createSignal<string | null>(null);
  const [leaderBoard, setLeaderBoard] =
    createStore<GroupChallengeProgressResponse>({
      winner: undefined,
      leaderboard: [],
    });
  const fetchChallengeLiderBoard = async (id: string) => {
    try {
      setIsLoading(true);
      const leaderBoard = await new ChallengeAPI().getChallengeLeaderBoard(id);
      setLeaderBoard(leaderBoard);
    } catch (error) {
      setError('Error al actualizar los valores iniciales del reto');
    } finally {
      setIsLoading(false);
    }
  };
  const setChallengeWinner = async (userId: string, id: string) => {
    try {
      setIsLoading(true);
      await new ChallengeAPI().setWinner(userId, id);
    } catch (error) {
      setError('Error al actualizar los valores iniciales del reto');
    } finally {
      setIsLoading(false);
    }
  };
  onMount(() => {
    fetchChallengeLiderBoard(props.id);
  });

  return { isLoading, error, leaderBoard, setChallengeWinner };
};
