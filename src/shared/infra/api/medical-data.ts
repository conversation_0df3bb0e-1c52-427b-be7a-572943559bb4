import { API } from '../../services/app-base/api-base';

import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';

export class MedicalDataAPI extends API {
  constructor() {
    super('medical-data');
  }

  public async getUserMedicalDataReportByIdentification() {
    const response = await this.request({
      method: 'POST',
      url: 'user-medical-data',
    });

    return response.data as UserMedicalDataReport;
  }

  public async getEmbedQSProgressReportURL() {
    const response = await this.request({
      method: 'GET',
      url: 'quicksight-progress-embed-dashboard-url',
    });

    return response.data as string;
  }

  public async getEmbedQSExamReportURL() {
    const response = await this.request({
      method: 'GET',
      url: 'quicksight-exams-embed-dashboard-url',
    });

    return response.data as string;
  }

  public async getEmbedQSStudiesReportURL() {
    const response = await this.request({
      method: 'GET',
      url: 'quicksight-studies-embed-dashboard-url',
    });

    return response.data as string;
  }
}
