export class LeadAPI {
  public async createLead(data: {
    name: string;
    email: string;
    source: string;
    platform: string;
    client: string;
    date: string;
  }) {
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authorization', 'Bearer 2');
    const body = JSON.stringify(data);
    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body,
      mode: 'no-cors',
    };
    await fetch(
      'https://hooks.zapier.com/hooks/catch/13150160/3n3s49b/',
      requestOptions
    );
  }
}
