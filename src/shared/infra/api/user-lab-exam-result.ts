import { UserLabExamResult as UserLabExamResult } from 'entities/UserLabExamResult';
import { API } from '../../services/app-base/api-base';

export class UserLabExamResultAPI extends API {
  constructor() {
    super('user-lab-exam-result');
  }

  public async getUserLabExamResultByStatus() {
    const response = await this.request({
      method: 'POST',
      url: 'user-lab-examn-result-list-by-status',
      body: {
        status: 'ACTIVE',
      },
    });

    return response.data as UserLabExamResult[];
  }

  // eslint-disable-next-line
  public async createUserLabExamResult(data: any) {
    const response = await this.request({
      method: 'POST',
      url: 'user-lab-examn-result',
      body: data,
    });

    return response.data as UserLabExamResult;
  }

  // eslint-disable-next-line
  public async updateUserLabExamResult(data: any) {
    const response = await this.request({
      method: 'PUT',
      url: 'user-lab-examn-result',
      body: data,
    });

    return response.data as UserLabExamResult;
  }

  public async archiveUserLabExamResult(id: string) {
    const response = await this.request({
      method: 'DELETE',
      url: 'user-lab-examn-result',
      params: { id },
    });

    return response.data as UserLabExamResult;
  }
}
