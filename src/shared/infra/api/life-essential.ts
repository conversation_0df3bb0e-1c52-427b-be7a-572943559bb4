import { UserMedicalDataReport } from 'entities/UserMedicalDataReport';
import { API } from '../../services/app-base/api-base';
import {
  LifeEssentialDashboardData,
  NewHabit,
} from '../../../apps/health/components/habits/types';

export class LifeEssentialAPI extends API {
  constructor() {
    super('life-essential');
  }

  public async createLifeEssential(data: NewHabit) {
    const response = await this.request({
      method: 'POST',
      url: 'life-essential',
      body: data,
    });

    return response.data as UserMedicalDataReport;
  }

  public async getLifeEssentialDashboardData(
    id: string
  ): Promise<LifeEssentialDashboardData[]> {
    const response = await this.request({
      method: 'GET',
      url: 'life-essential-dashboard',
      params: {
        id,
      },
    });

    return response.data as LifeEssentialDashboardData[];
  }
}
