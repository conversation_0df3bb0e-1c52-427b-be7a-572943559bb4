import { Notification } from 'entities/Notification';
import { API } from '../../services/app-base/api-base';
import { getCurrentUserData } from '../../services/user/user-session-management';

export class NotificationAPI extends API {
  constructor() {
    super('notifications');
  }

  public async getNotifications() {
    const userData = getCurrentUserData();
    const response = await this.request({
      method: 'POST',
      url: 'notifications-get',
      body: { userId: userData.id },
    });

    return response.data as Notification[];
  }

  public async markAsRead(id: string) {
    await this.request({
      method: 'POST',
      url: 'notifications-read',
      body: { id },
    });
  }

  public async archiveNotification(id: string) {
    await this.request({
      method: 'DELETE',
      url: 'notification',
      params: { id },
    });
  }
}
