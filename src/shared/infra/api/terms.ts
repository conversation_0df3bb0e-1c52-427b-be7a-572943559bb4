import { User } from 'entities/User';
import { API } from '../../services/app-base/api-base';
import { Terms } from 'entities/Terms';

export class TermsAPI extends API {
  constructor() {
    super('terms');
  }

  public async getLatestTerms() {
    const response = await this.request({
      method: 'GET',
      url: 'terms/latest',
    });

    return response.data as Terms | null;
  }

  public async acceptTerms(payload: { userId: string; version: number }) {
    const response = await this.request({
      method: 'POST',
      url: 'user-terms',
      body: { ...payload },
    });

    return response.data as User;
  }
}
