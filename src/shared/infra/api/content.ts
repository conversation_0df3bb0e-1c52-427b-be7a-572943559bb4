import { Post } from 'entities/Post';
import { API } from '../../services/app-base/api-base';
import { getCurrentUserData } from '../../services/user/user-session-management';
import { HealthContent } from 'entities/HealthContent';

export class HealthContentAPI extends API {
  constructor() {
    super('content');
  }

  public async getHealthPostByStatus() {
    const response = await this.request({
      method: 'POST',
      url: 'content-feed',
      body: { pageSize: 500, pageIndex: 0 },
    });

    return response.data as Post[];
  }

  public async postLike(post: Post) {
    const response = await this.request({
      method: 'POST',
      url: 'content-update',
      body: post,
    });

    return response.data as Post;
  }

  public async updatePost(post: Post) {
    const user = getCurrentUserData();
    const response = await this.request({
      method: 'POST',
      url: 'content-update',
      body: {
        ...post,
        userName: user.name, // There might be the possibility that the user name has changed
      },
    });

    return response.data as Post;
  }

  public async updateLikeCount(data: Post) {
    const response = await this.request({
      method: 'PUT',
      url: 'update-like-count',
      body: data,
    });

    return response.data as Post;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async getHealthContentsByStatus(_status: string) {
    const response = await this.request({
      method: 'POST',
      url: 'content-feed',
      body: { pageSize: 500, pageIndex: 0 },
    });

    return response.data as Post[];
  }

  public async getHealthPostById(id: string) {
    const response = await this.request({
      method: 'GET',
      url: 'content',
      params: {
        id,
      },
    });

    return response.data as Post;
  }

  public async createHealthContent(content: string, images: Array<string>) {
    const user = getCurrentUserData();
    const response = await this.request({
      method: 'POST',
      url: 'content-create',
      body: {
        userId: user.id,
        userName: user.name,
        content: content,
        imagesURL: images,
        externalLinks: [],
        contentStatus: 'ACTIVE',
        likes: [],
      },
    });

    return response.data as Post;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public async updateHealthContentInformation(data: any) {
    const response = await this.request({
      method: 'POST',
      url: 'content-update',
      body: data,
    });

    return response.data as HealthContent;
  }

  public async archiveHealthContent(id: string) {
    const response = await this.request({
      method: 'POST',
      url: 'content-archive',
      body: { contentId: id },
    });

    return response.data as HealthContent[];
  }
}
