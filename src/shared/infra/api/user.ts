import { UserProps } from '../../../apps/auth/multi-sign-in/types';
import { API } from '../../services/app-base/api-base';
import {
  UserUpdatedData,
  UserUpdatedPassword,
  UserUpdatedProfilePicture,
} from '../../../apps/profile/types';
import { User } from 'entities/User';
import { NewUser } from 'apps/admin/types';

export class UserAPI extends API {
  constructor() {
    super('user');
  }

  public async activateUser(id: string) {
    const response = await this.request({
      method: 'PUT',
      url: 'activate-use',
      params: { id },
    });

    return response.data as User[];
  }

  public async createUser(data: NewUser) {
    const response = await this.request({
      method: 'POST',
      url: 'user',
      body: data,
    });

    return response.data as UserProps;
  }

  public async getUsersByStatus(status: string) {
    const response = await this.request({
      method: 'GET',
      url: 'user-list-by-status',
      params: { status },
    });

    return response.data as User[];
  }

  public async archiveUser(id: string) {
    const response = await this.request({
      method: 'DELETE',
      url: 'user',
      params: { id },
    });

    return response.data as User[];
  }

  public async resetUserPassword(id: string) {
    const response = await this.request({
      method: 'POST',
      url: 'user-password',
      body: { id },
    });

    return response.data as { password: string };
  }

  public async updateUserInformation(data: UserUpdatedData) {
    const response = await this.request({
      method: 'PUT',
      url: 'user',
      body: data,
    });

    return response.data as UserProps;
  }

  public async updateProfilePicture(data: UserUpdatedProfilePicture) {
    const response = await this.request({
      method: 'PUT',
      url: 'user-profile-picture',
      body: data,
    });

    return response.data as UserProps;
  }

  public async updateUserPassword(data: UserUpdatedPassword) {
    const response = await this.request({
      method: 'PUT',
      url: 'user-password',
      body: data,
    });

    return response.data as UserProps;
  }
}
