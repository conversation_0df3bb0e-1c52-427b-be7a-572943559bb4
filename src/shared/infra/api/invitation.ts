import { User } from 'entities/User';
import { API } from '../../services/app-base/api-base';

export class InvitationAPI extends API {
  constructor() {
    super('invitation');
  }

  public async acceptInvitation(id: string) {
    const response = await this.request({
      method: 'PUT',
      url: 'invitation/accept',
      body: { id },
    });

    return response.data as User;
  }

  public async rejectInvitation(id: string) {
    const response = await this.request({
      method: 'PUT',
      url: 'invitation/reject',
      body: { id },
    });

    return response.data as User[];
  }
}
