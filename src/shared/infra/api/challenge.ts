import { API } from '../../services/app-base/api-base';
import {
  GroupChallengeProgressResponse,
  NewChallengeFormDTO,
  UpdateChallengeFormDTO,
  UpdateChallengeParticipationInitialValueDTO,
} from '../../../apps/challenges/types';
import { Challenge } from 'entities/Challenge';
import { ChallengeProgress } from 'entities/ChallengeProgress';
import { ChallengeProgressEntry } from 'shared/components/challenge-progress-modal/challenge-progress-types';

export class ChallengeAPI extends API {
  constructor() {
    super('challenge');
  }

  public async getUserChallenges(
    id: string,
    status: string
  ): Promise<Challenge[]> {
    const response = await this.request({
      method: 'GET',
      url: 'user-challenge-list',
      params: { userId: id, status },
    });

    return response.data as Challenge[];
  }

  public async postChallenge(
    challenge: NewChallengeFormDTO
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'POST',
      url: 'challenge',
      body: { ...challenge },
    });

    return response.data as Challenge;
  }

  public async postParticipationInitialValue(
    dto: UpdateChallengeParticipationInitialValueDTO
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'POST',
      url: 'participant/initial-value',
      body: { ...dto },
    });

    return response.data as Challenge;
  }

  public async setWinner(
    userId: string,
    challengeId: string
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'PUT',
      url: 'participant/winner',
      body: { userId, challengeId },
    });

    return response.data as Challenge;
  }

  public async updateChallenge(
    updateData: UpdateChallengeFormDTO
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'PUT',
      url: 'challenge',
      body: { ...updateData },
    });

    return response.data as Challenge;
  }

  public async archiveChallenge(id: string) {
    const response = await this.request({
      method: 'DELETE',
      url: 'challenge',
      params: { id },
    });

    return response.data as Challenge;
  }

  public async completeChallenge(id: string) {
    const response = await this.request({
      method: 'POST',
      url: 'challenge/complete',
      params: { id },
    });

    return response.data as Challenge;
  }

  public async getChallengeById(challengeId: string): Promise<Challenge> {
    const response = await this.request({
      method: 'GET',
      url: 'challenge',
      params: { challengeId },
    });

    return response.data as Challenge;
  }

  public async getChallengesProgress(id: string): Promise<ChallengeProgress[]> {
    const response = await this.request({
      method: 'GET',
      url: 'challenge-progress',
      params: { challengeId: id },
    });

    return response.data as ChallengeProgress[];
  }

  public async getChallengeLeaderBoard(
    id: string
  ): Promise<GroupChallengeProgressResponse> {
    const response = await this.request({
      method: 'GET',
      url: 'leader-board',
      params: { challengeId: id },
    });

    return response.data as GroupChallengeProgressResponse;
  }

  public async archiveChallengeProgress(id: string) {
    const response = await this.request({
      method: 'DELETE',
      url: 'challenge-progress',
      params: { id },
    });

    return response.data as ChallengeProgress;
  }

  public async postChallengeProgress(
    progress: ChallengeProgressEntry
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'POST',
      url: 'challenge-progress',
      body: { ...progress },
    });

    return response.data as Challenge;
  }

  public async postBatchChallengeProgress(
    progress: ChallengeProgressEntry[]
  ): Promise<Challenge> {
    const response = await this.request({
      method: 'POST',
      url: 'batch-challenge-progress',
      body: { ...progress },
    });

    return response.data as Challenge;
  }

  public async getUserChallengeProgress(challengeId: string, userId: string) {
    const response = await this.request({
      method: 'GET',
      url: 'challenge-progress/user-progress',
      params: { challengeId, userId },
    });

    return response.data as ChallengeProgress[];
  }
}
