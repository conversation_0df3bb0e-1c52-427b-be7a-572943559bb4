export type APIService =
  | 'user'
  | 'activate-user'
  | 'medical-data'
  | 'life-essential'
  | 'user-lab-exam-result'
  | 'challenge'
  | 'invitation'
  | 'content'
  | 'notifications'
  | 'terms';
export type APIStages = 'prod' | 'stg' | 'dev';
export type Service = {
  service: APIService;
  url: string;
};
type ServiceLibraryResource = {
  name: APIService;
  url: string;
};
const productionServiceLibrary: ServiceLibraryResource[] = [
  {
    name: 'user',
    url: 'https://lk1b0v0rt1.execute-api.sa-east-1.amazonaws.com/prod/',
  },

  {
    name: 'medical-data',
    url: 'https://zpo8thngm1.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'life-essential',
    url: 'https://gmp6xh5jwh.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'user-lab-exam-result',
    url: 'https://yc2or8xhc2.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'challenge',
    url: 'https://l7fw4qqyb5.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'content',
    url: 'https://uwwy261an1.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'notifications',
    url: 'https://rnwieeblml.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'invitation',
    url: 'https://mnd3k05000.execute-api.sa-east-1.amazonaws.com/prod/',
  },
  {
    name: 'terms',
    url: 'https://v2c9iym6gj.execute-api.sa-east-1.amazonaws.com/prod/',
  },
];
const developmentServiceLibrary: ServiceLibraryResource[] = [
  {
    name: 'user',
    url: 'https://aj5tukvw99.execute-api.sa-east-1.amazonaws.com/dev/',
  },

  {
    name: 'medical-data',
    url: 'https://8ch7r7kbq7.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'life-essential',
    url: 'https://7rqludm3oa.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'user-lab-exam-result',
    url: 'https://edushvjqda.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'challenge',
    url: 'https://ok8r992sw9.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'content',
    url: 'https://q7x9g8z5l8.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'notifications',
    url: 'https://db5pwnd8mg.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'invitation',
    url: 'https://rvr6hvgmo5.execute-api.sa-east-1.amazonaws.com/dev/',
  },
  {
    name: 'terms',
    url: 'https://v45dkdpspd.execute-api.sa-east-1.amazonaws.com/dev/',
  },
];
export const serviceLibrary = {
  prod: [...productionServiceLibrary],
  stg: [...developmentServiceLibrary],
  dev: [...developmentServiceLibrary],
};
