/* eslint-disable @typescript-eslint/no-explicit-any */
import { User } from 'entities/User';

export class Services {
  private readonly localDataUpdatedEvent = new Event(
    'localDataUpdated'
  ) as CustomEvent;

  public getLocalStorageItem(id: string): string {
    return JSON.parse(localStorage.getItem(id) || '');
  }

  public getCurrentUserData() {
    const localData = localStorage.getItem('user-local-data');
    if (!localData) {
      throw 'Missing user data';
    }

    return localData;
  }

  public async convertFileToBase64(file: File): Promise<string> {
    const fileToLoad = file;
    const fileReader = new FileReader();
    fileReader.readAsDataURL(fileToLoad);

    return new Promise<string>((resolve) => {
      fileReader.onload = () => {
        resolve(fileReader.result as string);
      };
    });
  }

  public isFileSizeValid(size: number) {
    const FILE_SIZE_LIMIT = 1000000;

    return size <= FILE_SIZE_LIMIT ? true : false;
  }

  public updateUserLocalStorageUserData(data: User) {
    const localData = JSON.parse(localStorage.getItem('user-local-data') || '');
    const updatedData = { ...localData, ...data };
    localStorage.setItem('user-local-data', JSON.stringify(updatedData));
    document.dispatchEvent(this.localDataUpdatedEvent);
  }

  public getHTMLElement(element: string) {
    return document.getElementsByTagName(element)[0];
  }

  public trimStringProperties<T extends Record<string, any>>(inputObj: T): T {
    const resultObj: Partial<T> = {};
    for (const key in inputObj) {
      if (typeof inputObj[key] === 'string') {
        // Trim the string and assign it to the result object
        resultObj[key] = inputObj[key].trim();
      } else {
        // If the property is not a string, simply assign it to the result object
        resultObj[key] = inputObj[key];
      }
    }

    // Cast to the original type to maintain type safety
    return resultObj as T;
  }

  public isDataMissingInformation<T>(obj: T): boolean {
    for (const prop in obj) {
      if (obj[prop] === undefined || obj[prop] === null) {
        return true;
      }
    }

    return false;
  }
}
