import { Accessor, Setter, createSignal } from 'solid-js';
import Swal from 'sweetalert2';

export class Page {
  private swal;

  public isLoading: Accessor<boolean>;

  public setIsLoading: Setter<boolean>;

  constructor() {
    const [isLoading, setIsLoading] = createSignal<boolean>(false);
    this.isLoading = isLoading;
    this.setIsLoading = setIsLoading;
    this.swal = Swal;
    this.swal.mixin({
      allowOutsideClick: false,
      allowEscapeKey: false,
      showCancelButton: false,
      confirmButtonText: 'OK',
      buttonsStyling: false,
      customClass: {},
    });
  }

  public getURLParams(): URLSearchParams {
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);

    return urlParams;
  }

  public showLoadingScreen() {
    document.body.classList.add('page-loading');
    document.body.setAttribute('data-kt-app-page-loading', 'on');
  }

  public hideLoadingScreen() {
    document.body.classList.remove('page-loading');
    document.body.removeAttribute('data-kt-app-page-loading');
  }

  public pageRedirect(url: string) {
    window.location.href = url;
  }

  public logger(error: Error | string | unknown) {
    if (import.meta.env.VITE_ENV === 'dev') {
      // eslint-disable-next-line no-console
      console.log(JSON.stringify(error));
    }
  }

  showWarningAlert({
    message,
    url,
    loaderException,
  }: {
    message: string;
    url?: string;
    loaderException?: boolean;
  }) {
    this.swal
      .fire({
        title: 'Alerta',
        text: message,
        icon: 'warning',
        allowEscapeKey: false,
        allowOutsideClick: false,
        customClass: {
          confirmButton: 'btn btn-warning',
          cancelButton: 'btn btn-secondary',
        },
      })
      .then((value) => {
        if (value.isConfirmed) {
          !loaderException && this.hideLoadingScreen();
          url && this.pageRedirect(url);

          return;
        }
      });
  }

  showErrorAlert({
    message,
    url,
    loaderException,
    footer,
  }: {
    message: string;
    url?: string;
    loaderException?: boolean;
    footer?: string;
  }) {
    this.swal
      .fire({
        title: 'Error',
        text: message,
        icon: 'error',
        customClass: {
          confirmButton: 'btn btn-danger',
          cancelButton: 'btn btn-secondary',
        },
        footer,
        allowEscapeKey: false,
        allowOutsideClick: false,
      })
      .then((value) => {
        if (value.isConfirmed) {
          !loaderException && this.hideLoadingScreen();
          url && this.pageRedirect(url);

          return;
        }
      });
  }

  showSuccessAlert({
    title,
    message,
    url,
    loaderException,
  }: {
    title?: string;
    message: string;
    url?: string;
    loaderException?: boolean;
  }) {
    this.swal
      .fire({
        title: title || 'Éxito',
        text: message,
        icon: 'success',
        allowEscapeKey: false,
        allowOutsideClick: false,
        customClass: {
          confirmButton: 'btn btn-success',
          cancelButton: 'btn btn-secondary',
        },
      })
      .then((value) => {
        if (value.isConfirmed) {
          !loaderException && this.hideLoadingScreen();
          url && this.pageRedirect(url);

          return;
        }
      });
  }

  showInfoAlert({
    message,
    url,
    loaderException,
  }: {
    message: string;
    url?: string;
    loaderException?: boolean;
  }) {
    this.swal
      .fire({
        title: 'Alerta',
        text: message,
        icon: 'info',
        allowEscapeKey: false,
        allowOutsideClick: false,
        customClass: {
          confirmButton: 'btn btn-info',
          cancelButton: 'btn btn-secondary',
        },
      })
      .then((value) => {
        if (value.isConfirmed) {
          !loaderException && this.hideLoadingScreen();
          url && this.pageRedirect(url);

          return;
        }
      });
  }
}
