import { CognitoAccessToken } from 'amazon-cognito-identity-js';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  APIService,
  serviceLibrary,
  APIStages,
} from '../../infra/service-library';
import { CustomIncomingRequest, IncomingRequest } from './types';
import { CognitoServices } from '../user/cognito-services';

export class API {
  private axiosInstance: AxiosInstance;

  public token: CognitoAccessToken;

  private cognitoServices: CognitoServices;

  private readonly mainAPI: APIService;

  private readonly env: APIStages = import.meta.env.VITE_ENV as APIStages;

  constructor(mainAPI: APIService) {
    this.mainAPI = mainAPI;
    this.cognitoServices = new CognitoServices(this.env);
    this.init();
  }

  private async init() {
    this.token = await this.cognitoServices.getUserToken();
    this.axiosInstance = this.getAxiosInstance(this.mainAPI);
  }

  private getAxiosInstance(api: APIService) {
    const baseURL: string = this.getBaseURL(api);

    return axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        Authorization: this.token.getJwtToken() || '',
        accept: ' application/json',
      },
    });
  }

  private getBaseURL(api: APIService) {
    const service = serviceLibrary[this.env].find(
      (service) => service.name === api
    );
    if (!service) throw 'Missing service base URL';

    return service.url;
  }

  private async tokenVerification() {
    if (!this.token) await this.init();
    const response = await this.cognitoServices.checkToken(this.token);
    if (response) await this.init();
  }

  public async request(data: IncomingRequest): Promise<AxiosResponse> {
    await this.tokenVerification();

    return this.axiosInstance({
      method: data.method,
      url: data.url,
      data: data.body,
      params: data.params,
    });
  }

  public async customRequest(data: CustomIncomingRequest) {
    await this.tokenVerification();
    const baseURL = this.getBaseURL(data.api);

    return axios({
      baseURL,
      timeout: 30000,
      headers: {
        Authorization: this.token.getJwtToken() || '',
        accept: ' application/json',
      },
      url: data.url,
      method: data.method,
      data: data.body,
      params: data.params,
    });
  }
}
