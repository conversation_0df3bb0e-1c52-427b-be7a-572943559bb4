import {
  CognitoAccessToken,
  CognitoRefreshToken,
  CognitoUserPool,
  CognitoUserSession,
  ICognitoUserPoolData,
} from 'amazon-cognito-identity-js';
import { CognitoUserPoolData } from '../../infra/cognito-user-pool';
import { APIStages } from '../../infra/service-library';

export class CognitoServices {
  public poolData: ICognitoUserPoolData;

  private userPool: CognitoUserPool;

  private readonly env: APIStages = import.meta.env.VITE_ENV as APIStages;

  constructor(env: APIStages) {
    this.env = env;
    this.poolData = this.getPoolData();
    this.userPool = new CognitoUserPool(this.poolData);
  }

  private getPoolData() {
    const poolData = CognitoUserPoolData[this.env];
    if (!poolData) throw 'Missing cognito pool data';

    return poolData;
  }

  public async getUserToken(): Promise<CognitoAccessToken> {
    const userSession = await this.getUserSession();

    return userSession.getIdToken();
  }

  public async checkToken(token: CognitoAccessToken) {
    try {
      const hasExpired = this.hasTokenExpired(token.decodePayload()['exp']);
      if (hasExpired) return await this.refreshUserToken();

      return false;
    } catch (error) {
      return false;
    }
  }

  private hasTokenExpired(exp: number): boolean {
    const hasExpired = Date.now() >= exp * 1000;

    return hasExpired;
  }

  public async refreshUserToken() {
    const refreshToken = await this.getUserRefreshToken();

    return new Promise((resolve) => {
      const user = this.userPool.getCurrentUser();
      try {
        if (!user) throw new Error('User not found');
        user.refreshSession(refreshToken, () => {
          resolve(true);
        });
      } catch (error) {
        location.reload();
      }
    });
  }

  public async getUserRefreshToken(): Promise<CognitoRefreshToken> {
    const userSession = await this.getUserSession();

    return userSession.getRefreshToken();
  }

  public getUserSession(): Promise<CognitoUserSession> {
    return new Promise((resolve, reject) => {
      const user = this.userPool.getCurrentUser();
      if (!user) {
        reject("Can't get user session. User is not logged in.");

        return;
      }
      user.getSession((error: Error | null, session: CognitoUserSession) => {
        if (error) location.reload();
        resolve(session);
      });
    });
  }
}
