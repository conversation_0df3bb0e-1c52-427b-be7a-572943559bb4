import {
  ChallengeProgressEntry,
  ProgressRecord,
} from '../../components/challenge-progress-modal/challenge-progress-types';
import { Services } from '../app-base/service-base';
import { getCurrentUserData } from '../user/user-session-management';
import { ImageServices } from '../utils/image-services';

export type ChallengeProgressServicesProps = {
  challengeId: string;
  challengeProgressType: string;
};
export class ChallengeProgressServices {
  private readonly userId: string;

  private readonly userName: string;

  private readonly challengeId: string;

  private readonly challengeProgressType: string;

  constructor(props: ChallengeProgressServicesProps) {
    const { id: userId, name: userName } = getCurrentUserData();
    this.userId = userId;
    this.userName = userName;
    this.challengeId = props.challengeId;
    this.challengeProgressType = props.challengeProgressType;
  }

  public validateProgressForm({
    isHabit,
    isEvidenceRequired,
    progress,
  }: {
    isHabit: boolean;
    isEvidenceRequired: boolean;
    progress: ProgressRecord[];
  }) {
    if (!this.isProgressValid(isHabit, progress))
      throw 'LOGGED_PROGRESS_NOT_VALID';
    if (!this.isProgressEvidenceValid(isEvidenceRequired, progress))
      throw 'LOGGED_EVIDENCE_NOT_VALID';
    if (!this.isProgressEvidenceSizeValid(progress))
      throw 'FILE_SIZE_LIMIT_EXCEEDED';
  }

  public isProgressValid(
    isHabit: boolean,
    progress: ProgressRecord[]
  ): boolean {
    if (isHabit) {
      const isProgressEmpty = progress.length === 0;

      return !isProgressEmpty;
    }

    return !progress.some((record) => record.progress < 0);
  }

  public isProgressEvidenceValid(
    isEvidenceRequired: boolean,
    progress: ProgressRecord[]
  ): boolean {
    if (isEvidenceRequired) {
      return !progress.some((record) => record.evidence === null);
    }

    return !progress.some((record) => record.progress < 0);
  }

  public isProgressEvidenceSizeValid(progress: ProgressRecord[]): boolean {
    const FILE_SIZE_LIMIT = 5000000;

    return !progress
      .filter((record) => record.evidence !== null)
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      .some((record) => record.evidence!.size >= FILE_SIZE_LIMIT);
  }

  public async mapProgressRecordListToChallengeProgressEntry(
    progress: ProgressRecord[]
  ): Promise<ChallengeProgressEntry[]> {
    const challengeProgressEntryList: ChallengeProgressEntry[] = [];
    for (const record of progress) {
      const evidence = await this.handleEvidenceFile(record.evidence);
      challengeProgressEntryList.push({
        challengeId: this.challengeId,
        userId: this.userId,
        userName: this.userName,
        value: record.progress,
        challengeProgressType:
          this.challengeProgressType === 'HABIT' ? 'HABIT' : 'NUMERIC',
        progressDate: record.date,
        evidence,
      });
    }

    return challengeProgressEntryList;
  }

  private async handleEvidenceFile(evidence: File | null) {
    if (!evidence) return null;
    const compressFile = await ImageServices.CompressImage(evidence, {
      maxSizeMB: 5,
      maxWidthOrHeight: 900,
    });
    const file = await new Services().convertFileToBase64(compressFile);

    return {
      name: evidence.name,
      size: compressFile.size,
      type: evidence.type,
      file: file,
    };
  }

  public mapErrorToText(error: unknown): string {
    const DEFAULT_ERROR = 'ERROR: Error en el formulario de progreso';
    if (typeof error !== 'string') {
      return DEFAULT_ERROR;
    }
    switch (error) {
      case 'CONTEXT_NOT_FOUND':
        return 'ERROR: Error interno del sistema, (CONTEXT_NOT_FOUND)';
      case 'LOGGED_PROGRESS_NOT_VALID':
        return 'ERROR: El progreso registrado no es válido, (LOGGED_PROGRESS_NOT_VALID)';
      case 'LOGGED_EVIDENCE_NOT_VALID':
        return 'ERROR: La evidencia registrada no es válida, (LOGGED_EVIDENCE_NOT_VALID)';
      case 'FILE_SIZE_LIMIT_EXCEEDED':
        return 'ERROR: El tamaño del archivo excede el límite permitido, (FILE_SIZE_LIMIT_EXCEEDED)';
      default:
        return DEFAULT_ERROR;
    }
  }
}
