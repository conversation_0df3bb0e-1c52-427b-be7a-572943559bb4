export class BodyAnimationService {
  public static changeOpacity() {
    setTimeout(() => {
      const body = document.body;
      const computedStyle = window.getComputedStyle(body);
      if (computedStyle.display === 'none') {
        body.style.display = 'block'; // You can use any appropriate display value here
      }
      let opacity = 0;
      const increment = 0.1;
      const intervalDuration = 30; // Duration between opacity changes (in milliseconds)
      const intervalId = setInterval(() => {
        opacity += increment;
        if (opacity === 1) return;
        body.style.opacity = opacity.toString();
        if (opacity >= 1) {
          clearInterval(intervalId);
        }
      }, intervalDuration);
    }, 400);
  }
}
