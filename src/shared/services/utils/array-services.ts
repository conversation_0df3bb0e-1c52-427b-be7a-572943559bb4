/* eslint-disable @typescript-eslint/no-explicit-any */
export const arraySplitter = (array: object[], num: number): object[][] => {
  const result: object[][] = [];
  let subArray: object[] = [];
  for (let i = 0; i < array.length; i++) {
    if (i % num === 0 && i !== 0) {
      result.push(subArray);
      subArray = [];
    }
    subArray.push(array[i]);
  }
  result.push(subArray);

  return result;
};
export const arraySorter = (
  array: object[],
  property: string,
  isDescending = false
): object[] => {
  if (!property) return array;
  const arrayToSort = Array.from(array);

  return arrayToSort.sort((a: any, b: any) => {
    if (isDescending) {
      if (a[property] < b[property]) {
        return 1;
      } else if (a[property] > b[property]) {
        return -1;
      } else {
        return 0;
      }
    } else {
      if (a[property] > b[property]) {
        return 1;
      } else if (a[property] < b[property]) {
        return -1;
      } else {
        return 0;
      }
    }
  });
};
export const arrayFilter = (array: any[], keyword: string) => {
  const matches = [];
  if (!keyword) return array;
  for (const obj of array) {
    for (const prop in obj) {
      if (
        // eslint-disable-next-line no-prototype-builtins
        obj.hasOwnProperty(prop) &&
        obj[prop] &&
        (obj[prop] as string)
          .toString()
          .toLowerCase()
          .includes(keyword.toLowerCase())
      ) {
        matches.push(obj);
        break;
      }
    }
  }

  return matches;
};
interface IDataObject {
  [key: string]: any;
}
export const arrangeProperties = (
  data: IDataObject[],
  controls: string[]
): IDataObject[] => {
  const result: IDataObject[] = [];
  // loop through each data object
  for (const dataObj of data) {
    const newObj: IDataObject = {};
    // loop through each control property in order
    for (const controlProp of controls) {
      // check if the property exists in the data object
      // eslint-disable-next-line no-prototype-builtins
      if (dataObj.hasOwnProperty(controlProp)) {
        // if it does, add it to the new object
        newObj[controlProp] = dataObj[controlProp];
      }
    }
    // loop through any remaining data object properties not in the control object
    for (const dataProp in dataObj) {
      if (
        // eslint-disable-next-line no-prototype-builtins
        dataObj.hasOwnProperty(dataProp) &&
        // eslint-disable-next-line no-prototype-builtins
        !newObj.hasOwnProperty(dataProp)
      ) {
        newObj[dataProp] = dataObj[dataProp];
      }
    }
    result.push(newObj);
  }

  return result;
};
