import imageCompression, { Options } from 'browser-image-compression';

export class ImageServices {
  public static async CompressImage(
    file: File,
    options?: Options
  ): Promise<File> {
    try {
      const compressionOptions: Options = {
        maxSizeMB: 10, // Maximum size in MB (adjust as needed)
        useWebWorker: true, // Use web workers for better performance
        ...options, // Merge with user-provided options
      };
      // Compress the image file
      const compressedFile = await imageCompression(file, compressionOptions);

      return compressedFile;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error compressing image:', error);
      throw new Error('Image compression failed');
    }
  }
}
