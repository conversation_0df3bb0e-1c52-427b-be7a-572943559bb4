import { format, utcToZonedTime } from 'date-fns-tz';

export class DateServices {
  public static getDatesBetween(start: string, end?: string) {
    const startDate = new Date(start);
    startDate.setDate(startDate.getDate() + 1);
    const endDate = end ? new Date(end) : new Date();
    endDate.setDate(endDate.getDate() + 1);
    const dates = [];
    for (
      const date = startDate;
      date <= endDate;
      date.setDate(date.getDate() + 1)
    ) {
      dates.push(date.toISOString());
    }

    return dates;
  }

  public static calculateNumberOfDaysBetweenDates({
    startData,
    endDate,
  }: {
    startData: Date;
    endDate: Date;
  }) {
    const timeDifference = endDate.getTime() - startData.getTime();

    return Math.round(timeDifference / (1000 * 3600 * 24));
  }

  /**
   * Converts a UTC date to a local date in a specific timezone and format.
   * @param date - The date to be converted (Date object or ISO string).
   * @param outputFormat - Optional output format string. Defaults to ISO 8601 format.
   * @returns The local date as a formatted string.
   */
  public static fromUTCDateToLocalDate(
    date: Date | string,
    outputFormat = `yyyy-MM-dd'T'HH:mm:ss.SSS'Z'`
  ): string {
    const timeZone = 'America/Costa_Rica';
    const isoDateString = DateServices.normalizeDateInputToISO(date);
    const localizedDate = utcToZonedTime(isoDateString, timeZone);

    return format(localizedDate, outputFormat, { timeZone });
  }

  /**
   * Normalizes the input date to an ISO string.
   * @param date - The input date, which can be a Date object or a string.
   * @returns A valid ISO date string.
   * @throws Error if the date format is invalid.
   */
  private static normalizeDateInputToISO(date: Date | string): string {
    if (typeof date === 'string') {
      // Handle special case for UTC midnight dates
      if (date.includes('T00:00:00.000Z')) {
        return DateServices.adjustUTCMidnightDate(date);
      }

      return new Date(date).toISOString();
    }
    if (date instanceof Date) {
      return date.toISOString();
    }
    throw new Error('Invalid date input: Must be a Date object or ISO string');
  }

  /**
   * Adjusts UTC midnight dates to a fixed hour offset (e.g., 06:00:00).
   * @param dateString - An ISO string with UTC midnight.
   * @returns The adjusted ISO string.
   */
  private static adjustUTCMidnightDate(dateString: string): string {
    const [datePart] = dateString.split('T');

    return `${datePart}T06:00:00.000Z`;
  }

  public static calculateNumberOfHoursBetweenDates({
    startData,
    endDate,
  }: {
    startData: Date;
    endDate: Date;
  }) {
    const timeDifference = endDate.getTime() - startData.getTime();

    return timeDifference / (1000 * 3600);
  }

  public static addDaysToDate({
    date,
    daysToAdd,
  }: {
    date: Date;
    daysToAdd: number;
  }) {
    const futureDate = new Date(date);
    futureDate.setDate(futureDate.getDate() + daysToAdd);

    return futureDate;
  }

  public static fromLocaleDateStringToDate(dateString: string) {
    const [day, month, year] = dateString.split('-').map(Number);

    return new Date(year, month - 1, day);
  }

  public static fromDateToDateFormat(date: Date): string {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  public static fromDateToDateTimeFormat(date: Date): string {
    const formattedDate = this.fromDateToDateFormat(date);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${formattedDate} ${hours}:${minutes}`;
  }

  public static formatNumberToThousandFormat(number: number): string {
    const [integerPart, decimalPart] = number.toFixed(2).split('.');
    const formattedIntegerPart = integerPart.replace(
      /\B(?=(\d{3})+(?!\d))/g,
      '.'
    );

    return `${formattedIntegerPart},${decimalPart}`;
  }

  public static mapISODateToDateWithoutTime = (isoDateString: string) => {
    try {
      const date = new Date(isoDateString);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      const formattedDate = `${day}/${month}/${year} `;

      return formattedDate;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error mapping ISO date to date without time', error);

      return '-';
    }
  };

  public static calculateAgeBasedOnDate(birthDateIso: string): number {
    try {
      const birthDate = new Date(birthDateIso);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDifference = today.getMonth() - birthDate.getMonth();
      // Check if the birthday hasn't occurred yet this year
      if (
        monthDifference < 0 ||
        (monthDifference === 0 && today.getDate() < birthDate.getDate())
      ) {
        age--;
      }

      return age;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error calculating age', error);

      return 0;
    }
  }
}
