name: Deploy to AWS S3

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
        AWS_REGION : ${{ secrets.AWS_REGION }}
        AWS_S3_BUCKET_NAME : ${{ secrets.AWS_S3_BUCKET_NAME }}
        AWS_ACCESS_KEY_ID : ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY : ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        VITE_POOL_PROD: ${{ secrets.VITE_POOL_PROD }}
        VITE_POOL_DEV: ${{ secrets.VITE_POOL_DEV }}
        VITE_CLIENT_ID_PROD: ${{ secrets.VITE_CLIENT_ID_PROD }}
        VITE_CLIENT_ID_DEV: ${{ secrets.VITE_CLIENT_ID_DEV }}
        VITE_ENV: ${{ secrets.VITE_ENV_PROD }}
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 18

      - name: 📥 Install modules
        run: npm install

      - name: 🔎 Typecheck
        run: npm run typecheck

      - name: 🔦 Lint Code
        run: npm run lint

      - name: 🔥 Build
        run: npm run build

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@master
        with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ${{ secrets.AWS_REGION }}

      - name: Sync to S3
        run: aws s3 sync ./dist s3://${{ secrets.AWS_S3_BUCKET_NAME }}

      
      - name: 🧻 Cleaning workspace
        uses: AutoModality/action-clean@v1
        if: ${{ always() }}

    