{
  "compilerOptions": {
    "jsx": "preserve",
    "jsxImportSource": "solid-js",
    "target": "es2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": [
      "ESNext",
      "DOM",
      "WebWorker"
    ],
    "moduleResolution": "Node",
    "strict": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "noEmit": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "declaration": true,
    "declarationMap": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": false,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "inlineSources": true,
    "experimentalDecorators": true,
    "strictPropertyInitialization": false,
    "typeRoots": [
      "./node_modules/@types",
      "vite/client"
    ],
    "types": [
      "jquery"
    ],
    "checkJs": false,
    "baseUrl": "src/",
    "skipLibCheck": true,
    "outDir": "./dist",
  },
  "include": [
    "**/*.ts",
    "**/**/*.tsx",
    "env.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "template"
  ]
}