{"name": "tb-locus-vitejs", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "lint": "npx eslint -c .eslintrc.cjs ./src/**/*.ts --fix", "build": "export NODE_OPTIONS=--max-old-space-size=16096 && vite build --mode development", "build-ws": "set NODE_OPTIONS=--max-old-space-size=16096 && vite build --mode development", "preview": "vite preview", "typecheck": "tsc", "prepare": "husky install"}, "devDependencies": {"@tiptap/core": "^2.1.12", "@tiptap/starter-kit": "^2.0.4", "@types/dropzone": "^5.7.4", "@types/jquery": "^3.5.14", "@types/node": "^18.11.17", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.48.1", "babel-preset-solid": "^1.6.9", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.2", "prettier": "2.8.1", "solid-tiptap": "^0.6.0", "typescript": "^4.9.4", "vite": "^4.0.4", "vite-plugin-progress": "^0.0.6", "vite-plugin-solid": "^2.5.0"}, "dependencies": {"@popperjs/core": "^2.11.8", "@t3-oss/env-core": "^0.7.1", "@tiptap/extension-bubble-menu": "^2.1.12", "@tiptap/extension-link": "^2.1.12", "@tiptap/pm": "^2.1.12", "amazon-cognito-identity-js": "^6.1.1", "apexcharts": "^3.41.1", "aws-rum-web": "^1.17.1", "axios": "^1.2.1", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.1", "browser-image-compression": "^2.0.2", "date-fns-tz": "^2.0.1", "dotenv": "^16.0.3", "gulp": "^4.0.2", "jquery": "^3.6.3", "lottie-web": "^5.10.2", "moment": "^2.29.4", "react-ga4": "^2.1.0", "sass": "^1.57.1", "solid-apexcharts": "^0.3.2", "solid-bootstrap": "^1.0.14", "solid-headless": "^0.13.1", "solid-icons": "^1.1.0", "solid-js": "^1.6.9", "sweetalert2": "^11.7.3", "terser": "^5.20.0", "xlsx": "^0.18.5", "zod": "^3.22.4"}}