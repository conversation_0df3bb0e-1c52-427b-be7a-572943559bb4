// vite.config.js
import { resolve } from "path";
import { defineConfig, loadEnv } from "vite";
import glob from "glob";
import solidPlugin from "vite-plugin-solid";
import { minify } from "terser";
import { checkEnVariables } from "./src/env.config.ts";

export default defineConfig(({ command, mode }) => {
  const root = resolve(__dirname, "src");
  const outDir = resolve(__dirname, "dist");

  if (command === "build") {
    const env = loadEnv(mode, root, "");
    checkEnVariables(env);
  }

  return {
    root,
    plugins: [solidPlugin()],
    define: {
      "process.env": JSON.stringify(import.meta.env),
    },
    build: {
      target: "esnext",
      polyfillDynamicImport: false,
      minify: false,
      reportCompressedSize: true,
      outDir,
      emptyOutDir: true,
      cssCodeSplit: true,
      rollupOptions: {
        input: [...glob.sync(resolve(__dirname, "src/**/*.{html,js,css}"))],
        preserveEntrySignatures: true,
        output: {
          preserveModules: true,
          preserveModulesRoot: "src",
          entryFileNames: ({ name: fileName }) => {
            return `${fileName}.js`;
          },
        }, // Use the custom Terser plugin for minification
        plugins: [skipMinify],
      },
    },
  };
});
const skipMinify = {
  name: "skipMinify",
  async renderChunk(code) {
    // Skip minification for files inside the specified folder
    if (this.id && !this.id.includes("src/assets/js")) {
      const result = await minify(code); // Minify the code
      return result.code;
    }
    return code;
  },
};
